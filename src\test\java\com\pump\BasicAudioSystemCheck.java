package com.pump;

import javax.sound.sampled.*;
import java.awt.Toolkit;
import java.io.ByteArrayInputStream;

/**
 * 基础音频系统检查
 * 从最底层开始诊断音频系统问题
 */
public class BasicAudioSystemCheck {

    public static void main(String[] args) {
        System.out.println("🔍 ========== 基础音频系统诊断 ==========");
        System.out.println("📢 从最底层开始检查音频系统...");
        System.out.println();

        // 1. 检查Java音频系统是否可用
        checkJavaAudioSystem();
        
        // 2. 测试最基础的系统蜂鸣
        testBasicSystemBeep();
        
        // 3. 生成并播放纯音频信号
        testGeneratedTone();
        
        // 4. 检查系统音频属性
        checkSystemAudioProperties();
        
        // 5. 测试不同频率的音频
        testDifferentFrequencies();
        
        System.out.println("🔍 ========== 诊断完成 ==========");
        System.out.println("📋 如果以上所有测试都没有声音，可能是：");
        System.out.println("   1. 系统音频驱动问题");
        System.out.println("   2. Java音频子系统被禁用");
        System.out.println("   3. 音频设备硬件问题");
        System.out.println("   4. 操作系统音频服务问题");
    }

    /**
     * 检查Java音频系统基础功能
     */
    private static void checkJavaAudioSystem() {
        System.out.println("🔧 === Java音频系统检查 ===");
        
        try {
            // 检查AudioSystem是否可用
            System.out.println("📋 检查AudioSystem可用性...");
            
            // 获取音频设备数量
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            System.out.println("✅ 音频设备数量: " + mixers.length);
            
            if (mixers.length == 0) {
                System.out.println("❌ 严重问题：系统中没有检测到任何音频设备！");
                return;
            }
            
            // 检查默认音频格式支持
            AudioFormat testFormat = new AudioFormat(44100, 16, 2, true, false);
            DataLine.Info info = new DataLine.Info(Clip.class, testFormat);
            
            if (AudioSystem.isLineSupported(info)) {
                System.out.println("✅ 系统支持标准音频格式");
            } else {
                System.out.println("⚠️ 系统不支持标准音频格式");
            }
            
            // 检查每个设备的基本信息
            for (int i = 0; i < Math.min(mixers.length, 5); i++) {
                Mixer.Info mixerInfo = mixers[i];
                System.out.println("🎧 设备 " + (i+1) + ": " + mixerInfo.getName());
                
                try {
                    Mixer mixer = AudioSystem.getMixer(mixerInfo);
                    System.out.println("   状态: " + (mixer.isOpen() ? "已打开" : "未打开"));
                    System.out.println("   输出线路: " + mixer.getSourceLineInfo().length);
                    System.out.println("   输入线路: " + mixer.getTargetLineInfo().length);
                } catch (Exception e) {
                    System.out.println("   ❌ 设备访问失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ Java音频系统检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试最基础的系统蜂鸣
     */
    private static void testBasicSystemBeep() {
        System.out.println("\n📢 === 基础系统蜂鸣测试 ===");
        
        try {
            System.out.println("🔊 测试Toolkit.beep()功能...");
            
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            
            // 测试单次蜂鸣
            System.out.println("🔔 单次蜂鸣测试 - 请注意听！");
            toolkit.beep();
            Thread.sleep(1000);
            
            // 测试连续蜂鸣
            System.out.println("🔔 连续蜂鸣测试 (5次) - 请注意听！");
            for (int i = 0; i < 5; i++) {
                System.out.print("蜂鸣 " + (i+1) + " ");
                toolkit.beep();
                Thread.sleep(300);
            }
            System.out.println();
            
            // 测试快速蜂鸣
            System.out.println("🔔 快速蜂鸣测试 (10次) - 请注意听！");
            for (int i = 0; i < 10; i++) {
                toolkit.beep();
                Thread.sleep(100);
            }
            
            System.out.println("✅ 系统蜂鸣测试完成");
            
        } catch (Exception e) {
            System.out.println("❌ 系统蜂鸣测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 生成并播放纯音频信号
     */
    private static void testGeneratedTone() {
        System.out.println("\n🎵 === 生成音频信号测试 ===");
        
        try {
            // 生成440Hz的正弦波 (标准A音)
            int sampleRate = 44100;
            int duration = 1; // 1秒
            double frequency = 440.0; // A4音符
            
            System.out.println("🎼 生成 " + frequency + "Hz 正弦波信号...");
            
            byte[] audioData = generateSineWave(frequency, duration, sampleRate);
            
            // 创建音频格式
            AudioFormat format = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                sampleRate,
                16,
                1, // 单声道
                2,
                sampleRate,
                false
            );
            
            System.out.println("📊 音频格式: " + format);
            
            // 创建音频输入流
            ByteArrayInputStream byteStream = new ByteArrayInputStream(audioData);
            AudioInputStream audioStream = new AudioInputStream(byteStream, format, audioData.length / format.getFrameSize());
            
            // 播放生成的音频
            System.out.println("🔊 播放生成的音频信号 - 请注意听！");
            playAudioStream(audioStream);
            
        } catch (Exception e) {
            System.out.println("❌ 生成音频信号测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 生成正弦波音频数据
     */
    private static byte[] generateSineWave(double frequency, int durationSeconds, int sampleRate) {
        int numSamples = durationSeconds * sampleRate;
        byte[] audioData = new byte[numSamples * 2]; // 16位 = 2字节
        
        for (int i = 0; i < numSamples; i++) {
            double time = (double) i / sampleRate;
            double amplitude = 0.5; // 50%音量
            short sample = (short) (amplitude * Short.MAX_VALUE * Math.sin(2 * Math.PI * frequency * time));
            
            // 转换为字节 (小端序)
            audioData[i * 2] = (byte) (sample & 0xFF);
            audioData[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
        }
        
        return audioData;
    }

    /**
     * 播放音频流
     */
    private static void playAudioStream(AudioInputStream audioStream) {
        Clip clip = null;
        
        try {
            clip = AudioSystem.getClip();
            clip.open(audioStream);
            
            // 设置音量到最大
            if (clip.isControlSupported(FloatControl.Type.MASTER_GAIN)) {
                FloatControl gainControl = (FloatControl) clip.getControl(FloatControl.Type.MASTER_GAIN);
                gainControl.setValue(gainControl.getMaximum());
                System.out.println("🔊 音量设置到最大: " + gainControl.getValue() + " dB");
            }
            
            // 播放
            clip.start();
            
            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(50);
            }
            
            System.out.println("✅ 生成音频播放完成");
            
        } catch (Exception e) {
            System.out.println("❌ 音频流播放失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (clip != null) {
                clip.close();
            }
        }
    }

    /**
     * 检查系统音频属性
     */
    private static void checkSystemAudioProperties() {
        System.out.println("\n🔍 === 系统音频属性检查 ===");
        
        try {
            // 检查系统属性
            System.out.println("📋 Java音频相关系统属性:");
            
            String[] audioProperties = {
                "java.version",
                "java.vendor",
                "os.name",
                "os.version",
                "os.arch",
                "javax.sound.sampled.Clip",
                "javax.sound.sampled.Port",
                "javax.sound.sampled.SourceDataLine",
                "javax.sound.sampled.TargetDataLine"
            };
            
            for (String prop : audioProperties) {
                String value = System.getProperty(prop);
                if (value != null) {
                    System.out.println("   " + prop + " = " + value);
                }
            }
            
            // 检查可用的音频格式
            System.out.println("\n📊 检查支持的音频格式:");
            AudioFormat[] commonFormats = {
                new AudioFormat(44100, 16, 2, true, false),
                new AudioFormat(44100, 16, 1, true, false),
                new AudioFormat(22050, 16, 2, true, false),
                new AudioFormat(8000, 8, 1, true, false)
            };
            
            for (AudioFormat format : commonFormats) {
                DataLine.Info info = new DataLine.Info(Clip.class, format);
                boolean supported = AudioSystem.isLineSupported(info);
                System.out.println("   " + format + " : " + (supported ? "✅ 支持" : "❌ 不支持"));
            }
            
        } catch (Exception e) {
            System.out.println("❌ 系统音频属性检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试不同频率的音频
     */
    private static void testDifferentFrequencies() {
        System.out.println("\n🎼 === 不同频率音频测试 ===");
        
        double[] frequencies = {220.0, 440.0, 880.0, 1000.0}; // 不同频率
        
        for (double freq : frequencies) {
            try {
                System.out.println("🎵 测试频率: " + freq + "Hz - 请注意听！");
                
                byte[] audioData = generateSineWave(freq, 1, 44100);
                
                AudioFormat format = new AudioFormat(
                    AudioFormat.Encoding.PCM_SIGNED,
                    44100, 16, 1, 2, 44100, false
                );
                
                ByteArrayInputStream byteStream = new ByteArrayInputStream(audioData);
                AudioInputStream audioStream = new AudioInputStream(byteStream, format, audioData.length / format.getFrameSize());
                
                playAudioStream(audioStream);
                
                Thread.sleep(500); // 频率间间隔
                
            } catch (Exception e) {
                System.out.println("❌ 频率 " + freq + "Hz 测试失败: " + e.getMessage());
            }
        }
        
        System.out.println("✅ 不同频率测试完成");
    }
}
