package com.pump;

import javax.sound.sampled.*;
import java.io.ByteArrayInputStream;

/**
 * 音频修复尝试工具
 * 基于诊断结果，尝试各种修复方案
 */
public class AudioFixAttempt {

    public static void main(String[] args) {
        System.out.println("🔧 ========== 音频修复尝试 ==========");
        System.out.println("📢 基于诊断结果，尝试各种修复方案...");
        System.out.println();

        // 1. 强制使用Realtek音频设备
        tryRealtekDevice();
        
        // 2. 尝试使用SourceDataLine而不是Clip
        trySourceDataLine();
        
        // 3. 尝试不同的音频格式
        tryDifferentFormats();
        
        // 4. 尝试强制刷新音频系统
        tryRefreshAudioSystem();
        
        System.out.println("🔧 ========== 修复尝试完成 ==========");
        System.out.println("💡 如果仍然没有声音，请按照以下步骤操作：");
        System.out.println("   1. 右键点击任务栏音量图标");
        System.out.println("   2. 选择'打开音量混合器'");
        System.out.println("   3. 查找Java相关的音量控制");
        System.out.println("   4. 确保Java音量没有被静音或设为0");
        System.out.println("   5. 在'声音设置'中确认默认播放设备");
    }

    /**
     * 强制使用Realtek音频设备
     */
    private static void tryRealtekDevice() {
        System.out.println("🎧 === 尝试强制使用Realtek设备 ===");
        
        try {
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            Mixer realtekMixer = null;
            
            // 查找Realtek设备
            for (Mixer.Info info : mixers) {
                String name = info.getName();
                System.out.println("🔍 检查设备: " + name);
                
                if (name.contains("Realtek") || name.contains("扬声器") || name.contains("Speaker")) {
                    System.out.println("✅ 找到Realtek相关设备: " + name);
                    
                    try {
                        realtekMixer = AudioSystem.getMixer(info);
                        Line.Info[] sourceLines = realtekMixer.getSourceLineInfo();
                        
                        if (sourceLines.length > 0) {
                            System.out.println("🔊 设备支持音频输出，尝试播放...");
                            playTestAudioOnMixer(realtekMixer, name);
                        }
                        
                    } catch (Exception e) {
                        System.out.println("❌ Realtek设备测试失败: " + e.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ Realtek设备尝试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 尝试使用SourceDataLine而不是Clip
     */
    private static void trySourceDataLine() {
        System.out.println("\n🎵 === 尝试SourceDataLine播放 ===");
        
        try {
            // 生成测试音频数据
            byte[] audioData = generateTestTone(440.0, 2, 44100);
            
            AudioFormat format = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                44100, 16, 1, 2, 44100, false
            );
            
            System.out.println("📊 使用格式: " + format);
            
            // 尝试所有可能的设备
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            
            for (Mixer.Info mixerInfo : mixers) {
                try {
                    Mixer mixer = AudioSystem.getMixer(mixerInfo);
                    DataLine.Info info = new DataLine.Info(SourceDataLine.class, format);
                    
                    if (mixer.isLineSupported(info)) {
                        System.out.println("🎧 尝试设备: " + mixerInfo.getName());
                        
                        SourceDataLine line = (SourceDataLine) mixer.getLine(info);
                        line.open(format);
                        
                        // 设置缓冲区大小
                        int bufferSize = line.getBufferSize();
                        System.out.println("📊 缓冲区大小: " + bufferSize + " bytes");
                        
                        line.start();
                        
                        System.out.println("🔊 播放测试音频 - 请注意听！");
                        
                        // 分块写入音频数据
                        int chunkSize = bufferSize / 4;
                        int offset = 0;
                        
                        while (offset < audioData.length) {
                            int bytesToWrite = Math.min(chunkSize, audioData.length - offset);
                            int bytesWritten = line.write(audioData, offset, bytesToWrite);
                            offset += bytesWritten;
                            
                            // 显示进度
                            if (offset % (audioData.length / 10) == 0) {
                                System.out.print(".");
                            }
                        }
                        
                        System.out.println();
                        
                        // 等待播放完成
                        line.drain();
                        line.stop();
                        line.close();
                        
                        System.out.println("✅ SourceDataLine播放完成: " + mixerInfo.getName());
                        
                        // 只测试第一个成功的设备
                        break;
                        
                    }
                    
                } catch (Exception e) {
                    System.out.println("⚠️ 设备 " + mixerInfo.getName() + " 测试失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ SourceDataLine测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 尝试不同的音频格式
     */
    private static void tryDifferentFormats() {
        System.out.println("\n📊 === 尝试不同音频格式 ===");
        
        AudioFormat[] formats = {
            // 标准格式
            new AudioFormat(44100, 16, 2, true, false),
            new AudioFormat(44100, 16, 1, true, false),
            // 低质量格式
            new AudioFormat(22050, 16, 1, true, false),
            new AudioFormat(11025, 8, 1, true, false),
            // 高质量格式
            new AudioFormat(48000, 16, 2, true, false),
            // 大端序格式
            new AudioFormat(44100, 16, 1, true, true)
        };
        
        for (int i = 0; i < formats.length; i++) {
            AudioFormat format = formats[i];
            System.out.println("🎵 测试格式 " + (i+1) + ": " + format);
            
            try {
                DataLine.Info info = new DataLine.Info(Clip.class, format);
                
                if (AudioSystem.isLineSupported(info)) {
                    System.out.println("✅ 格式支持，尝试播放...");
                    
                    // 生成适合该格式的音频数据
                    byte[] audioData = generateTestToneForFormat(440.0, 1, format);
                    
                    ByteArrayInputStream byteStream = new ByteArrayInputStream(audioData);
                    AudioInputStream audioStream = new AudioInputStream(
                        byteStream, format, audioData.length / format.getFrameSize()
                    );
                    
                    Clip clip = AudioSystem.getClip();
                    clip.open(audioStream);
                    
                    // 设置音量
                    if (clip.isControlSupported(FloatControl.Type.MASTER_GAIN)) {
                        FloatControl gainControl = (FloatControl) clip.getControl(FloatControl.Type.MASTER_GAIN);
                        gainControl.setValue(gainControl.getMaximum());
                    }
                    
                    System.out.println("🔊 播放格式测试 - 请注意听！");
                    clip.start();
                    
                    while (clip.isRunning()) {
                        Thread.sleep(50);
                    }
                    
                    clip.close();
                    System.out.println("✅ 格式测试完成");
                    
                } else {
                    System.out.println("❌ 格式不支持");
                }
                
            } catch (Exception e) {
                System.out.println("❌ 格式测试失败: " + e.getMessage());
            }
            
            System.out.println();
        }
    }

    /**
     * 尝试强制刷新音频系统
     */
    private static void tryRefreshAudioSystem() {
        System.out.println("🔄 === 尝试刷新音频系统 ===");

        try {
            System.out.println("📋 当前音频设备:");
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();

            for (int i = 0; i < mixers.length; i++) {
                Mixer.Info info = mixers[i];
                System.out.println("   " + (i+1) + ". " + info.getName());

                try {
                    Mixer mixer = AudioSystem.getMixer(info);

                    // 简单检查设备状态
                    Line.Info[] sourceLines = mixer.getSourceLineInfo();
                    Line.Info[] targetLines = mixer.getTargetLineInfo();

                    System.out.println("      � 输出线路: " + sourceLines.length);
                    System.out.println("      📊 输入线路: " + targetLines.length);
                    System.out.println("      📊 设备状态: " + (mixer.isOpen() ? "已打开" : "未打开"));

                    // 检查设备是否支持标准格式
                    if (sourceLines.length > 0) {
                        try {
                            AudioFormat testFormat = new AudioFormat(44100, 16, 1, true, false);
                            DataLine.Info testInfo = new DataLine.Info(Clip.class, testFormat);

                            if (mixer.isLineSupported(testInfo)) {
                                System.out.println("      ✅ 设备支持标准音频格式");
                            } else {
                                System.out.println("      ⚠️ 设备不支持标准音频格式");
                            }
                        } catch (Exception e) {
                            System.out.println("      ⚠️ 格式检查失败: " + e.getMessage());
                        }
                    }

                } catch (Exception e) {
                    System.out.println("      ❌ 设备访问失败: " + e.getMessage());
                }
            }

            System.out.println("✅ 音频系统检查完成");

        } catch (Exception e) {
            System.out.println("❌ 音频系统检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 在指定混合器上播放测试音频
     */
    private static void playTestAudioOnMixer(Mixer mixer, String deviceName) {
        try {
            byte[] audioData = generateTestTone(440.0, 1, 44100);
            
            AudioFormat format = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                44100, 16, 1, 2, 44100, false
            );
            
            DataLine.Info info = new DataLine.Info(Clip.class, format);
            
            if (mixer.isLineSupported(info)) {
                Clip clip = (Clip) mixer.getLine(info);
                
                ByteArrayInputStream byteStream = new ByteArrayInputStream(audioData);
                AudioInputStream audioStream = new AudioInputStream(
                    byteStream, format, audioData.length / format.getFrameSize()
                );
                
                clip.open(audioStream);
                
                System.out.println("🔊 在设备 [" + deviceName + "] 上播放 - 请注意听！");
                clip.start();
                
                while (clip.isRunning()) {
                    Thread.sleep(50);
                }
                
                clip.close();
                System.out.println("✅ 设备播放完成");
                
            } else {
                System.out.println("❌ 设备不支持Clip播放");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 设备播放失败: " + e.getMessage());
        }
    }

    /**
     * 生成测试音调
     */
    private static byte[] generateTestTone(double frequency, int durationSeconds, int sampleRate) {
        int numSamples = durationSeconds * sampleRate;
        byte[] audioData = new byte[numSamples * 2];
        
        for (int i = 0; i < numSamples; i++) {
            double time = (double) i / sampleRate;
            short sample = (short) (0.7 * Short.MAX_VALUE * Math.sin(2 * Math.PI * frequency * time));
            
            audioData[i * 2] = (byte) (sample & 0xFF);
            audioData[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
        }
        
        return audioData;
    }

    /**
     * 为特定格式生成测试音调
     */
    private static byte[] generateTestToneForFormat(double frequency, int durationSeconds, AudioFormat format) {
        int sampleRate = (int) format.getSampleRate();
        int sampleSizeInBits = format.getSampleSizeInBits();
        int channels = format.getChannels();
        boolean signed = format.getEncoding() == AudioFormat.Encoding.PCM_SIGNED;
        boolean bigEndian = format.isBigEndian();
        
        int numSamples = durationSeconds * sampleRate;
        int bytesPerSample = sampleSizeInBits / 8;
        byte[] audioData = new byte[numSamples * bytesPerSample * channels];
        
        for (int i = 0; i < numSamples; i++) {
            double time = (double) i / sampleRate;
            double amplitude = 0.7;
            
            for (int ch = 0; ch < channels; ch++) {
                if (sampleSizeInBits == 16) {
                    short sample = (short) (amplitude * Short.MAX_VALUE * Math.sin(2 * Math.PI * frequency * time));
                    
                    int offset = (i * channels + ch) * 2;
                    if (bigEndian) {
                        audioData[offset] = (byte) ((sample >> 8) & 0xFF);
                        audioData[offset + 1] = (byte) (sample & 0xFF);
                    } else {
                        audioData[offset] = (byte) (sample & 0xFF);
                        audioData[offset + 1] = (byte) ((sample >> 8) & 0xFF);
                    }
                } else if (sampleSizeInBits == 8) {
                    byte sample;
                    if (signed) {
                        sample = (byte) (amplitude * Byte.MAX_VALUE * Math.sin(2 * Math.PI * frequency * time));
                    } else {
                        sample = (byte) (128 + amplitude * 127 * Math.sin(2 * Math.PI * frequency * time));
                    }
                    
                    audioData[i * channels + ch] = sample;
                }
            }
        }
        
        return audioData;
    }
}
