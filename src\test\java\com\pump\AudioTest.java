package com.pump;

import javax.sound.sampled.*;
import java.io.BufferedInputStream;
import java.io.InputStream;

/**
 * 音频功能测试类
 * 用于验证JAR包内音频资源是否能正常播放
 */
public class AudioTest {
    
    public static void main(String[] args) {
        System.out.println("🎵 开始音频功能测试...");
        
        // 测试up.wav
        System.out.println("📢 测试买入告警音频 (up.wav)...");
        testAudio("up.wav");
        
        try {
            Thread.sleep(2000); // 等待2秒
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        
        // 测试down.wav
        System.out.println("📢 测试卖出告警音频 (down.wav)...");
        testAudio("down.wav");
        
        System.out.println("✅ 音频功能测试完成");
    }
    
    private static void testAudio(String audioFileName) {
        try {
            // 尝试多个路径
            String[] paths = {
                "/audio/" + audioFileName,
                "/sounds/" + audioFileName,
                "/" + audioFileName
            };
            
            InputStream audioStream = null;
            String successPath = null;
            
            for (String path : paths) {
                InputStream rawStream = AudioTest.class.getResourceAsStream(path);
                if (rawStream != null) {
                    audioStream = rawStream.markSupported() ? 
                        rawStream : new BufferedInputStream(rawStream, 8192);
                    successPath = path;
                    break;
                }
            }
            
            if (audioStream == null) {
                System.err.println("❌ 无法找到音频文件: " + audioFileName);
                return;
            }
            
            System.out.println("✅ 成功加载音频文件: " + successPath);
            
            // 创建AudioInputStream
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            System.out.println("✅ 成功创建AudioInputStream");
            
            // 播放音频
            Clip clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            System.out.println("✅ 成功打开音频剪辑");
            
            clip.start();
            System.out.println("🔊 开始播放音频: " + audioFileName);
            
            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(100);
            }
            
            // 清理资源
            clip.close();
            audioInputStream.close();
            audioStream.close();
            
            System.out.println("✅ 音频播放完成: " + audioFileName);
            
        } catch (Exception e) {
            System.err.println("❌ 音频播放失败: " + audioFileName);
            e.printStackTrace();
        }
    }
}
