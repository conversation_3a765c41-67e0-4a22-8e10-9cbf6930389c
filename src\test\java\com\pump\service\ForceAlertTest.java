package com.pump.service;

import com.pump.config.PumpConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import java.math.BigDecimal;

/**
 * 强制触发告警测试
 * 用于验证告警功能是否正常工作
 */
@SpringBootApplication
@ComponentScan(basePackages = "com.pump")
public class ForceAlertTest implements CommandLineRunner {
    
    @Autowired
    private AlertSoundService alertSoundService;
    
    @Autowired
    private PumpConfigService configService;
    
    public static void main(String[] args) {
        SpringApplication.run(ForceAlertTest.class, args);
    }
    
    @Override
    public void run(String... args) throws Exception {
        System.out.println("=== 强制告警测试开始 ===");
        
        // 显示配置信息
        System.out.println("配置信息:");
        System.out.println("- 告警启用: " + configService.isAlertEnabled());
        System.out.println("- 买入阈值: $" + configService.getBuyThreshold());
        System.out.println("- 卖出阈值: $" + configService.getSellThreshold());
        System.out.println("- 音频类型: " + configService.getSoundType());
        
        // 检查AlertSoundService是否正确注入
        if (alertSoundService == null) {
            System.out.println("❌ AlertSoundService为null，依赖注入失败！");
            return;
        } else {
            System.out.println("✅ AlertSoundService注入成功");
        }
        
        // 测试1: 强制触发买入告警
        System.out.println("\n=== 测试1: 强制触发买入告警 ===");
        BigDecimal buyDifference = new BigDecimal("5.00"); // 远超过1.00阈值
        BigDecimal jupiterBuyPrice = new BigDecimal("6700.00");
        BigDecimal gatePrice = new BigDecimal("6705.00");
        
        System.out.printf("买入差价: $%.2f (Gate: $%.2f - Jupiter: $%.2f)%n", 
            buyDifference, gatePrice, jupiterBuyPrice);
        System.out.println("预期: 应该触发买入告警，播放up.wav");
        
        try {
            alertSoundService.checkBuyAlert(buyDifference, jupiterBuyPrice, gatePrice);
            System.out.println("✅ 买入告警调用完成");
        } catch (Exception e) {
            System.out.println("❌ 买入告警调用失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 等待音频播放完成
        Thread.sleep(3000);
        
        // 测试2: 强制触发卖出告警
        System.out.println("\n=== 测试2: 强制触发卖出告警 ===");
        BigDecimal sellDifference = new BigDecimal("3.50"); // 远超过1.00阈值
        BigDecimal jupiterSellPrice = new BigDecimal("6708.50");
        
        System.out.printf("卖出差价: $%.2f (Jupiter: $%.2f - Gate: $%.2f)%n", 
            sellDifference, jupiterSellPrice, gatePrice);
        System.out.println("预期: 应该触发卖出告警，播放down.wav");
        
        try {
            alertSoundService.checkSellAlert(sellDifference, jupiterSellPrice, gatePrice);
            System.out.println("✅ 卖出告警调用完成");
        } catch (Exception e) {
            System.out.println("❌ 卖出告警调用失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 等待音频播放完成
        Thread.sleep(3000);
        
        // 测试3: 差价不足（不应该触发告警）
        System.out.println("\n=== 测试3: 差价不足（不应该触发告警）===");
        BigDecimal lowDifference = new BigDecimal("0.50"); // 低于1.00阈值
        
        System.out.printf("买入差价: $%.2f (低于阈值 $%.2f)%n", 
            lowDifference, configService.getBuyThreshold());
        System.out.println("预期: 不应该触发告警");
        
        try {
            alertSoundService.checkBuyAlert(lowDifference, jupiterBuyPrice, gatePrice);
            System.out.println("✅ 低差价测试完成（应该没有声音）");
        } catch (Exception e) {
            System.out.println("❌ 低差价测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n=== 强制告警测试完成 ===");
        System.out.println("如果听到了音频，说明告警功能正常");
        System.out.println("如果没有听到音频，请检查音频设备和配置");
        
        // 退出应用
        System.exit(0);
    }
}
