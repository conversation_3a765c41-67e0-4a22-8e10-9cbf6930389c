package com.pump.service.impl;

import com.pump.client.GateIoApiClient;
import com.pump.client.JupiterApiClientFixed;
import com.pump.client.JupiterUltraApiClient;
import com.pump.model.PriceData;
import com.pump.service.PumpPriceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * PUMP价格服务实现类
 * 整合Gate.io和Jupiter API客户端，提供统一的价格服务
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
@Service
public class PumpPriceServiceImpl implements PumpPriceService {
    
    private static final Logger logger = LoggerFactory.getLogger(PumpPriceServiceImpl.class);
    
    @Autowired
    private GateIoApiClient gateIoApiClient;

    @Autowired
    private JupiterApiClientFixed jupiterApiClient;

    @Autowired
    private JupiterUltraApiClient jupiterUltraApiClient;
    
    /**
     * 获取CEX (Gate.io) 价格数据
     * 
     * @return 价格数据
     */
    @Override
    public PriceData getCexPrice() {
        logger.debug("获取CEX价格数据");
        
        try {
            PriceData cexPrice = gateIoApiClient.getPumpPrice();
            
            if (cexPrice.isValid()) {
                logger.debug("CEX价格获取成功: {}", cexPrice);
            } else {
                logger.warn("CEX价格获取失败: {}", cexPrice.getErrorMessage());
            }
            
            return cexPrice;
            
        } catch (Exception e) {
            logger.error("获取CEX价格时发生异常", e);
            PriceData errorData = new PriceData();
            errorData.setExchange("Gate.io");
            errorData.setErrorMessage("获取CEX价格异常: " + e.getMessage());
            return errorData;
        }
    }
    
    /**
     * 获取DEX (Jupiter) 价格数据
     * 
     * @return 价格数据
     */
    @Override
    public PriceData getDexPrice() {
        logger.debug("获取DEX价格数据");
        
        try {
            PriceData dexPrice = jupiterApiClient.getPumpPrice();
            
            if (dexPrice.isValid()) {
                logger.debug("DEX价格获取成功: {}", dexPrice);
            } else {
                logger.warn("DEX价格获取失败: {}", dexPrice.getErrorMessage());
            }
            
            return dexPrice;
            
        } catch (Exception e) {
            logger.error("获取DEX价格时发生异常", e);
            PriceData errorData = new PriceData();
            errorData.setExchange("Jupiter");
            errorData.setErrorMessage("获取DEX价格异常: " + e.getMessage());
            return errorData;
        }
    }
    
    /**
     * 获取指定数量的买入价格
     * 
     * @param amount 购买数量
     * @param exchange 交易所类型 (CEX/DEX)
     * @return 买入价格
     */
    @Override
    public BigDecimal getBuyPrice(BigDecimal amount, String exchange) {
        logger.debug("获取买入价格，数量: {}, 交易所: {}", amount, exchange);
        
        try {
            if ("CEX".equalsIgnoreCase(exchange)) {
                // 尝试获取Gate.io的订单簿价格
                BigDecimal orderBookPrice = gateIoApiClient.getOrderBookPrice(amount, true);
                if (orderBookPrice != null) {
                    return orderBookPrice;
                }
                
                // 如果订单簿价格获取失败，使用ticker价格
                PriceData cexPrice = getCexPrice();
                if (cexPrice.isValid() && cexPrice.getBuyPrice() != null) {
                    return cexPrice.getBuyPrice();
                }
                
            } else if ("DEX".equalsIgnoreCase(exchange)) {
                // 对于DEX，优先使用Ultra API获取准确的买入价格
                logger.debug("尝试使用Jupiter Ultra API获取买入价格（基于$6000）");
                BigDecimal ultraUnitPrice = jupiterUltraApiClient.getUltraQuotePrice(amount, true);
                if (ultraUnitPrice != null) {
                    // 计算总价：单价 * 数量
                    BigDecimal totalPrice = ultraUnitPrice.multiply(amount);
                    logger.debug("Jupiter Ultra API买入单价: {} USDT/PUMP (基于$6000), 100万个PUMP总价: {} USDT",
                                ultraUnitPrice, totalPrice);
                    return totalPrice;
                }

                // 备选：使用Quote API获取买入价格
                logger.debug("Ultra API失败，尝试使用Jupiter Quote API获取买入价格（基于$6000）");
                BigDecimal unitPrice = jupiterApiClient.getQuotePrice(amount, true);
                if (unitPrice != null) {
                    // 计算总价：单价 * 数量
                    BigDecimal totalPrice = unitPrice.multiply(amount);
                    logger.debug("Jupiter Quote API买入单价: {} USDT/PUMP (基于$6000), 100万个PUMP总价: {} USDT",
                                unitPrice, totalPrice);
                    return totalPrice;
                }

                // 备选：使用基础价格
                PriceData dexPrice = getDexPrice();
                if (dexPrice.isValid() && dexPrice.getBuyPrice() != null) {
                    BigDecimal totalPrice = dexPrice.getBuyPrice().multiply(amount);
                    logger.debug("使用DEX基础买入单价: {} USDT/PUMP, 总价: {} USDT",
                                dexPrice.getBuyPrice(), totalPrice);
                    return totalPrice;
                }
            }
            
            logger.warn("无法获取买入价格，交易所: {}", exchange);
            return null;
            
        } catch (Exception e) {
            logger.error("获取买入价格时发生异常", e);
            return null;
        }
    }
    
    /**
     * 获取指定数量的卖出价格
     * 
     * @param amount 卖出数量
     * @param exchange 交易所类型 (CEX/DEX)
     * @return 卖出价格
     */
    @Override
    public BigDecimal getSellPrice(BigDecimal amount, String exchange) {
        logger.debug("获取卖出价格，数量: {}, 交易所: {}", amount, exchange);
        
        try {
            if ("CEX".equalsIgnoreCase(exchange)) {
                // 尝试获取Gate.io的订单簿价格
                BigDecimal orderBookPrice = gateIoApiClient.getOrderBookPrice(amount, false);
                if (orderBookPrice != null) {
                    return orderBookPrice;
                }
                
                // 如果订单簿价格获取失败，使用ticker价格
                PriceData cexPrice = getCexPrice();
                if (cexPrice.isValid() && cexPrice.getSellPrice() != null) {
                    return cexPrice.getSellPrice();
                }
                
            } else if ("DEX".equalsIgnoreCase(exchange)) {
                // 对于DEX，优先使用Ultra API获取准确的卖出价格
                logger.debug("尝试使用Jupiter Ultra API获取卖出价格，数量: {}", amount);
                BigDecimal ultraUnitPrice = jupiterUltraApiClient.getUltraQuotePrice(amount, false);
                if (ultraUnitPrice != null) {
                    // 计算总价：单价 * 数量
                    BigDecimal totalPrice = ultraUnitPrice.multiply(amount);
                    logger.debug("Jupiter Ultra API卖出单价: {} USDT/PUMP, 总价: {} USDT", ultraUnitPrice, totalPrice);
                    return totalPrice;
                }

                // 备选：使用Quote API获取卖出价格
                logger.debug("Ultra API失败，尝试使用Jupiter Quote API获取卖出价格，数量: {}", amount);
                BigDecimal unitPrice = jupiterApiClient.getQuotePrice(amount, false);
                if (unitPrice != null) {
                    // 计算总价：单价 * 数量
                    BigDecimal totalPrice = unitPrice.multiply(amount);
                    logger.debug("Jupiter Quote API卖出单价: {} USDT/PUMP, 总价: {} USDT", unitPrice, totalPrice);
                    return totalPrice;
                }

                // 备选：使用基础价格
                PriceData dexPrice = getDexPrice();
                if (dexPrice.isValid() && dexPrice.getSellPrice() != null) {
                    BigDecimal totalPrice = dexPrice.getSellPrice().multiply(amount);
                    logger.debug("使用DEX基础卖出单价: {} USDT/PUMP, 总价: {} USDT",
                                dexPrice.getSellPrice(), totalPrice);
                    return totalPrice;
                }
            }
            
            logger.warn("无法获取卖出价格，交易所: {}", exchange);
            return null;
            
        } catch (Exception e) {
            logger.error("获取卖出价格时发生异常", e);
            return null;
        }
    }
    
    /**
     * 检查服务健康状态
     * 
     * @return 服务是否正常
     */
    @Override
    public boolean isHealthy() {
        logger.debug("检查服务健康状态");
        
        try {
            boolean cexHealthy = gateIoApiClient.isApiHealthy();
            boolean dexHealthy = jupiterApiClient.isApiHealthy();
            
            logger.debug("CEX健康状态: {}, DEX健康状态: {}", cexHealthy, dexHealthy);
            
            // 至少有一个API正常就认为服务健康
            return cexHealthy || dexHealthy;
            
        } catch (Exception e) {
            logger.error("检查服务健康状态时发生异常", e);
            return false;
        }
    }
} 