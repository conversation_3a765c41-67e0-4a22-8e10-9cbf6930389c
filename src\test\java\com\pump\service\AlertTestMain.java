package com.pump.service;

import com.pump.config.PumpConfigService;
import java.math.BigDecimal;

/**
 * 告警功能测试程序
 * 用于测试告警触发逻辑
 */
public class AlertTestMain {
    
    public static void main(String[] args) {
        System.out.println("=== 告警功能测试 ===");
        
        // 创建模拟的配置服务
        MockPumpConfigService configService = new MockPumpConfigService();
        
        // 不创建AlertSoundService，直接测试告警逻辑
        
        System.out.println("配置信息:");
        System.out.println("- 告警启用: " + configService.isAlertEnabled());
        System.out.println("- 买入阈值: $" + configService.getBuyThreshold());
        System.out.println("- 卖出阈值: $" + configService.getSellThreshold());
        System.out.println("- 音频类型: " + configService.getSoundType());
        System.out.println("- 连续播放: " + configService.isContinuousPlayEnabled());
        
        // 测试1: 买入告警（差价超过阈值）
        System.out.println("\n=== 测试1: 买入告警（差价超过阈值）===");
        BigDecimal buyDifference = new BigDecimal("2.19"); // 超过1.00阈值
        BigDecimal jupiterBuyPrice = new BigDecimal("6697.81");
        BigDecimal gatePrice = new BigDecimal("6700.00");
        
        System.out.printf("买入差价: $%.2f (Gate: $%.2f - Jupiter: $%.2f)%n", 
            buyDifference, gatePrice, jupiterBuyPrice);
        System.out.println("预期: 应该触发买入告警，播放up.wav");
        
        try {
            // 这里需要手动调用，因为没有Spring依赖注入
            testBuyAlert(configService, buyDifference, jupiterBuyPrice, gatePrice);
        } catch (Exception e) {
            System.out.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 等待音频播放完成
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试2: 卖出告警（差价超过阈值）
        System.out.println("\n=== 测试2: 卖出告警（差价超过阈值）===");
        BigDecimal sellDifference = new BigDecimal("1.50"); // 超过1.00阈值
        BigDecimal jupiterSellPrice = new BigDecimal("6701.50");
        
        System.out.printf("卖出差价: $%.2f (Jupiter: $%.2f - Gate: $%.2f)%n", 
            sellDifference, jupiterSellPrice, gatePrice);
        System.out.println("预期: 应该触发卖出告警，播放down.wav");
        
        try {
            testSellAlert(configService, sellDifference, jupiterSellPrice, gatePrice);
        } catch (Exception e) {
            System.out.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 等待音频播放完成
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试3: 差价不足（不应该触发告警）
        System.out.println("\n=== 测试3: 差价不足（不应该触发告警）===");
        BigDecimal lowDifference = new BigDecimal("0.50"); // 低于1.00阈值
        
        System.out.printf("买入差价: $%.2f (低于阈值 $%.2f)%n", 
            lowDifference, configService.getBuyThreshold());
        System.out.println("预期: 不应该触发告警");
        
        try {
            testBuyAlert(configService, lowDifference, jupiterBuyPrice, gatePrice);
        } catch (Exception e) {
            System.out.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    private static void testBuyAlert(MockPumpConfigService configService, 
                                   BigDecimal buyDifference, 
                                   BigDecimal jupiterPrice, 
                                   BigDecimal gatePrice) {
        
        // 手动实现告警逻辑测试
        if (!configService.isAlertEnabled() || buyDifference == null) {
            System.out.println("⚠️ 告警已禁用或差价为空");
            return;
        }
        
        // 检查阈值条件
        boolean thresholdMet = buyDifference.compareTo(BigDecimal.ZERO) > 0 &&
                              buyDifference.compareTo(new BigDecimal(configService.getBuyThreshold())) > 0;
        
        if (thresholdMet) {
            System.out.printf("✅ 买入告警条件满足: $%.2f > $%.2f%n", 
                buyDifference, configService.getBuyThreshold());
            System.out.println("🔊 播放买入告警音频 (up.wav)...");
            
            // 直接调用音频播放测试
            playTestAudio("up.wav");
        } else {
            System.out.printf("ℹ️ 买入差价 $%.2f 未超过阈值 $%.2f，不触发告警%n",
                buyDifference, configService.getBuyThreshold());
        }
    }
    
    private static void testSellAlert(MockPumpConfigService configService,
                                    BigDecimal sellDifference,
                                    BigDecimal jupiterPrice,
                                    BigDecimal gatePrice) {
        
        if (!configService.isAlertEnabled() || sellDifference == null) {
            System.out.println("⚠️ 告警已禁用或差价为空");
            return;
        }
        
        // 检查阈值条件
        boolean thresholdMet = sellDifference.compareTo(BigDecimal.ZERO) > 0 &&
                              sellDifference.compareTo(new BigDecimal(configService.getSellThreshold())) > 0;
        
        if (thresholdMet) {
            System.out.printf("✅ 卖出告警条件满足: $%.2f > $%.2f%n", 
                sellDifference, configService.getSellThreshold());
            System.out.println("🔊 播放卖出告警音频 (down.wav)...");
            
            // 直接调用音频播放测试
            playTestAudio("down.wav");
        } else {
            System.out.printf("ℹ️ 卖出差价 $%.2f 未超过阈值 $%.2f，不触发告警%n",
                sellDifference, configService.getSellThreshold());
        }
    }
    
    private static void playTestAudio(String audioFileName) {
        try {
            // 使用之前测试过的音频播放代码
            java.io.InputStream rawStream = loadAudioResourceSafely(audioFileName);
            if (rawStream == null) {
                System.out.println("❌ 无法加载音频文件: " + audioFileName);
                return;
            }
            
            // 确保支持mark/reset
            java.io.InputStream audioStream = rawStream.markSupported() ? 
                rawStream : new java.io.BufferedInputStream(rawStream, 8192);
            
            // 创建AudioInputStream
            javax.sound.sampled.AudioInputStream audioInputStream = 
                javax.sound.sampled.AudioSystem.getAudioInputStream(audioStream);
            
            // 播放音频
            javax.sound.sampled.Clip clip = javax.sound.sampled.AudioSystem.getClip();
            clip.open(audioInputStream);
            clip.start();
            
            System.out.println("🎵 开始播放音频: " + audioFileName);
            
            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(100);
            }
            
            // 清理资源
            clip.close();
            audioInputStream.close();
            audioStream.close();
            
            System.out.println("✅ 音频播放完成: " + audioFileName);
            
        } catch (Exception e) {
            System.out.println("❌ 音频播放失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static java.io.InputStream loadAudioResourceSafely(String audioFileName) {
        String[] resourcePaths = {
            "sounds/" + audioFileName,
            audioFileName,
            "/" + audioFileName,
            "/sounds/" + audioFileName
        };
        
        // 尝试Class.getResourceAsStream()
        for (String path : resourcePaths) {
            java.io.InputStream stream = AlertTestMain.class.getResourceAsStream("/" + path);
            if (stream != null) {
                return stream;
            }
        }
        
        // 尝试ClassLoader.getResourceAsStream()
        for (String path : resourcePaths) {
            java.io.InputStream stream = AlertTestMain.class.getClassLoader().getResourceAsStream(path);
            if (stream != null) {
                return stream;
            }
        }
        
        return null;
    }
    
    // 模拟配置服务
    static class MockPumpConfigService {
        public boolean isAlertEnabled() { return true; }
        public double getBuyThreshold() { return 1.00; }
        public double getSellThreshold() { return 1.00; }
        public String getSoundType() { return "CUSTOM"; }
        public boolean isContinuousPlayEnabled() { return true; }
    }
}
