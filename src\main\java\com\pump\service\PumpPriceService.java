package com.pump.service;

import com.pump.model.PriceData;
import java.math.BigDecimal;

/**
 * PUMP价格服务接口
 * 提供统一的价格数据获取接口
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public interface PumpPriceService {
    
    /**
     * 获取CEX (Gate.io) 价格数据
     * 
     * @return 价格数据
     */
    PriceData getCexPrice();
    
    /**
     * 获取DEX (Jupiter) 价格数据
     * 
     * @return 价格数据
     */
    PriceData getDexPrice();
    
    /**
     * 获取指定数量的买入价格
     * 
     * @param amount 购买数量
     * @param exchange 交易所类型 (CEX/DEX)
     * @return 买入价格
     */
    BigDecimal getBuyPrice(BigDecimal amount, String exchange);
    
    /**
     * 获取指定数量的卖出价格
     * 
     * @param amount 卖出数量
     * @param exchange 交易所类型 (CEX/DEX)
     * @return 卖出价格
     */
    BigDecimal getSellPrice(BigDecimal amount, String exchange);
    
    /**
     * 检查服务健康状态
     * 
     * @return 服务是否正常
     */
    boolean isHealthy();
} 