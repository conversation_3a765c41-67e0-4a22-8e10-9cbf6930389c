# 🔊 PUMP报警音测试工具使用说明

## 📋 功能概述

这个测试工具用于验证PUMP监控系统的报警音功能，包括：
- 买入报警音测试 (up.wav)
- 卖出报警音测试 (down.wav)
- 不同音效类型测试 (SYSTEM/CUSTOM/MULTIPLE)
- 配置参数调整

## 🚀 快速开始

### 1. 运行测试工具
```bash
# 方法1: 使用批处理脚本 (推荐)
run-alert-test.bat

# 方法2: 手动编译运行
javac TestAlertSound.java
java TestAlertSound
```

### 2. 测试菜单
启动后会显示以下菜单：
```
请选择测试项目：
1. 测试买入报警音 (up.wav)
2. 测试卖出报警音 (down.wav)
3. 测试系统音效 (SYSTEM)
4. 测试自定义音频 (CUSTOM)
5. 测试多重音效 (MULTIPLE)
6. 查看当前配置
7. 修改配置
0. 退出
```

## 🎵 测试项目详解

### **1. 买入报警音测试**
- **模拟场景**: Gate.io价格 $5620，Ultra买入价格 $5595
- **差价计算**: $5620 - $5595 = $25
- **触发条件**: 差价 > 阈值 (默认$20)
- **播放音频**: `sounds/up.wav`

### **2. 卖出报警音测试**
- **模拟场景**: Ultra卖出价格 $5630，Gate.io价格 $5605
- **差价计算**: $5630 - $5605 = $25
- **触发条件**: 差价 > 阈值 (默认$20)
- **播放音频**: `sounds/down.wav`

### **3. 系统音效测试**
- **买入音效**: 2声系统提示音，间隔200ms
- **卖出音效**: 3声系统提示音，间隔200ms
- **优点**: 无需音频文件，兼容性好

### **4. 自定义音频测试**
- **买入音频**: `sounds/up.wav`
- **卖出音频**: `sounds/down.wav`
- **格式要求**: WAV格式，PCM 16位，44.1kHz
- **回退机制**: 文件不存在时自动使用系统音效

### **5. 多重音效测试**
- **买入音效**: 4声系统提示音，间隔150ms
- **卖出音效**: 5声系统提示音，间隔150ms
- **用途**: 强调重要的套利机会

## ⚙️ 配置管理

### **查看当前配置 (选项6)**
显示当前的配置参数：
```
📋 当前配置：
报警启用: true
买入阈值: $20.00
卖出阈值: $20.00
音频类型: CUSTOM
```

### **修改配置 (选项7)**
可以修改以下参数：
1. **买入阈值** - 触发买入报警的最小差价
2. **卖出阈值** - 触发卖出报警的最小差价
3. **音频类型** - SYSTEM/CUSTOM/MULTIPLE
4. **启用/禁用报警** - 开关报警功能

## 📁 文件结构

```
pump-price-monitor/
├── TestAlertSound.java         # 测试工具源码
├── run-alert-test.bat         # 运行脚本
├── 报警音测试说明.md          # 本说明文档
└── sounds/                    # 音频文件目录
    ├── up.wav                # 买入报警音
    └── down.wav              # 卖出报警音
```

## 🔧 故障排除

### **音频不播放**
1. **检查音频文件**:
   ```bash
   dir sounds\
   # 应该看到 up.wav 和 down.wav
   ```

2. **检查文件格式**:
   - 确保是WAV格式
   - 确保不是损坏的文件

3. **测试系统音效**:
   - 选择选项3测试系统音效
   - 如果系统音效正常，说明是自定义音频问题

### **编译错误**
1. **检查Java版本**:
   ```bash
   java -version
   # 需要Java 8+
   ```

2. **检查文件完整性**:
   - 确保TestAlertSound.java文件完整
   - 重新下载或复制文件

### **阈值测试**
1. **调整阈值**:
   - 选择选项7修改配置
   - 将阈值设置为较小值 (如$5)
   - 重新测试

2. **查看详细输出**:
   - 工具会显示差价计算过程
   - 检查是否满足触发条件

## 📊 测试结果示例

### **成功触发报警**
```
🔥 测试买入报警音...
模拟场景：Gate.io价格更高，Ultra API买入更便宜
Gate.io总价: $5620.00
Ultra买入总价: $5595.00
买入差价: $25.00
🔥 买入套利机会！Ultra API成本更低 $25.00 (Gate: $5620.00 vs Ultra: $5595.00)
🔊 播放买入音效 (CUSTOM)...
✅ 播放自定义音频: E:\pump\sounds\up.wav
✅ 买入报警音测试完成
```

### **未触发报警**
```
🔥 测试买入报警音...
模拟场景：Gate.io价格更高，Ultra API买入更便宜
Gate.io总价: $5620.00
Ultra买入总价: $5595.00
买入差价: $25.00
ℹ️ 买入差价 $25.00 未超过阈值 $30.00，不触发报警
✅ 买入报警音测试完成
```

## 💡 使用技巧

1. **先测试系统音效** - 确保基本功能正常
2. **逐步测试** - 从简单到复杂
3. **调整阈值** - 根据实际需要设置合适的阈值
4. **音频文件** - 确保音频文件存在且格式正确
5. **多次测试** - 验证冷却机制是否正常工作

## 🔗 相关文档

- [自定义音频使用指南](docs/自定义音频使用指南.md)
- [JSON配置使用说明](docs/JSON配置使用说明.md)
- [报警问题诊断修复](docs/报警问题诊断修复.md)

---

💡 **提示**: 这个测试工具独立于主系统运行，可以安全地进行各种测试而不影响正常的价格监控功能。
