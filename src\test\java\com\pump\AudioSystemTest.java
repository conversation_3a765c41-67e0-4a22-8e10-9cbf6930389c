package com.pump;

import javax.sound.sampled.*;
import java.awt.Toolkit;
import java.io.InputStream;
import java.io.BufferedInputStream;

/**
 * 音频系统测试工具
 * 用于诊断音频播放问题
 */
public class AudioSystemTest {
    
    public static void main(String[] args) {
        System.out.println("🎵 开始音频系统诊断测试...");
        
        // 测试1: 系统蜂鸣
        testSystemBeep();
        
        // 等待2秒
        try { Thread.sleep(2000); } catch (InterruptedException e) {}
        
        // 测试2: 音频设备信息
        testAudioDevices();
        
        // 等待2秒
        try { Thread.sleep(2000); } catch (InterruptedException e) {}
        
        // 测试3: 播放up.wav
        testWavFile("up.wav");
        
        // 等待3秒
        try { Thread.sleep(3000); } catch (InterruptedException e) {}
        
        // 测试4: 播放down.wav
        testWavFile("down.wav");
        
        System.out.println("✅ 音频系统诊断测试完成");
    }
    
    /**
     * 测试系统蜂鸣
     */
    private static void testSystemBeep() {
        System.out.println("\n📢 测试1: 系统蜂鸣");
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            System.out.println("🔊 播放系统蜂鸣音...");
            
            for (int i = 0; i < 3; i++) {
                System.out.println("  蜂鸣 " + (i + 1) + "/3");
                toolkit.beep();
                Thread.sleep(500);
            }
            
            System.out.println("✅ 系统蜂鸣测试完成");
        } catch (Exception e) {
            System.out.println("❌ 系统蜂鸣测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试音频设备信息
     */
    private static void testAudioDevices() {
        System.out.println("\n🎧 测试2: 音频设备信息");
        try {
            // 获取音频系统信息
            Mixer.Info[] mixerInfos = AudioSystem.getMixerInfo();
            System.out.println("📋 可用音频设备数量: " + mixerInfos.length);
            
            for (int i = 0; i < mixerInfos.length; i++) {
                Mixer.Info info = mixerInfos[i];
                System.out.println("  设备 " + (i + 1) + ": " + info.getName());
                System.out.println("    描述: " + info.getDescription());
                System.out.println("    供应商: " + info.getVendor());
                
                // 检查设备是否支持播放
                Mixer mixer = AudioSystem.getMixer(info);
                Line.Info[] sourceLineInfos = mixer.getSourceLineInfo();
                System.out.println("    支持的输出线路: " + sourceLineInfos.length);
            }
            
            System.out.println("✅ 音频设备信息获取完成");
        } catch (Exception e) {
            System.out.println("❌ 音频设备信息获取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试WAV文件播放
     */
    private static void testWavFile(String fileName) {
        System.out.println("\n🎵 测试3: 播放WAV文件 - " + fileName);
        
        InputStream rawStream = null;
        InputStream audioStream = null;
        AudioInputStream audioInputStream = null;
        Clip clip = null;
        
        try {
            // 加载音频资源
            String resourcePath = "/audio/" + fileName;
            System.out.println("🔍 加载音频资源: " + resourcePath);
            
            rawStream = AudioSystemTest.class.getResourceAsStream(resourcePath);
            if (rawStream == null) {
                System.out.println("❌ 无法找到音频资源: " + resourcePath);
                return;
            }
            System.out.println("✅ 音频资源加载成功");
            
            // 处理音频流
            audioStream = rawStream.markSupported() ? 
                rawStream : new BufferedInputStream(rawStream, 8192);
            System.out.println("✅ 音频流处理完成，支持mark/reset: " + audioStream.markSupported());
            
            // 创建AudioInputStream
            audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            AudioFormat format = audioInputStream.getFormat();
            System.out.println("✅ AudioInputStream创建成功");
            System.out.println("  音频格式: " + format.toString());
            System.out.println("  采样率: " + format.getSampleRate() + " Hz");
            System.out.println("  声道数: " + format.getChannels());
            System.out.println("  位深度: " + format.getSampleSizeInBits() + " bits");
            
            // 创建音频剪辑
            clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            long durationMicros = clip.getMicrosecondLength();
            System.out.println("✅ 音频剪辑创建成功，时长: " + (durationMicros / 1000) + "ms");
            
            // 设置音量到最大
            if (clip.isControlSupported(FloatControl.Type.MASTER_GAIN)) {
                FloatControl gainControl = (FloatControl) clip.getControl(FloatControl.Type.MASTER_GAIN);
                gainControl.setValue(gainControl.getMaximum());
                System.out.println("✅ 音量设置到最大: " + gainControl.getValue() + " dB");
            } else {
                System.out.println("⚠️ 不支持音量控制");
            }
            
            // 播放音频
            System.out.println("🔊 开始播放音频...");
            clip.start();
            
            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(100);
            }
            
            System.out.println("✅ 音频播放完成: " + fileName);
            
        } catch (Exception e) {
            System.out.println("❌ 音频播放失败: " + fileName + " - " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 清理资源
            try {
                if (clip != null) clip.close();
                if (audioInputStream != null) audioInputStream.close();
                if (audioStream != null) audioStream.close();
                if (rawStream != null && rawStream != audioStream) rawStream.close();
            } catch (Exception e) {
                System.out.println("⚠️ 资源清理异常: " + e.getMessage());
            }
        }
    }
}
