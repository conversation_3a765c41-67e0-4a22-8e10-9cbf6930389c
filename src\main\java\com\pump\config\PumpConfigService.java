package com.pump.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.math.BigDecimal;

/**
 * PUMP配置服务
 * 从JSON文件加载和管理系统配置
 * 支持热重载配置文件
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
@Service
public class PumpConfigService {
    
    private static final Logger logger = LoggerFactory.getLogger(PumpConfigService.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private JsonNode config;

    // 配置文件路径
    private static final String CONFIG_FILE = "pump-config.json";
    
    @PostConstruct
    public void loadConfig() {
        try {
            // 只加载内部配置文件
            ClassPathResource resource = new ClassPathResource(CONFIG_FILE);
            if (resource.exists()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    config = objectMapper.readTree(inputStream);
                    logger.info("加载内部配置文件: {}", CONFIG_FILE);
                }
            } else {
                // 使用默认配置
                config = createDefaultConfig();
                logger.info("使用默认配置 (未找到配置文件)");
            }

            logConfigSummary();

        } catch (Exception e) {
            logger.error("加载配置文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("配置文件加载失败", e);
        }
    }

    
    // 监控配置
    public long getMonitorInterval() {
        // 移除硬编码的默认值，确保使用配置文件中的值
        if (config.has("monitor") && config.get("monitor").has("interval")) {
            return config.get("monitor").get("interval").asLong();
        }
        // 仅在配置缺失时使用默认值
        return 2000;
    }
    
    public long getMonitorAmount() {
        return config.path("monitor").path("amount").asLong(1000000);
    }
    
    // 报警配置
    public boolean isAlertEnabled() {
        return config.path("alert").path("enabled").asBoolean(true);
    }
    
    public BigDecimal getBuyThreshold() {
        return new BigDecimal(config.path("alert").path("buyThreshold").asText("30.00"));
    }
    
    public BigDecimal getSellThreshold() {
        return new BigDecimal(config.path("alert").path("sellThreshold").asText("30.00"));
    }
    
    public String getSoundType() {
        return config.path("alert").path("soundType").asText("CUSTOM");
    }

    public boolean isContinuousPlayEnabled() {
        return config.path("alert").path("continuousPlay").asBoolean(false);
    }
    
    // API配置
    public int getApiTimeout() {
        return config.path("api").path("timeout").asInt(30000);
    }
    
    public int getRetryDelay() {
        return config.path("api").path("retryDelay").asInt(2000);
    }
    
    // 代理配置
    public boolean isProxyEnabled() {
        return config.path("proxy").path("enabled").asBoolean(false);
    }
    
    public String getProxyHost() {
        return config.path("proxy").path("host").asText("127.0.0.1");
    }
    
    public int getProxyPort() {
        return config.path("proxy").path("port").asInt(7890);
    }
    
    public String getProxyType() {
        return config.path("proxy").path("type").asText("SOCKS");
    }
    
    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format(
            "监控配置 - 间隔: %dms, 数量: %d个PUMP | " +
            "报警配置 - 启用: %s, 买入阈值: $%.2f, 卖出阈值: $%.2f, 音频: %s | " +
            "API配置 - 超时: %dms, 重试延迟: %dms | " +
            "代理配置 - 启用: %s",
            getMonitorInterval(), getMonitorAmount(),
            isAlertEnabled(), getBuyThreshold(), getSellThreshold(), getSoundType(),
            getApiTimeout(), getRetryDelay(),
            isProxyEnabled()
        );
    }
    
    /**
     * 输出配置摘要到日志
     */
    private void logConfigSummary() {
        logger.info("=== PUMP系统配置 ===");
        logger.info("监控间隔: {}ms", getMonitorInterval());
        logger.info("监控数量: {}个PUMP", getMonitorAmount());
        logger.info("报警启用: {}", isAlertEnabled());
        logger.info("买入阈值: ${}", getBuyThreshold());
        logger.info("卖出阈值: ${}", getSellThreshold());
        logger.info("音频类型: {}", getSoundType());
        logger.info("API超时: {}ms", getApiTimeout());
        logger.info("代理启用: {}", isProxyEnabled());
        logger.info("==================");
        
        // 同时使用System.out.println确保配置信息一定显示，不受日志级别影响
        String separator = new String(new char[50]).replace('\0', '=');
        System.out.println("\n" + separator);
        System.out.println("🚀 PUMP价格监控系统配置信息");
        System.out.println(separator);
        System.out.println("📊 监控配置:");
        System.out.println("   ⏰ 监控间隔: " + getMonitorInterval() + "ms");
        System.out.println("   📈 监控数量: " + getMonitorAmount() + "个PUMP");
        System.out.println("");
        System.out.println("🔔 报警配置:");
        System.out.println("   ✅ 报警启用: " + (isAlertEnabled() ? "是" : "否"));
        System.out.println("   💰 买入阈值: $" + getBuyThreshold());
        System.out.println("   💸 卖出阈值: $" + getSellThreshold());
        System.out.println("   🔊 音频类型: " + getSoundType());
        System.out.println("   🔄 连续播放: " + (isContinuousPlayEnabled() ? "是" : "否"));
        System.out.println("");
        System.out.println("🌐 API配置:");
        System.out.println("   ⏱️ 超时时间: " + getApiTimeout() + "ms");
        System.out.println("   🔄 重试延迟: " + getRetryDelay() + "ms");
        System.out.println("");
        System.out.println("🔗 代理配置:");
        System.out.println("   🌐 代理启用: " + (isProxyEnabled() ? "是" : "否"));
        System.out.println(separator + "\n");
    }
    
    /**
     * 创建默认配置
     * 当没有找到任何配置文件时使用
     */
    private JsonNode createDefaultConfig() {
        try {
            String defaultConfigJson = "{\n" +
                "  \"monitor\": {\n" +
                "    \"interval\": 2000,\n" +
                "    \"amount\": 1000000,\n" +
                "    \"comment\": \"默认监控配置\"\n" +
                "  },\n" +
                "  \"alert\": {\n" +
                "    \"enabled\": true,\n" +
                "    \"buyThreshold\": 30.00,\n" +
                "    \"sellThreshold\": 30.00,\n" +
                "    \"soundType\": \"CUSTOM\",\n" +
                "    \"comment\": \"默认报警配置\"\n" +
                "  },\n" +
                "  \"api\": {\n" +
                "    \"timeout\": 30000,\n" +
                "    \"retryDelay\": 2000,\n" +
                "    \"comment\": \"默认API配置\"\n" +
                "  },\n" +
                "  \"proxy\": {\n" +
                "    \"enabled\": false,\n" +
                "    \"host\": \"127.0.0.1\",\n" +
                "    \"port\": 7890,\n" +
                "    \"type\": \"SOCKS\",\n" +
                "    \"comment\": \"默认代理配置\"\n" +
                "  }\n" +
                "}";

            return objectMapper.readTree(defaultConfigJson);
        } catch (Exception e) {
            logger.error("创建默认配置失败: {}", e.getMessage());
            throw new RuntimeException("创建默认配置失败", e);
        }
    }


}
