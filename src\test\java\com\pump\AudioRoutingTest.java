package com.pump;

import javax.sound.sampled.*;
import java.io.InputStream;
import java.io.BufferedInputStream;

/**
 * 音频路由测试
 * 专门测试音频输出路由问题，确保声音输出到正确的设备
 */
public class AudioRoutingTest {

    public static void main(String[] args) {
        System.out.println("🔀 ========== 音频路由测试 ==========");
        System.out.println("📢 测试音频输出路由，确保声音输出到正确设备");
        System.out.println();

        // 1. 强制使用系统默认设备
        testSystemDefaultDevice();
        
        sleep(3000);
        
        // 2. 强制使用扬声器设备
        testSpeakerDevice();
        
        sleep(3000);
        
        // 3. 测试所有可能的输出设备
        testAllOutputDevices();
        
        System.out.println("🔀 ========== 音频路由测试完成 ==========");
    }

    /**
     * 测试系统默认设备
     */
    private static void testSystemDefaultDevice() {
        System.out.println("🔊 === 测试系统默认音频设备 ===");
        
        try {
            // 获取系统默认的音频格式
            AudioFormat defaultFormat = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                44100.0f,  // 采样率
                16,        // 位深度
                2,         // 声道数 (立体声)
                4,         // 帧大小
                44100.0f,  // 帧率
                false      // 字节序
            );
            
            System.out.println("📊 使用默认格式: " + defaultFormat);
            
            // 获取默认的音频设备信息
            DataLine.Info info = new DataLine.Info(Clip.class, defaultFormat);
            
            if (AudioSystem.isLineSupported(info)) {
                System.out.println("✅ 系统支持默认音频格式");
                
                // 播放测试音频
                playTestAudioWithFormat("up.wav", defaultFormat, null);
                
            } else {
                System.out.println("❌ 系统不支持默认音频格式");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 系统默认设备测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试扬声器设备
     */
    private static void testSpeakerDevice() {
        System.out.println("\n🔊 === 强制使用扬声器设备 ===");
        
        try {
            Mixer.Info[] mixerInfos = AudioSystem.getMixerInfo();
            Mixer speakerMixer = null;
            
            // 查找扬声器设备
            for (Mixer.Info info : mixerInfos) {
                String name = info.getName().toLowerCase();
                if (name.contains("扬声器") || name.contains("speaker") || 
                    name.contains("主声音驱动程序") || name.contains("primary sound driver") ||
                    name.contains("realtek") || name.contains("audio")) {
                    
                    System.out.println("🎧 找到可能的扬声器设备: " + info.getName());
                    
                    try {
                        speakerMixer = AudioSystem.getMixer(info);
                        
                        // 检查设备是否支持输出
                        Line.Info[] sourceLineInfos = speakerMixer.getSourceLineInfo();
                        if (sourceLineInfos.length > 0) {
                            System.out.println("✅ 设备支持音频输出，尝试播放...");
                            
                            // 在这个设备上播放测试音频
                            playTestAudioOnDevice("up.wav", speakerMixer, info.getName());
                            
                            // 只测试第一个找到的扬声器设备
                            break;
                        } else {
                            System.out.println("⚠️ 设备不支持音频输出");
                        }
                        
                    } catch (Exception e) {
                        System.out.println("❌ 设备测试失败: " + e.getMessage());
                    }
                }
            }
            
            if (speakerMixer == null) {
                System.out.println("⚠️ 未找到扬声器设备，使用默认设备");
                playTestAudioWithFormat("up.wav", null, null);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 扬声器设备测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试所有输出设备
     */
    private static void testAllOutputDevices() {
        System.out.println("\n🔊 === 测试所有可能的输出设备 ===");
        
        try {
            Mixer.Info[] mixerInfos = AudioSystem.getMixerInfo();
            
            for (int i = 0; i < mixerInfos.length; i++) {
                Mixer.Info info = mixerInfos[i];
                System.out.println("\n🎧 设备 " + (i + 1) + ": " + info.getName());
                
                try {
                    Mixer mixer = AudioSystem.getMixer(info);
                    Line.Info[] sourceLineInfos = mixer.getSourceLineInfo();
                    
                    if (sourceLineInfos.length > 0) {
                        System.out.println("   ✅ 设备支持输出，尝试播放...");
                        
                        // 播放短音频测试
                        playShortTestAudio("up.wav", mixer, info.getName());
                        
                    } else {
                        System.out.println("   ⚠️ 设备不支持输出");
                    }
                    
                } catch (Exception e) {
                    System.out.println("   ❌ 设备测试失败: " + e.getMessage());
                }
                
                sleep(1000); // 设备间间隔
            }
            
        } catch (Exception e) {
            System.out.println("❌ 所有设备测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 使用指定格式播放测试音频
     */
    private static void playTestAudioWithFormat(String fileName, AudioFormat targetFormat, Mixer mixer) {
        InputStream rawStream = null;
        InputStream audioStream = null;
        AudioInputStream audioInputStream = null;
        Clip clip = null;
        
        try {
            // 加载音频文件
            String resourcePath = "/audio/" + fileName;
            rawStream = AudioRoutingTest.class.getResourceAsStream(resourcePath);
            if (rawStream == null) {
                System.out.println("❌ 找不到音频文件: " + resourcePath);
                return;
            }
            
            audioStream = new BufferedInputStream(rawStream, 8192);
            audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            
            System.out.println("📊 原始音频格式: " + audioInputStream.getFormat());
            
            // 创建Clip
            if (mixer != null && targetFormat != null) {
                clip = (Clip) mixer.getLine(new DataLine.Info(Clip.class, targetFormat));
            } else if (mixer != null) {
                clip = (Clip) mixer.getLine(new DataLine.Info(Clip.class, audioInputStream.getFormat()));
            } else {
                clip = AudioSystem.getClip();
            }
            
            clip.open(audioInputStream);
            
            // 设置音量到最大
            setMaxVolume(clip);
            
            // 播放音频
            System.out.println("🎵 开始播放 - 请仔细听！");
            clip.start();
            
            // 等待播放完成
            long startTime = System.currentTimeMillis();
            while (clip.isRunning() && (System.currentTimeMillis() - startTime) < 5000) {
                Thread.sleep(50);
            }
            
            if (clip.isRunning()) {
                clip.stop();
                System.out.println("⏰ 播放超时，强制停止");
            } else {
                System.out.println("✅ 播放完成");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 音频播放失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeResources(clip, audioInputStream, audioStream, rawStream);
        }
    }

    /**
     * 在指定设备上播放测试音频
     */
    private static void playTestAudioOnDevice(String fileName, Mixer mixer, String deviceName) {
        System.out.println("   🎵 在设备 [" + deviceName + "] 上播放");
        playTestAudioWithFormat(fileName, null, mixer);
    }

    /**
     * 播放短音频测试
     */
    private static void playShortTestAudio(String fileName, Mixer mixer, String deviceName) {
        System.out.println("   🎵 短音频测试: " + deviceName);
        
        InputStream rawStream = null;
        InputStream audioStream = null;
        AudioInputStream audioInputStream = null;
        Clip clip = null;
        
        try {
            String resourcePath = "/audio/" + fileName;
            rawStream = AudioRoutingTest.class.getResourceAsStream(resourcePath);
            if (rawStream == null) return;
            
            audioStream = new BufferedInputStream(rawStream, 8192);
            audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            
            try {
                clip = (Clip) mixer.getLine(new DataLine.Info(Clip.class, audioInputStream.getFormat()));
            } catch (Exception e) {
                clip = AudioSystem.getClip();
            }
            
            clip.open(audioInputStream);
            setMaxVolume(clip);
            
            System.out.println("     🔊 播放中...");
            clip.start();
            
            // 只播放500ms
            Thread.sleep(500);
            
            if (clip.isRunning()) {
                clip.stop();
            }
            
            System.out.println("     ✅ 短音频测试完成");
            
        } catch (Exception e) {
            System.out.println("     ❌ 短音频测试失败: " + e.getMessage());
        } finally {
            closeResources(clip, audioInputStream, audioStream, rawStream);
        }
    }

    /**
     * 设置音量到最大
     */
    private static void setMaxVolume(Clip clip) {
        try {
            Control[] controls = clip.getControls();
            
            for (Control control : controls) {
                if (control instanceof FloatControl) {
                    FloatControl floatControl = (FloatControl) control;
                    
                    if (control.getType() == FloatControl.Type.MASTER_GAIN ||
                        control.getType() == FloatControl.Type.VOLUME) {
                        
                        float maxValue = floatControl.getMaximum();
                        floatControl.setValue(maxValue);
                        System.out.println("🔊 音量设置到最大: " + maxValue + " dB");
                    }
                    
                } else if (control instanceof BooleanControl) {
                    BooleanControl booleanControl = (BooleanControl) control;
                    
                    if (control.getType() == BooleanControl.Type.MUTE) {
                        booleanControl.setValue(false);
                        System.out.println("🔇 取消静音");
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("⚠️ 音量设置失败: " + e.getMessage());
        }
    }

    /**
     * 关闭资源
     */
    private static void closeResources(Clip clip, AudioInputStream audioInputStream, 
                                     InputStream audioStream, InputStream rawStream) {
        try {
            if (clip != null) clip.close();
            if (audioInputStream != null) audioInputStream.close();
            if (audioStream != null) audioStream.close();
            if (rawStream != null && rawStream != audioStream) rawStream.close();
        } catch (Exception e) {
            // 忽略清理异常
        }
    }

    /**
     * 线程睡眠
     */
    private static void sleep(int milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
