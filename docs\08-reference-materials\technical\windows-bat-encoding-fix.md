# Windows BAT脚本中文字符乱码问题解决方案

## 问题现象

当运行BAT脚本时，中文字符显示为乱码，例如：
```
鐜鍙橀噺閰嶇疆娴嬭瘯
娴嬭瘯JAVA_HOME...
娴嬭瘯MAVEN_HOME...
娴嬭瘯Java鍛戒护...
Java鍛戒护澶辫触
```

## 问题根因

1. **编码不匹配**: BAT文件保存为UTF-8编码，但Windows命令行默认使用GBK/CP936编码
2. **代码页设置**: Windows命令行的代码页设置与文件编码不一致
3. **系统区域设置**: 系统区域设置影响默认编码

## 解决方案

### 方案1：修改代码页设置（推荐）

在每个BAT脚本的开头添加以下代码：
```batch
@echo off
chcp 65001 >nul
```

### 方案2：使用PowerShell脚本替代

创建对应的PowerShell脚本（.ps1文件）：
```powershell
# 设置输出编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
Write-Host "环境变量配置测试"
```

### 方案3：修改文件编码

将BAT文件重新保存为GBK/ANSI编码：
1. 使用记事本打开BAT文件
2. 点击"另存为"
3. 在编码下拉框中选择"ANSI"
4. 保存文件

## 实施步骤

### 步骤1：批量修复现有BAT脚本

我将为所有BAT脚本添加代码页设置：
- test-env.bat
- maven-setup-guide.bat
- verification-script.bat
- quick-build-run.bat
- full-verification.bat
- 等等

### 步骤2：创建PowerShell版本

为关键脚本创建PowerShell版本，避免编码问题：
- test-env.ps1
- maven-setup-guide.ps1
- verification-script.ps1

### 步骤3：更新使用指南

更新文档，推荐使用PowerShell脚本或修复后的BAT脚本。

## 验证方法

修复后，运行脚本应该显示正常的中文字符：
```
========================================
环境变量配置测试
========================================

测试JAVA_HOME...
JAVA_HOME: F:\java2

测试MAVEN_HOME...
MAVEN_HOME: F:\apache-maven-3.9.10
```

## 最佳实践

1. **统一使用UTF-8**: 所有文本文件使用UTF-8编码
2. **添加代码页设置**: BAT脚本开头添加 `chcp 65001`
3. **优先使用PowerShell**: 对于复杂脚本，使用PowerShell替代BAT
4. **测试验证**: 修改后及时测试验证效果

## 注意事项

- `chcp 65001` 会改变整个命令行会话的编码
- 部分旧版本Windows系统可能不完全支持UTF-8
- 建议在脚本结束时恢复原始代码页（可选）

---

**创建日期**: 2025-01-15
**更新日期**: 2025-01-15
**状态**: 待实施 