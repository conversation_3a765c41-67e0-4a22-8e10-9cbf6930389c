# PUMP价格监控系统MVP配置
# 应用配置
spring.application.name=pump-price-monitor
# 启用Web服务器 - 用于音频测试接口
spring.main.web-application-type=servlet
server.port=8080

# 日志配置 - 简化输出，只显示价格验证信息
logging.level.root=WARN
logging.level.com.pump=WARN
logging.level.com.pump.scheduler=INFO
logging.pattern.console=%msg%n
logging.level.org.springframework=WARN
logging.charset.console=UTF-8
logging.charset.file=UTF-8

# 价格监控配置
# 调整为2秒间隔确保不超过Jupiter API限制 (每分钟30次监控 × 2次Jupiter调用 = 60次/分钟)
pump.monitor.interval=2000
pump.monitor.amount=1000000

# 报警音配置
pump.alert.enabled=true
pump.alert.buy-threshold=0.10
pump.alert.sell-threshold=0.10
pump.alert.cooldown=5000
pump.alert.sound-type=CUSTOM

# Gate.io API配置
gate.api.base-url=https://api.gateio.ws/api/v4
gate.api.timeout=10000

# Jupiter API配置 - 优化超时和重试
# 免费版本配置（无需API key）
jupiter.api.base-url=https://lite-api.jup.ag/price/v3
jupiter.api.quote-url=https://lite-api.jup.ag/swap/v1/quote
jupiter.api.ultra-url=https://lite-api.jup.ag/ultra/v1/order
# 付费版本配置（需要API key时启用）
# jupiter.api.base-url=https://api.jup.ag/price/v3
# jupiter.api.quote-url=https://api.jup.ag/swap/v1/quote
# jupiter.api.ultra-url=https://api.jup.ag/ultra/v1/order
# jupiter.api.key=your-api-key-here
jupiter.api.timeout=30000
jupiter.api.connect-timeout=15000
jupiter.api.read-timeout=20000

# 代理配置 - 可选启用
proxy.enabled=true
proxy.host=127.0.0.1
proxy.port=7890
proxy.type=SOCKS

# 重试配置
jupiter.api.retry.max-attempts=3
jupiter.api.retry.delay=1000

# 系统配置
system.timezone=Asia/Shanghai
system.locale=zh_CN

# Spring Boot标准编码配置
spring.messages.encoding=UTF-8
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
