package com.pump.config;

import java.io.PrintStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.util.TimeZone;

/**
 * 编码初始化器
 * 在应用启动的最早阶段强制设置UTF-8编码
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class EncodingInitializer {
    
    private static boolean initialized = false;
    
    /**
     * 强制初始化UTF-8编码
     * 使用反射等方式确保编码设置生效
     */
    public static synchronized void forceInitializeUTF8() {
        if (initialized) {
            return;
        }
        
        try {
            // 1. 设置系统属性
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("console.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");
            System.setProperty("user.timezone", "Asia/Shanghai");
            
            // 2. 设置默认时区
            TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
            
            // 3. 尝试通过反射设置默认字符集
            try {
                Field charsetField = Charset.class.getDeclaredField("defaultCharset");
                charsetField.setAccessible(true);
                charsetField.set(null, Charset.forName("UTF-8"));
            } catch (Exception e) {
                // 忽略反射失败
            }
            
            // 4. 重新设置标准输出流
            try {
                System.setOut(new PrintStream(System.out, true, "UTF-8"));
                System.setErr(new PrintStream(System.err, true, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                // 忽略编码异常
            }
            
            // 5. Windows控制台设置
            if (isWindows()) {
                setWindowsConsoleUTF8();
            }
            
            initialized = true;
            
        } catch (Exception e) {
            System.err.println("强制编码初始化失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否为Windows系统
     */
    private static boolean isWindows() {
        return System.getProperty("os.name").toLowerCase().contains("windows");
    }
    
    /**
     * 设置Windows控制台为UTF-8
     */
    private static void setWindowsConsoleUTF8() {
        try {
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", "chcp", "65001");
            pb.redirectErrorStream(true);
            Process process = pb.start();
            process.waitFor();
        } catch (Exception e) {
            // 忽略错误
        }
    }
    
    /**
     * 检查编码是否已正确初始化
     */
    public static boolean isInitialized() {
        return initialized && "UTF-8".equals(System.getProperty("file.encoding"));
    }
}
