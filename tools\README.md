# 🛠️ PUMP价格监控系统工具集

本目录包含PUMP价格监控系统的各种工具、测试文件和脚本。

## 🎵 音频工具

### 音频转换
- [convert_audio.bat](convert_audio.bat) - M4A/MP3转WAV格式转换脚本
- [GenerateTestAudio.java](GenerateTestAudio.java) - 生成测试音频文件

**使用方法**:
```bash
# 转换音频文件
./convert_audio.bat

# 生成测试音频
java GenerateTestAudio.java
```

## 🧪 测试工具

### API测试
- [JupiterApiTest.java](JupiterApiTest.java) - Jupiter API测试
- [SimpleJupiterTest.java](SimpleJupiterTest.java) - 简化Jupiter测试
- [TestUltraAPI.java](TestUltraAPI.java) - Ultra API测试
- [TestUltraAPIWithProxy.java](TestUltraAPIWithProxy.java) - 代理环境Ultra API测试

### 价格测试
- [Test100WPumpPrice.java](Test100WPumpPrice.java) - 100万PUMP价格测试
- [TestBuyWith6000USDT.java](TestBuyWith6000USDT.java) - 6000USDT买入测试
- [TestBuySellPriceDifference.java](TestBuySellPriceDifference.java) - 买卖价差测试

### 系统功能测试
- [TestAlertSoundSystem.java](TestAlertSoundSystem.java) - 报警音系统测试
- [TestCacheIntegration.java](TestCacheIntegration.java) - 缓存集成测试
- [TestPriceMonitoringFix.java](TestPriceMonitoringFix.java) - 价格监控修复测试

### 逻辑测试
- [TestCorrectLogic.java](TestCorrectLogic.java) - 正确逻辑测试
- [TestCurrentLogic.java](TestCurrentLogic.java) - 当前逻辑测试
- [TestFixedLogic.java](TestFixedLogic.java) - 修复后逻辑测试

### 输出格式测试
- [TestCorrectedOutputFormat.java](TestCorrectedOutputFormat.java) - 修正输出格式测试
- [TestNewOutputFormat.java](TestNewOutputFormat.java) - 新输出格式测试
- [TestSimplifiedOutput.java](TestSimplifiedOutput.java) - 简化输出测试

### Jupiter相关测试
- [TestJupiterFixed.java](TestJupiterFixed.java) - Jupiter修复测试
- [TestJupiterImprovements.java](TestJupiterImprovements.java) - Jupiter改进测试
- [TestJupiterQuoteAPIFix.java](TestJupiterQuoteAPIFix.java) - Jupiter Quote API修复测试
- [TestJupiterQuoteFix.java](TestJupiterQuoteFix.java) - Jupiter Quote修复测试

### 系统测试
- [TestCorrectedAlertSystem.java](TestCorrectedAlertSystem.java) - 修正报警系统测试
- [TestSchedulerFix.java](TestSchedulerFix.java) - 调度器修复测试

## 🔧 构建和运行脚本

### 构建脚本
- [full-verification.bat](full-verification.bat) - 完整验证脚本
- [run-jar.bat](run-jar.bat) - JAR运行脚本
- [run-mvp.bat](run-mvp.bat) - MVP版本运行脚本

### 环境测试
- [test-env.bat](test-env.bat) - 环境测试脚本
- [test-env.ps1](test-env.ps1) - PowerShell环境测试
- [test-java-env.bat](test-java-env.bat) - Java环境测试
- [test-proxy.bat](test-proxy.bat) - 代理测试脚本

### 编码和验证
- [test-encoding-fix.bat](test-encoding-fix.bat) - 编码修复测试
- [verify-without-maven.ps1](verify-without-maven.ps1) - 无Maven验证脚本

### 配置工具
- [setup-maven-admin.ps1](setup-maven-admin.ps1) - Maven管理员配置
- [ProxyTestTool.java](ProxyTestTool.java) - 代理测试工具

## 📋 使用指南

### 运行Java测试工具
```bash
# 编译并运行测试
javac TestFileName.java
java TestFileName

# 或直接运行（如果已编译）
java TestFileName
```

### 运行批处理脚本
```bash
# Windows
script-name.bat

# PowerShell
.\script-name.ps1
```

### 音频工具使用
```bash
# 1. 将音频文件放在项目根目录
# 2. 运行转换脚本
.\convert_audio.bat

# 3. 生成的WAV文件会在sounds目录中
```

## 🔍 工具分类

### 按功能分类
```
音频工具/
├── convert_audio.bat
├── GenerateTestAudio.java
├── SimpleAlertTest.java        # 简化报警音测试
├── TestAlertSound.java         # 完整报警音测试
├── test-alert.bat             # 简化测试脚本
└── run-alert-test.bat         # 完整测试脚本

API测试/
├── JupiterApiTest.java
├── SimpleJupiterTest.java
├── TestUltraAPI.java
└── TestUltraAPIWithProxy.java

价格测试/
├── Test100WPumpPrice.java
├── TestBuyWith6000USDT.java
└── TestBuySellPriceDifference.java

系统测试/
├── TestAlertSoundSystem.java
├── TestCacheIntegration.java
└── TestPriceMonitoringFix.java

构建脚本/
├── full-verification.bat
├── run-jar.bat
└── run-mvp.bat
```

## ⚠️ 注意事项

1. **Java工具**: 需要Java 8+环境
2. **批处理脚本**: 仅在Windows环境下运行
3. **PowerShell脚本**: 可能需要执行策略权限
4. **音频工具**: 需要FFmpeg支持
5. **测试工具**: 部分需要网络连接

## 🤝 贡献

如需添加新的工具或测试，请：
1. 将文件放入相应分类
2. 更新本README文档
3. 添加必要的使用说明

---

💡 **提示**: 这些工具主要用于开发和测试，生产环境请使用主程序。
