# Maven 安装指南

## 🔧 手动安装Maven (Windows)

### 1. 下载Maven
- 访问: https://maven.apache.org/download.cgi
- 下载: `apache-maven-3.9.5-bin.zip` (或最新版本)
- 解压到: `C:\apache-maven-3.9.5`

### 2. 配置环境变量
1. 右键"此电脑" → "属性" → "高级系统设置"
2. 点击"环境变量"
3. 在"系统变量"中点击"新建"：
   - 变量名: `MAVEN_HOME`
   - 变量值: `C:\apache-maven-3.9.5`
4. 编辑系统变量"Path"，添加: `%MAVEN_HOME%\bin`

### 3. 验证安装
重新打开PowerShell，运行：
```powershell
mvn -version
```

## 🚀 快速验证脚本

如果Maven安装成功，运行以下命令：

```powershell
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 启动系统
mvn spring-boot:run
```

## 📋 验证检查清单

- [ ] Maven成功安装 (`mvn -version` 显示版本信息)
- [ ] 项目编译成功 (`mvn clean compile` 无错误)
- [ ] 系统可以启动 (`mvn spring-boot:run` 成功启动)
- [ ] 输出显示分离的买入/卖出价格
- [ ] 价格差异反映真实市场状况

## 🔄 如果不想安装Maven

可以使用以下替代方案：

1. **IDE运行**: 在IntelliJ IDEA或Eclipse中直接运行
2. **Java命令**: 手动编译并运行（较复杂）
3. **在线编译**: 使用在线Java编译器

## 📞 需要帮助？

如果安装过程中遇到问题，请告诉我具体的错误信息。 