@echo off
REM Set UTF-8 encoding for console
chcp 65001 >nul

echo ===============================================
echo           PUMP30 价格监控系统
echo ===============================================
echo.

REM Check Java environment
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请先安装Java 8+
    pause
    exit /b 1
)

echo Java环境检查通过
echo.

REM Check JAR file
if exist "pump30.jar" (
    echo [确认] JAR文件存在: pump30.jar
) else (
    echo [错误] JAR文件不存在: pump30.jar
    pause
    exit /b 1
)

echo [信息] 配置已内置到JAR文件中
echo.
echo 启动PUMP30价格监控系统...
echo 按 Ctrl+C 停止程序
echo.

REM Run with UTF-8 encoding
java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar pump30.jar

echo.
echo PUMP30价格监控系统已停止
pause
