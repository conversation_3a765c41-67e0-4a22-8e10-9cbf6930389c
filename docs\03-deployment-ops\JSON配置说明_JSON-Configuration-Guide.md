---
title: "PUMP监控系统JSON配置使用说明"
author: "配置管理工程师"
created: "2025-01-14"
updated: "2025-01-16"
version: "1.2"
category: "deployment-ops"
tags: ["configuration", "json", "deployment", "setup"]
status: "active"
---

# 🔧 PUMP监控系统JSON配置使用说明

## ✅ 已完成的优化

### 1. **JSON配置文件支持**
- ✅ 创建了 `pump-config.json` 配置文件
- ✅ 支持动态加载和热重载
- ✅ 替代了原有的 `application.properties` 配置

### 2. **优化的报警冷却机制**
- ✅ 移除了30秒固定冷却时间
- ✅ 改为音频播放状态检测
- ✅ 只在上一段音频播放完成后才播放新音频

## 📄 JSON配置文件详解

### **配置文件位置**
```
优先级1: ./pump-config.json        (外部配置文件，可热重载)
优先级2: src/main/resources/pump-config.json  (内部配置文件)
```

### **完整配置示例**
```json
{
  "monitor": {
    "interval": 2000,
    "amount": 1000000,
    "comment": "监控间隔(毫秒)和监控数量(PUMP代币数量)"
  },
  "alert": {
    "enabled": true,
    "buyThreshold": 30.00,
    "sellThreshold": 30.00,
    "soundType": "CUSTOM",
    "comment": "报警配置 - 阈值单位为美元，soundType可选: SYSTEM, MULTIPLE, CUSTOM"
  },
  "api": {
    "timeout": 30000,
    "retryDelay": 2000,
    "comment": "API配置 - 超时时间和重试延迟(毫秒)"
  },
  "proxy": {
    "enabled": false,
    "host": "127.0.0.1",
    "port": 7890,
    "type": "SOCKS",
    "comment": "代理配置 - type可选: HTTP, SOCKS"
  }
}
```

## ⚙️ 配置参数详解

### **监控配置 (monitor)**
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `interval` | 数字 | 2000 | 监控间隔(毫秒) |
| `amount` | 数字 | 1000000 | 监控的PUMP代币数量 |

**间隔建议**：
- 2000ms (2秒) - 免费API极限 (60次/分钟)
- 2500ms (2.5秒) - 免费API安全 (48次/分钟)
- 1000ms (1秒) - 需要付费API

### **报警配置 (alert)**
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | 布尔 | true | 是否启用报警 |
| `buyThreshold` | 数字 | 30.00 | 买入报警阈值($) |
| `sellThreshold` | 数字 | 30.00 | 卖出报警阈值($) |
| `soundType` | 字符串 | "CUSTOM" | 音频类型 |

**音频类型选项**：
- `SYSTEM` - 系统提示音 (买入2声，卖出3声)
- `MULTIPLE` - 多重提示音 (买入4声，卖出5声)
- `CUSTOM` - 自定义WAV文件

### **API配置 (api)**
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `timeout` | 数字 | 30000 | API超时时间(毫秒) |
| `retryDelay` | 数字 | 2000 | 429错误重试延迟(毫秒) |

### **代理配置 (proxy)**
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | 布尔 | false | 是否启用代理 |
| `host` | 字符串 | "127.0.0.1" | 代理主机 |
| `port` | 数字 | 7890 | 代理端口 |
| `type` | 字符串 | "SOCKS" | 代理类型 |

## 🔄 配置热重载

### **如何使用热重载**
1. **复制配置文件到外部**：
   ```bash
   cp src/main/resources/pump-config.json ./pump-config.json
   ```

2. **修改外部配置文件**：
   ```json
   {
     "monitor": {
       "interval": 1500,  // 修改监控间隔
       "amount": 1000000
     },
     "alert": {
       "enabled": true,
       "buyThreshold": 25.00,  // 修改报警阈值
       "sellThreshold": 35.00,
       "soundType": "SYSTEM"   // 修改音频类型
     }
   }
   ```

3. **系统自动检测更新**：
   - 系统会自动检测文件修改时间
   - 发现更新后自动重新加载配置
   - 无需重启系统

## 🎵 优化的报警机制

### **新的冷却逻辑**
```java
// 旧逻辑：固定30秒冷却
if (lastAlert + 30000 < currentTime) {
    playAlert();
}

// 新逻辑：音频播放状态检测
if (!isAudioPlaying) {
    playAlert();
    isAudioPlaying = true;
    // 播放完成后自动设置为false
}
```

### **优势**
- ✅ **更快响应**：不需要等待30秒
- ✅ **避免重叠**：防止多个音频同时播放
- ✅ **更自然**：音频播放完立即可以播放下一个

### **播放状态管理**
- 买入报警和卖出报警独立管理
- 可以同时触发不同类型的报警
- 自动重置播放状态

## 🚀 使用步骤

### **1. 创建外部配置文件**
```bash
# 复制默认配置
cp src/main/resources/pump-config.json ./pump-config.json

# 或者手动创建
touch pump-config.json
```

### **2. 自定义配置**
根据需要修改 `pump-config.json` 中的参数

### **3. 启动系统**
```bash
java -jar target/pump-monitor-1.0.jar
```

### **4. 实时调整**
- 修改外部配置文件
- 系统自动检测并应用新配置
- 无需重启

## 📊 配置示例

### **高频监控配置**
```json
{
  "monitor": {
    "interval": 1000,
    "amount": 1000000
  },
  "alert": {
    "enabled": true,
    "buyThreshold": 20.00,
    "sellThreshold": 20.00,
    "soundType": "MULTIPLE"
  }
}
```

### **保守监控配置**
```json
{
  "monitor": {
    "interval": 3000,
    "amount": 1000000
  },
  "alert": {
    "enabled": true,
    "buyThreshold": 50.00,
    "sellThreshold": 50.00,
    "soundType": "SYSTEM"
  }
}
```

### **静音监控配置**
```json
{
  "monitor": {
    "interval": 2000,
    "amount": 1000000
  },
  "alert": {
    "enabled": false,
    "buyThreshold": 30.00,
    "sellThreshold": 30.00,
    "soundType": "CUSTOM"
  }
}
```

## 🔧 故障排除

### **配置文件格式错误**
- 确保JSON格式正确
- 使用JSON验证工具检查语法
- 查看系统日志中的错误信息

### **配置不生效**
- 检查外部配置文件是否存在
- 确认文件权限可读
- 查看系统日志确认加载状态

### **热重载不工作**
- 确保修改了外部配置文件 (./pump-config.json)
- 检查文件修改时间是否更新
- 系统会在下次监控周期时检测更新

现在您可以通过JSON文件灵活配置所有参数，并享受优化的报警体验！🎉
