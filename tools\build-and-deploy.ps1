# ===============================================================================
# PUMP价格监控系统 - 构建和部署脚本 v1.0
# 功能：修改报警阈值 -> 编译打包 -> 复制jar包到日期目录 -> 生成启动脚本
# 作者：PUMP Project Team
# 创建日期：$(Get-Date -Format "yyyy-MM-dd")
# ===============================================================================

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[System.Text.Encoding]::Default = [System.Text.Encoding]::UTF8

# 配置文件路径
$configPath = "src\main\resources\pump-config.json"
$buildDir = "builds"
$dateDir = Get-Date -Format "yyyy-MM-dd"

Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "🚀 PUMP价格监控系统 - 构建和部署脚本" -ForegroundColor Green
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""

# ===== 步骤1：修改报警阈值 =====
Write-Host "📝 步骤1：修改报警阈值配置" -ForegroundColor Yellow
Write-Host "───────────────────────────────────────────────────────────────────────────────" -ForegroundColor Gray

# 读取当前配置
try {
    $config = Get-Content $configPath -Encoding UTF8 | ConvertFrom-Json
    $currentBuyThreshold = $config.alert.buyThreshold
    $currentSellThreshold = $config.alert.sellThreshold
    $currentMonitorAmount = $config.monitor.amount
    
    Write-Host "📊 当前配置："
    Write-Host "   💰 买入阈值：$currentBuyThreshold 美元" -ForegroundColor Green
    Write-Host "   💸 卖出阈值：$currentSellThreshold 美元" -ForegroundColor Red
    Write-Host "   📈 监控数量：$currentMonitorAmount 个PUMP" -ForegroundColor Cyan
    Write-Host ""
} catch {
    Write-Host "❌ 读取配置文件失败：$($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 用户输入新阈值和监控数量
Write-Host "🔧 请输入新的监控配置："
Write-Host ""

do {
    $newBuyThreshold = Read-Host "💰 买入阈值 (美元，当前：$currentBuyThreshold)"
    if ([string]::IsNullOrEmpty($newBuyThreshold)) {
        $newBuyThreshold = $currentBuyThreshold
        Write-Host "   ➡️ 使用当前值：$newBuyThreshold" -ForegroundColor Cyan
    }
} while (-not ([double]::TryParse($newBuyThreshold, [ref]$null)))

do {
    $newSellThreshold = Read-Host "💸 卖出阈值 (美元，当前：$currentSellThreshold)"
    if ([string]::IsNullOrEmpty($newSellThreshold)) {
        $newSellThreshold = $currentSellThreshold
        Write-Host "   ➡️ 使用当前值：$newSellThreshold" -ForegroundColor Cyan
    }
} while (-not ([double]::TryParse($newSellThreshold, [ref]$null)))

do {
    $newMonitorAmount = Read-Host "📈 监控数量 (个PUMP，当前：$currentMonitorAmount)"
    if ([string]::IsNullOrEmpty($newMonitorAmount)) {
        $newMonitorAmount = $currentMonitorAmount
        Write-Host "   ➡️ 使用当前值：$newMonitorAmount" -ForegroundColor Cyan
    }
} while (-not ([long]::TryParse($newMonitorAmount, [ref]$null)))

# 转换为数字类型
$newBuyThreshold = [double]$newBuyThreshold
$newSellThreshold = [double]$newSellThreshold
$newMonitorAmount = [long]$newMonitorAmount

Write-Host ""
Write-Host "✅ 新配置确认："
Write-Host "   💰 买入阈值：$newBuyThreshold 美元" -ForegroundColor Green
Write-Host "   💸 卖出阈值：$newSellThreshold 美元" -ForegroundColor Red
Write-Host "   📈 监控数量：$newMonitorAmount 个PUMP" -ForegroundColor Cyan

$confirm = Read-Host "📝 确认修改配置？(Y/n)"
if ($confirm -eq 'n' -or $confirm -eq 'N') {
    Write-Host "❌ 用户取消操作" -ForegroundColor Red
    exit 0
}

# 更新配置文件
try {
    $config.alert.buyThreshold = $newBuyThreshold
    $config.alert.sellThreshold = $newSellThreshold
    $config.monitor.amount = $newMonitorAmount
    $config | ConvertTo-Json -Depth 5 | Set-Content $configPath -Encoding UTF8
    Write-Host "✅ 配置文件已更新" -ForegroundColor Green
} catch {
    Write-Host "❌ 更新配置文件失败：$($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# ===== 步骤2：编译打包JAR =====
Write-Host "🔨 步骤2：编译打包JAR" -ForegroundColor Yellow
Write-Host "───────────────────────────────────────────────────────────────────────────────" -ForegroundColor Gray

Write-Host "🧹 清理target目录..."
& mvn clean | Out-Null

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Maven clean 失败" -ForegroundColor Red
    exit 1
}

Write-Host "🔨 开始编译和打包..."
$buildOutput = & mvn clean package -DskipTests 2>&1
$buildSuccess = $LASTEXITCODE -eq 0

if ($buildSuccess) {
    Write-Host "✅ JAR包构建成功" -ForegroundColor Green
    
    # 获取生成的JAR包路径
    $jarPath = "target\pump.jar"
    if (Test-Path $jarPath) {
        $jarSize = (Get-Item $jarPath).Length / 1MB
        Write-Host "📦 JAR包信息：$jarPath ($('{0:N1}' -f $jarSize) MB)" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ JAR包构建失败" -ForegroundColor Red
    Write-Host "错误输出：" -ForegroundColor Red
    $buildOutput | ForEach-Object { Write-Host "   $_" -ForegroundColor Red }
    exit 1
}

Write-Host ""

# ===== 步骤3：创建打包记录目录并复制JAR包 =====
Write-Host "📁 步骤3：创建打包记录目录并复制JAR包" -ForegroundColor Yellow
Write-Host "───────────────────────────────────────────────────────────────────────────────" -ForegroundColor Gray

# 创建builds目录结构
$targetDir = Join-Path $buildDir $dateDir
if (-not (Test-Path $buildDir)) {
    New-Item -ItemType Directory -Path $buildDir | Out-Null
    Write-Host "📁 创建构建目录：$buildDir" -ForegroundColor Cyan
}

if (-not (Test-Path $targetDir)) {
    New-Item -ItemType Directory -Path $targetDir | Out-Null
    Write-Host "📁 创建日期目录：$targetDir" -ForegroundColor Cyan
}

# 生成包名（智能命名：相同阈值使用简化名称）
if ($newBuyThreshold -eq $newSellThreshold) {
    $packageName = "pump${newBuyThreshold}.jar"
} else {
    $packageName = "pump_buy${newBuyThreshold}_sell${newSellThreshold}.jar"
}
$targetJarPath = Join-Path $targetDir $packageName

# 复制JAR包（如果存在则覆盖）
try {
    if (Test-Path $targetJarPath) {
        Write-Host "⚠️  文件已存在，将被覆盖：$packageName" -ForegroundColor Yellow
    }
    Copy-Item $jarPath $targetJarPath -Force
    Write-Host "📦 JAR包已复制：$targetJarPath" -ForegroundColor Green
    
    # 显示文件信息
    $jarInfo = Get-Item $targetJarPath
    Write-Host "📊 包信息：" -ForegroundColor Cyan
    Write-Host "   🏷️  文件名：$packageName"
    Write-Host "   📏 大小：$('{0:N1}' -f ($jarInfo.Length / 1MB)) MB"
    Write-Host "   📅 创建时间：$($jarInfo.CreationTime.ToString('yyyy-MM-dd HH:mm:ss'))"
} catch {
    Write-Host "❌ 复制JAR包失败：$($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""

# ===== 步骤4：生成启动脚本 =====
Write-Host "🚀 步骤4：生成启动脚本" -ForegroundColor Yellow
Write-Host "───────────────────────────────────────────────────────────────────────────────" -ForegroundColor Gray

# 生成启动脚本内容
if ($newBuyThreshold -eq $newSellThreshold) {
    $scriptName = "start_pump${newBuyThreshold}.bat"
} else {
    $scriptName = "start_pump_buy${newBuyThreshold}_sell${newSellThreshold}.bat"
}
$scriptPath = Join-Path $targetDir $scriptName

$thresholdDisplay = if ($newBuyThreshold -eq $newSellThreshold) { "阈值:${newBuyThreshold}美元" } else { "买入:${newBuyThreshold}美元 卖出:${newSellThreshold}美元" }

$startScript = @"
@echo off
chcp 65001 >nul
title PUMP价格监控系统 - $thresholdDisplay
cls

echo ===============================================================================
echo 🚀 PUMP价格监控系统
echo ===============================================================================
echo 📊 配置信息：
echo    💰 买入阈值：${newBuyThreshold} 美元
echo    💸 卖出阈值：${newSellThreshold} 美元  
echo    📦 JAR包：$packageName
echo    📅 构建日期：$dateDir
echo    📅 构建时间：$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
echo ===============================================================================
echo.

echo 🔄 启动中...
java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar "$packageName"

echo.
echo ===============================================================================
echo 程序已退出，按任意键关闭窗口...
pause >nul
"@

try {
    if (Test-Path $scriptPath) {
        Write-Host "⚠️  启动脚本已存在，将被覆盖：$scriptName" -ForegroundColor Yellow
    }
    $startScript | Set-Content $scriptPath -Encoding UTF8
    Write-Host "🚀 启动脚本已生成：$scriptPath" -ForegroundColor Green
} catch {
    Write-Host "❌ 生成启动脚本失败：$($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 生成说明文件
$readmePath = Join-Path $targetDir "README.txt"
$readmeContent = @"
===============================================================================
PUMP价格监控系统 - 构建包 $dateDir
===============================================================================

📦 包内容：
- $packageName           # 可执行JAR包 ($thresholdDisplay)
- $scriptName            # 启动脚本
- README.txt                      # 本说明文件

⚙️ 配置信息：
- 💰 买入阈值：$newBuyThreshold 美元
- 💸 卖出阈值：$newSellThreshold 美元
- 📅 构建日期：$dateDir
- 📅 构建时间：$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

🚀 使用方法：
1. 双击 $scriptName 启动程序
2. 或者使用命令行：java -jar "$packageName"

📋 系统要求：
- Java 8 或以上版本
- Windows 系统

💡 文件命名规则：
- 相同阈值：pump{阈值}.jar （如：pump1.5.jar）
- 不同阈值：pump_buy{买入}_sell{卖出}.jar
- 同名文件将被自动覆盖

===============================================================================
构建脚本：tools\build-and-deploy.ps1
项目地址：$(Get-Location)
===============================================================================
"@

try {
    $readmeContent | Set-Content $readmePath -Encoding UTF8
    Write-Host "📋 说明文件已生成：$readmePath" -ForegroundColor Green
} catch {
    Write-Host "❌ 生成说明文件失败：$($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# ===== 完成总结 =====
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "🎉 构建和部署完成！" -ForegroundColor Green
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "📁 构建目录：$targetDir" -ForegroundColor Cyan
Write-Host "📦 JAR包：$packageName" -ForegroundColor Green  
Write-Host "🚀 启动脚本：$scriptName" -ForegroundColor Green
Write-Host ""
Write-Host "⚙️ 配置信息：" -ForegroundColor Cyan
Write-Host "   💰 买入阈值：$newBuyThreshold 美元"
Write-Host "   💸 卖出阈值：$newSellThreshold 美元"
Write-Host ""
Write-Host "🔗 快速启动："
Write-Host "   cd $targetDir" -ForegroundColor Yellow
Write-Host "   .\$scriptName" -ForegroundColor Yellow
Write-Host ""

# 询问是否立即启动
$launchNow = Read-Host "🚀 是否立即启动程序？(Y/n)"
if ($launchNow -ne 'n' -and $launchNow -ne 'N') {
    Write-Host ""
    Write-Host "🚀 启动程序..." -ForegroundColor Green
    Set-Location $targetDir
    Start-Process $scriptName
    Write-Host "✅ 程序已在新窗口中启动" -ForegroundColor Green
}

Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "🎯 任务完成！感谢使用PUMP价格监控系统构建脚本" -ForegroundColor Green  
Write-Host "===============================================================================" -ForegroundColor Cyan 