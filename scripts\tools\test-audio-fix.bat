@echo off
echo ========================================
echo 测试音频修复效果
echo ========================================

echo.
echo 1. 在项目目录测试...
echo 当前目录: %CD%
echo 启动JAR包（10秒后自动停止）...

start /B java -jar target\pump.jar
timeout /t 10 /nobreak >nul
taskkill /f /im java.exe >nul 2>&1

echo.
echo 2. 在其他目录测试...
mkdir temp-test 2>nul
copy target\pump.jar temp-test\ >nul
cd temp-test

echo 当前目录: %CD%
echo 启动JAR包（10秒后自动停止）...

start /B java -jar pump.jar
timeout /t 10 /nobreak >nul
taskkill /f /im java.exe >nul 2>&1

cd ..
rmdir /s /q temp-test

echo.
echo ========================================
echo 测试完成！
echo.
echo 请检查日志输出：
echo ✅ 应该看到音频播放成功或系统蜂鸣降级
echo ❌ 不应该再看到 "mark/reset not supported" 错误
echo ========================================
pause
