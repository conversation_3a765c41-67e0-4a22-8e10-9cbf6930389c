@echo off
REM 强制设置控制台编码为UTF-8以避免乱码
chcp 65001 > nul
echo 🔧 ====== 音频配置更新工具 ======
echo.

cd /d "%~dp0.."

echo 📋 当前可用的音频配置选项：
echo.
echo 1. CUSTOM           - 原版复杂音频播放方法
echo 2. CUSTOM_SIMPLIFIED - 🆕 基于成功实例的简化方法 (推荐)
echo 3. CUSTOM_ENHANCED   - 🆕 增强版多路径方法
echo 4. SYSTEM           - 系统蜂鸣音
echo 5. MULTIPLE         - 多重蜂鸣音
echo.

set /p choice="请选择音频方法 (1-5): "

if "%choice%"=="1" (
    set soundType=CUSTOM
    echo 📢 已选择：原版复杂音频播放方法
) else if "%choice%"=="2" (
    set soundType=CUSTOM_SIMPLIFIED
    echo 🎵 已选择：简化版音频播放方法 ^(推荐^)
) else if "%choice%"=="3" (
    set soundType=CUSTOM_ENHANCED
    echo 🔧 已选择：增强版多路径音频播放方法
) else if "%choice%"=="4" (
    set soundType=SYSTEM
    echo 📢 已选择：系统蜂鸣音
) else if "%choice%"=="5" (
    set soundType=MULTIPLE
    echo 🔔 已选择：多重蜂鸣音
) else (
    echo ❌ 无效选择，使用默认的简化方法
    set soundType=CUSTOM_SIMPLIFIED
)

echo.
echo 🔄 正在更新配置文件...

REM 备份原配置文件
copy "src\main\resources\pump-config.json" "src\main\resources\pump-config.json.backup.%date:~0,4%%date:~5,2%%date:~8,2%" > nul

REM 使用PowerShell更新JSON配置
powershell -Command "& {
    $configPath = 'src/main/resources/pump-config.json'
    $config = Get-Content $configPath | ConvertFrom-Json
    $config.alertConfig.soundType = '%soundType%'
    $config | ConvertTo-Json -Depth 10 | Set-Content $configPath -Encoding UTF8
    Write-Host '✅ 配置文件已更新'
}"

echo.
echo 📄 当前配置内容：
type "src\main\resources\pump-config.json"

echo.
echo 🧪 要立即测试新配置吗？ (y/n)
set /p test="请选择: "

if /i "%test%"=="y" (
    echo.
    echo 🎵 启动音频测试...
    call "tools\test-simplified-audio.bat"
) else (
    echo.
    echo 💡 提示：你可以随时运行以下命令测试音频：
    echo    tools\test-simplified-audio.bat
    echo.
    echo 🚀 或者启动PUMP系统测试实际效果：
    echo    pump30.bat
)

echo.
echo 🏁 配置更新完成！
pause 