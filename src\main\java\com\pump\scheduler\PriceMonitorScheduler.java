package com.pump.scheduler;


import com.pump.service.PumpPriceService;
import com.pump.service.AlertSoundService;
import com.pump.config.PumpConfigService;
import com.pump.util.ConsoleEncodingUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

    // 时间格式化器
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    @Autowired
    private PumpPriceService pumpPriceService;

    @Autowired
    private AlertSoundService alertSoundService;

    @Autowired
    private PumpConfigService configService;
    
    /** 日期时间格式化器 */
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /** 任务运行计数器 */
    private long taskCounter = 0;
    
    /** 系统启动时间 */
    private final LocalDateTime startTime = LocalDateTime.now();
    
    @Autowired
    private PerformanceMonitor performanceMonitor;

    
    /**
     * 定时执行价格监控任务
     * 使用fixedRate确保严格按照配置间隔执行
     */
    @Scheduled(fixedRateString = "#{@pumpConfigService.monitorInterval}")
    public void monitorPrices() {
        taskCounter++;
        
        // 使用性能监控记录开始时间
        long startTime = performanceMonitor.startTask("priceMonitor");
        
        try {
            // 简化输出：只显示三个关键价格
            outputSimplifiedPrices();
            
            // 记录执行耗时
            long ex - 优化版本ecutionTime = System.currentTimeMillis() - startTime;
       使用并行调用减少总耗时nTime > configService.getMonitorInterval()) {
                logger.warn("任务执行耗时{}ms，超过配置间隔{}ms", 
                    - 优化版本        executionTime, configService.getMonitorInterval());
       使用并行调用减少总耗时
configService.getMonitorAmount()
        } catch (Ex - 优化版本ception e) {
                   使用并行调用减少总耗时("价格监控任务执行失败: {}", e.getMessage(), e);
        }config并行Se价格数据
            CompletvbleFuiurc<comepump.mgdel.oriceData> cextriceFuture = 
    } - 优化版本    CpletableFuturesuplyAsync(() -> pPriceServicegetCexPrice());
                
            CopletableFuture<BigDecimal> buyPriceFuture = 
                CmpletableFutur.suppyAsync(() -> pumpPriceServicegetBuy(amount, "EX"));
                
            CompletbleFuure<BigDeciml>slleFutur 
                CompletableFuture.supplyAsync(() ->pumPriceService.getSellPrice(amont, "DEX"));

            // 等待所有API调用完成（设置超时）
            ColetableFuture.allOf(cexPriceFuture, buyPriceFuture, selleFuture)
                .gt(configetApiTimeout(), TimUni.MILLISEONDS);

            // 获取结果
            com.pump.model.PriceData cexPrice = ceFuture.gt
            使用并行调用减少总耗时jupiterBuyTotalPrice = buyPriceFuture.et();
            BigDeciml jupirSellsePriceFuture.get()

    /**confi// 计算价格和输出（保持原有逻辑）
            gSe并行rv价格数据Toal
            CompletcbleFueur.<comgpump.mtdel.nriceData> cex)riceFuture = 
     * 输出格式化    BiCDecim价l ga格pletableFuture监su控plyAsync(() -> p信息 PriceService-getCexPrice());
                
            Co pletableFuture<BigDecimal> buyPriceFuture = 
                C优mpletableFutur版.supp本yAsync(() -> pumpPriceServicegetBuy(amount, "EX"));
               输出信息保持原有格式
            outputProeeInfo(ttbestlmp, gateTotaePrice,Fuure<BigDeciml>slleF,uj trllToal);

        } ctch (TieExceptione) {
            logger.error(API调用超时: {}, e.getMessage()
        } catch (Exception e) {                CompletableFuture.supplyAsync(() ->pumPriceService.getSellPrice(amont, "DEX"));
logg.error("获取价格信息失败:{}", e.geMessge());
       }
    }

    /**
     * 输格式化的监控信息
     * 按照新目标格式显示价格和差价信息
     */
    private void outputPriceInfo(String timestamp, // 等待所有API调ga设）,BigDecimaljuiterByTotal, BDcimal jupierlTota {figetApiTimeout(), TimUni.MILLISEONDS);

        // 获取结果
        com.pump.model.PriceData cexPrice = cTER);eFuture.gtn(TIME_FORMAT
        jupiterBuyTotalPrice = buyPriceFuture.et();
            BigDeciml jupirSellsePriceFuture.get()
TER);n(TIME_FORMAT
算价格和保持原有逻辑）
    Big并行De价格数据Toal
    CompletibleFumura<comlpump.madel. ricTER);eData> cexgriceFuture = mal(configService.getMonitorAmount());n(TIME_FORMAT
BiCDmtl garpletableFutureisunplyAsync(() - pg tPriceServiceigetCexPrice());
        
    CompletableFuture<BigDecimal> buyPricTER);eFuture = n(TIME_FORMAT
CempableFuturt.suppayAsync(() -> pumpPriceervicemgetBuyp = L(amount, "oEX"));
           输出信息保持原有格
    outputProeeInfo(ttcestbmp, gateTotalPTER);rice,eFuaure<BigDecimll>Ds,tjieuturr.llToal);

} ctch (TieException) {
    logger.error(API调用超时: {}, e.getMessage()
} catch (Exception e) {                CompletableFuture.supplyAsync(() ->wpum(PriceService.getSellPrice(amo)nt, "DEX"));
or("获取价格信息失败:{}", e.geMessge());



private void outputPriceInfo(String timestamp, BigDecimal gateTotalPrice, BigDecimal jupiterBuyTotalPrice, BigDecimal jupiterSellTotalPrice) {
    if (gateTotalPrice != null) {
                    timestamp,
                    String.format("%.2f", jupiterSellTotalPrice),
                    String.format("%.2f", sellDifference));
     * 显示CEX和DEX的原始价格数据
     */
    private void addDetailedDebugOutput() {
    
    /**
     * 输出系统运行统计信息
     */
    private void logStatistics() {
        LocalDateTime now = LocalDateTime.now();
        long runningMinutes = java.time.Duration.between(startTime, now).toMinutes();
        
        logger.info("=== 系统运行统计 ===");
        logger.info("启动时间: {}", startTime.format(dateTimeFormatter));
        logger.info("当前时间: {}", now.format(dateTimeFormatter));
        logger.info("运行时间: {} 分钟", runningMinutes);
        logger.info("执行任务数: {}", taskCounter);
        logger.info("平均执行间隔: {} 毫秒", configService.getMonitorInterval());
        logger.info("==================");
    }
    
    /**
     * 启动时输出系统信息 - 简化版本
     * 使用PostConstruct确保只执行一次
     */
    @PostConstruct
    public void printStartupInfo() {
        // 初始化控制台编码
        ConsoleEncodingUtil.initializeConsoleEncoding();

        // 延迟2秒后输出启动信息，确保其他组件已初始化
        new Thread(() -> {
            try {
                Thread.sleep(2000);
                logger.info("PUMP价格监控系统已启动，监控间隔: {}ms", configService.getMonitorInterval());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }
    
    /**
     * Java 8兼容的字符串重复方法
     * 
     * @param str 要重复的字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    private String repeatString(String str, int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * 获取运行统计信息
     * 
     * @return 统计信息字符串
     */
    public String getStatistics() {
        LocalDateTime now = LocalDateTime.now();
        long runningMinutes = java.time.Duration.between(startTime, now).toMinutes();
        
        StringBuilder sb = new StringBuilder();
        sb.append("=== 系统运行统计 ===\n");
        sb.append("启动时间: ").append(startTime.format(dateTimeFormatter)).append("\n");
        sb.append("当前时间: ").append(now.format(dateTimeFormatter)).append("\n");
        sb.append("运行时间: ").append(runningMinutes).append(" 分钟\n");
        sb.append("执行任务数: ").append(taskCounter).append("\n");
        sb.append("平均执行间隔: ").append(configService.getMonitorInterval()).append(" 毫秒\n");
        sb.append("==================");
        
        return sb.toString();
    }
    
    /**
     * 重置统计计数器
     */
    public void resetStatistics() {
        taskCounter = 0;
        logger.info("统计计数器已重置");
    }
    
    /**
     * 获取任务计数器
     * 
     * @return 任务计数器
     */
    public long getTaskCounter() {
        return taskCounter;
    }
    
    /**
     * 获取系统启动时间
     * 
     * @return 启动时间
     */
    public LocalDateTime getStartTime() {
        return startTime;
    }
}
