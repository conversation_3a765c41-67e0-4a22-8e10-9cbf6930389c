package com.pump;

import com.pump.service.AlertSoundService;
import com.pump.config.PumpConfigService;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

/**
 * AlertSoundService方法调用测试
 * 验证方法是否被正确调用和执行
 */
@Configuration
@ComponentScan(basePackages = "com.pump")
public class AlertSoundServiceTest {

    public static void main(String[] args) {
        System.out.println("🎵 开始AlertSoundService方法调用测试...");

        try {
            // 创建Spring应用上下文
            AnnotationConfigApplicationContext context = new AnnotationConfigApplicationContext(AlertSoundServiceTest.class);

            // 获取AlertSoundService bean
            AlertSoundService alertSoundService = context.getBean(AlertSoundService.class);
            PumpConfigService configService = context.getBean(PumpConfigService.class);

            System.out.println("✅ Spring上下文创建成功");
            System.out.println("✅ AlertSoundService注入成功: " + alertSoundService.getClass().getName());
            System.out.println("✅ PumpConfigService注入成功: " + configService.getClass().getName());

            // 显示当前配置
            System.out.println("📋 当前配置:");
            System.out.println("  - 告警启用: " + configService.isAlertEnabled());
            System.out.println("  - 买入阈值: $" + configService.getBuyThreshold());
            System.out.println("  - 卖出阈值: $" + configService.getSellThreshold());
            System.out.println("  - 音频类型: " + configService.getSoundType());

            // 测试买入告警 - 超过阈值
            System.out.println("\n🔊 测试买入告警 (差价=$5.00, 超过阈值$1.00):");
            BigDecimal buyDifference = new BigDecimal("5.00");
            BigDecimal jupiterBuyPrice = new BigDecimal("6700.00");
            BigDecimal gatePrice = new BigDecimal("6705.00");

            System.out.println("📞 即将调用alertSoundService.checkBuyAlert()");
            alertSoundService.checkBuyAlert(buyDifference, jupiterBuyPrice, gatePrice);
            System.out.println("✅ alertSoundService.checkBuyAlert()调用完成");

            // 等待3秒
            Thread.sleep(3000);

            // 测试卖出告警 - 超过阈值
            System.out.println("\n🔊 测试卖出告警 (差价=$3.50, 超过阈值$1.00):");
            BigDecimal sellDifference = new BigDecimal("3.50");
            BigDecimal jupiterSellPrice = new BigDecimal("6708.50");

            System.out.println("📞 即将调用alertSoundService.checkSellAlert()");
            alertSoundService.checkSellAlert(sellDifference, jupiterSellPrice, gatePrice);
            System.out.println("✅ alertSoundService.checkSellAlert()调用完成");

            System.out.println("\n✅ AlertSoundService方法调用测试完成");

            context.close();

        } catch (Exception e) {
            System.err.println("❌ AlertSoundService方法调用测试失败");
            e.printStackTrace();
        }
    }
}
