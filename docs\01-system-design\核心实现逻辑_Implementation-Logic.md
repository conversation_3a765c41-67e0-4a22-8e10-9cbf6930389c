---
title: "PUMP代币价格监控系统核心实现逻辑"
author: "开发工程师"
created: "2025-01-16"
updated: "2025-01-16"
version: "1.0"
category: "system-design"
tags: ["implementation", "business-logic", "price-monitoring"]
status: "active"
---

# PUMP代币价格监控系统核心实现逻辑

## 📋 功能说明

### 核心业务逻辑
系统通过定时调度器每2秒执行价格监控任务，获取Gate.io CEX价格和Jupiter DEX价格，计算价格差异，并在满足条件时触发音频告警。

### 关键算法
- **价格差异计算**: 买入差价 = Gate.io总价 - Jupiter买入总价
- **告警触发逻辑**: 差价超过阈值($30)时播放对应音频文件
- **缓存优化策略**: 30秒TTL的价格数据缓存机制

## 🛠️ 实现方式

### 价格监控流程
```java
@Scheduled(fixedDelay = 1000)
public void monitorPrices() {
    // 动态间隔控制
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastExecutionTime < configService.getMonitorInterval()) {
        return;
    }
    lastExecutionTime = currentTime;
    
    // 执行价格监控
    outputSimplifiedPrices();
}
```

### 差价计算实现
```java
private void outputSimplifiedPrices() {
    BigDecimal amount = new BigDecimal("1000000"); // 100万个PUMP
    
    // 获取Gate.io价格
    PriceData cexPrice = pumpPriceService.getCexPrice();
    BigDecimal gateTotalPrice = cexPrice.getLastPrice().multiply(amount);
    
    // 获取Jupiter买入价格
    BigDecimal jupiterBuyTotalPrice = pumpPriceService.getDexBuyPrice(amount);
    
    // 计算买入差价
    BigDecimal buyDifference = gateTotalPrice.subtract(jupiterBuyTotalPrice);
    
    // 检查告警条件
    alertSoundService.checkBuyAlert(buyDifference, jupiterBuyTotalPrice, gateTotalPrice);
}
```

### 音频告警机制
```java
public void checkBuyAlert(BigDecimal buyDifference, BigDecimal jupiterPrice, BigDecimal gatePrice) {
    BigDecimal buyThreshold = new BigDecimal(configService.getBuyThreshold());
    
    if (buyDifference.compareTo(buyThreshold) > 0 && !isBuyAudioPlaying) {
        triggerAlert(AlertType.BUY_OPPORTUNITY, "买入机会告警");
    }
}

private void triggerAlert(AlertType alertType, String message) {
    CompletableFuture.runAsync(() -> {
        try {
            String audioFile = (alertType == AlertType.BUY_OPPORTUNITY) ? "up.wav" : "down.wav";
            playCustomSound(audioFile);
        } finally {
            resetPlayingStatus(alertType);
        }
    });
}
```

## 📊 图示说明

### 价格监控状态机
```mermaid
stateDiagram-v2
    [*] --> 空闲状态
    空闲状态 --> 获取价格: 定时触发
    获取价格 --> 计算差价: 价格获取成功
    获取价格 --> 空闲状态: 价格获取失败
    计算差价 --> 检查告警: 差价计算完成
    检查告警 --> 播放告警: 差价超过阈值
    检查告警 --> 输出信息: 差价未超过阈值
    播放告警 --> 输出信息: 告警播放完成
    输出信息 --> 空闲状态: 等待下次执行
```

### API调用重试机制
```mermaid
flowchart TD
    A[发送API请求] --> B{请求成功?}
    B -->|是| C[解析响应数据]
    B -->|否| D{是否429限流?}
    D -->|是| E[等待2秒]
    D -->|否| F{重试次数<3?}
    E --> G[重新发送请求]
    F -->|是| H[指数退避延迟]
    F -->|否| I[返回失败]
    H --> G
    G --> B
    C --> J[返回成功结果]
```

### 缓存机制流程
```mermaid
graph LR
    A[API请求] --> B{检查缓存}
    B -->|命中| C[返回缓存数据]
    B -->|未命中| D[调用外部API]
    D --> E{API调用成功?}
    E -->|是| F[存入缓存]
    E -->|否| G[返回错误]
    F --> H[设置TTL=30s]
    H --> I[返回数据]
    
    style C fill:#e8f5e8
    style G fill:#ffebee
    style I fill:#e8f5e8
```

## ⚙️ 配置示例

### 重试配置
```java
// 重试配置常量
private static final int MAX_RETRIES = 3;
private static final long BASE_RETRY_DELAY = 1000; // 基础延迟1秒
private static final double BACKOFF_MULTIPLIER = 2.0; // 退避倍数
private static final long MAX_RETRY_DELAY = 10000; // 最大延迟10秒

// 指数退避重试实现
for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
        return executeApiCall();
    } catch (Exception e) {
        if (attempt == MAX_RETRIES) {
            throw e;
        }
        
        long delay = Math.min(
            (long)(BASE_RETRY_DELAY * Math.pow(BACKOFF_MULTIPLIER, attempt - 1)),
            MAX_RETRY_DELAY
        );
        Thread.sleep(delay);
    }
}
```

### 缓存配置
```java
@Component
public class PriceCache {
    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    private final long defaultTtl = 30000; // 30秒TTL
    
    public PriceData get(String key) {
        CacheEntry entry = cache.get(key);
        if (entry != null && !entry.isExpired()) {
            return entry.getData();
        }
        return null;
    }
    
    public void put(String key, PriceData data) {
        cache.put(key, new CacheEntry(data, System.currentTimeMillis() + defaultTtl));
    }
}
```

### 编码处理配置
```java
// UTF-8编码强制初始化
public static void forceInitializeUTF8() {
    System.setProperty("file.encoding", "UTF-8");
    System.setProperty("console.encoding", "UTF-8");
    System.setProperty("sun.jnu.encoding", "UTF-8");
    System.setProperty("user.timezone", "Asia/Shanghai");
    
    // 强制设置默认字符集
    Field charset = Charset.class.getDeclaredField("defaultCharset");
    charset.setAccessible(true);
    charset.set(null, StandardCharsets.UTF_8);
}
```

## 📝 更新记录

| 日期 | 版本 | 更新内容 | 作者 |
|------|------|----------|------|
| 2025-01-16 | 1.0 | 初始版本，核心实现逻辑文档 | 开发工程师 |

---

**相关文档**: 
- [系统架构概览](系统架构概览_Architecture-Overview.md)
- [API集成指南](../02-api-integration/API集成指南_Integration-Guide.md)
- [测试策略文档](../05-testing-docs/测试策略文档_Testing-Strategy.md)
