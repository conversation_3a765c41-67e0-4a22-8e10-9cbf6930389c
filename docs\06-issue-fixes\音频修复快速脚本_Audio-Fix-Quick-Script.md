---
title: "音频mark/reset错误快速修复脚本"
author: "音频系统工程师"
created: "2025-01-16"
updated: "2025-01-16"
version: "1.0"
category: "issue-fixes"
tags: ["audio", "quick-fix", "script", "automation"]
status: "active"
priority: "high"
---

# 音频mark/reset错误快速修复脚本

## 📋 功能说明

提供快速修复"mark/reset not supported"错误的自动化脚本和代码片段，可以直接复制粘贴应用到项目中。

## 🛠️ 实现方式

### 快速修复代码片段

#### 1. 最小化修复方案（推荐）
直接替换现有的`playCustomSound`方法：

```java
/**
 * 修复版本的自定义音频播放方法
 * 解决mark/reset not supported问题
 */
private void playCustomSound(AlertType alertType) {
    String audioFile = (alertType == AlertType.BUY_OPPORTUNITY) ? "up.wav" : "down.wav";
    
    try {
        // 1. 加载音频资源
        InputStream rawStream = getClass().getClassLoader().getResourceAsStream(audioFile);
        if (rawStream == null) {
            logger.warn("音频文件不存在: {}, 使用系统蜂鸣", audioFile);
            playSystemBeep(alertType);
            return;
        }
        
        // 2. 关键修复：确保InputStream支持mark/reset
        InputStream audioStream = rawStream.markSupported() ? 
            rawStream : new BufferedInputStream(rawStream, 8192);
        
        // 3. 创建AudioInputStream（现在不会出现mark/reset错误）
        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream);
        
        // 4. 播放音频
        Clip clip = AudioSystem.getClip();
        clip.open(audioInputStream);
        clip.start();
        
        // 5. 等待播放完成
        while (clip.isRunning()) {
            Thread.sleep(100);
        }
        
        // 6. 清理资源
        clip.close();
        audioInputStream.close();
        audioStream.close();
        
        logger.debug("自定义音频播放完成: {}", audioFile);
        
    } catch (Exception e) {
        logger.error("自定义音频播放失败: {}, 降级到系统蜂鸣", e.getMessage());
        playSystemBeep(alertType);
    }
}

/**
 * 系统蜂鸣降级方法
 */
private void playSystemBeep(AlertType alertType) {
    try {
        if (alertType == AlertType.BUY_OPPORTUNITY) {
            // 买入：单次蜂鸣
            Toolkit.getDefaultToolkit().beep();
        } else {
            // 卖出：双次蜂鸣
            Toolkit.getDefaultToolkit().beep();
            Thread.sleep(200);
            Toolkit.getDefaultToolkit().beep();
        }
    } catch (Exception e) {
        logger.error("系统蜂鸣也失败了", e);
    }
}
```

#### 2. 需要添加的import语句
在AlertSoundService.java文件顶部添加：

```java
import java.io.BufferedInputStream;
import javax.sound.sampled.DataLine;
import javax.sound.sampled.UnsupportedAudioFileException;
```

### 一键修复脚本

#### Windows PowerShell脚本
```powershell
# audio-fix.ps1 - 一键修复音频问题
Write-Host "🔧 开始修复音频mark/reset问题..." -ForegroundColor Yellow

# 1. 备份原文件
$sourceFile = "src\main\java\com\pump\service\AlertSoundService.java"
$backupFile = "$sourceFile.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"

if (Test-Path $sourceFile) {
    Copy-Item $sourceFile $backupFile
    Write-Host "✅ 已备份原文件到: $backupFile" -ForegroundColor Green
} else {
    Write-Host "❌ 找不到源文件: $sourceFile" -ForegroundColor Red
    exit 1
}

# 2. 应用修复
$fixedContent = @"
// 修复后的playCustomSound方法
private void playCustomSound(AlertType alertType) {
    String audioFile = (alertType == AlertType.BUY_OPPORTUNITY) ? "up.wav" : "down.wav";
    
    try {
        InputStream rawStream = getClass().getClassLoader().getResourceAsStream(audioFile);
        if (rawStream == null) {
            logger.warn("音频文件不存在: {}, 使用系统蜂鸣", audioFile);
            playSystemBeep(alertType);
            return;
        }
        
        // 关键修复：确保支持mark/reset
        InputStream audioStream = rawStream.markSupported() ? 
            rawStream : new BufferedInputStream(rawStream, 8192);
        
        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream);
        Clip clip = AudioSystem.getClip();
        clip.open(audioInputStream);
        clip.start();
        
        while (clip.isRunning()) {
            Thread.sleep(100);
        }
        
        clip.close();
        audioInputStream.close();
        audioStream.close();
        
    } catch (Exception e) {
        logger.error("自定义音频播放失败: {}, 降级到系统蜂鸣", e.getMessage());
        playSystemBeep(alertType);
    }
}

private void playSystemBeep(AlertType alertType) {
    try {
        if (alertType == AlertType.BUY_OPPORTUNITY) {
            Toolkit.getDefaultToolkit().beep();
        } else {
            Toolkit.getDefaultToolkit().beep();
            Thread.sleep(200);
            Toolkit.getDefaultToolkit().beep();
        }
    } catch (Exception e) {
        logger.error("系统蜂鸣也失败了", e);
    }
}
"@

Write-Host "📝 应用代码修复..." -ForegroundColor Cyan
# 这里需要手动替换代码，或者使用更复杂的文本处理

# 3. 重新编译
Write-Host "🔨 重新编译项目..." -ForegroundColor Cyan
mvn clean compile

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 编译成功" -ForegroundColor Green
    
    # 4. 打包
    Write-Host "📦 打包JAR..." -ForegroundColor Cyan
    mvn package -DskipTests
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 打包成功，修复完成！" -ForegroundColor Green
        Write-Host "🚀 请测试新的JAR包：java -jar target/pump30.jar" -ForegroundColor Yellow
    } else {
        Write-Host "❌ 打包失败" -ForegroundColor Red
    }
} else {
    Write-Host "❌ 编译失败，请检查代码" -ForegroundColor Red
}
```

#### Linux/macOS Bash脚本
```bash
#!/bin/bash
# audio-fix.sh - 一键修复音频问题

echo "🔧 开始修复音频mark/reset问题..."

# 1. 检查文件存在
SOURCE_FILE="src/main/java/com/pump/service/AlertSoundService.java"
if [ ! -f "$SOURCE_FILE" ]; then
    echo "❌ 找不到源文件: $SOURCE_FILE"
    exit 1
fi

# 2. 备份原文件
BACKUP_FILE="${SOURCE_FILE}.backup.$(date +%Y%m%d-%H%M%S)"
cp "$SOURCE_FILE" "$BACKUP_FILE"
echo "✅ 已备份原文件到: $BACKUP_FILE"

# 3. 提示手动修改
echo "📝 请手动应用以下修复："
echo "1. 在import部分添加: import java.io.BufferedInputStream;"
echo "2. 替换playCustomSound方法为修复版本"
echo "3. 添加playSystemBeep降级方法"

# 4. 等待用户确认
read -p "修改完成后按Enter继续编译..."

# 5. 编译测试
echo "🔨 重新编译项目..."
mvn clean compile

if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
    
    # 6. 打包
    echo "📦 打包JAR..."
    mvn package -DskipTests
    
    if [ $? -eq 0 ]; then
        echo "✅ 打包成功，修复完成！"
        echo "🚀 请测试新的JAR包：java -jar target/pump30.jar"
    else
        echo "❌ 打包失败"
    fi
else
    echo "❌ 编译失败，请检查代码"
fi
```

### 验证修复效果

#### 测试脚本
```bash
#!/bin/bash
# test-audio-fix.sh - 测试音频修复效果

echo "🧪 测试音频修复效果..."

# 1. 在项目目录测试
echo "1. 在项目目录测试..."
java -jar target/pump30.jar &
PID1=$!
sleep 10
kill $PID1

# 2. 在其他目录测试
echo "2. 在其他目录测试..."
mkdir -p /tmp/pump-test
cp target/pump30.jar /tmp/pump-test/
cd /tmp/pump-test

java -jar pump30.jar &
PID2=$!
sleep 10
kill $PID2

echo "✅ 测试完成，请检查日志输出"
echo "   - 应该看到音频播放成功或系统蜂鸣降级"
echo "   - 不应该再看到'mark/reset not supported'错误"

# 清理
cd - > /dev/null
rm -rf /tmp/pump-test
```

## ⚙️ 配置示例

### 验证修复的日志输出
修复成功后，应该看到类似的日志：

```
✅ 正常情况：
2025-01-16 10:30:15.123 DEBUG - 自定义音频播放完成: up.wav

✅ 降级情况：
2025-01-16 10:30:15.123 WARN  - 音频文件不存在: up.wav, 使用系统蜂鸣
2025-01-16 10:30:15.124 INFO  - 使用系统蜂鸣器播放告警: BUY_OPPORTUNITY

❌ 修复前的错误（不应该再出现）：
2025-01-16 10:30:15.123 ERROR - 播放自定义音频失败: mark/reset not supported
```

### 快速检查清单
- [ ] 添加了BufferedInputStream import
- [ ] 修改了playCustomSound方法
- [ ] 添加了playSystemBeep降级方法
- [ ] 编译无错误
- [ ] 在不同目录测试JAR包
- [ ] 确认不再出现mark/reset错误
- [ ] 音频播放正常或正确降级

## 📝 更新记录

| 日期 | 版本 | 更新内容 | 作者 |
|------|------|----------|------|
| 2025-01-16 | 1.0 | 初始版本，快速修复脚本 | 音频系统工程师 |

---

**相关文档**: 
- [音频mark-reset错误修复](音频mark-reset错误修复_Audio-Mark-Reset-Fix.md)
- [音频告警跨平台问题](音频告警跨平台问题_Audio-Alert-Cross-Platform-Issue.md)
- [故障排查指南](故障排查指南_Troubleshooting-Guide.md)
