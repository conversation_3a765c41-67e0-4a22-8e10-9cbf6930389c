package com.pump.util;

import java.io.IOException;
import java.io.PrintStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;

/**
 * 控制台编码工具类
 * 处理控制台输出的UTF-8编码问题
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class ConsoleEncodingUtil {
    
    private static boolean initialized = false;
    
    /**
     * 初始化控制台UTF-8编码
     * 只执行一次初始化
     */
    public static synchronized void initializeConsoleEncoding() {
        if (initialized) {
            return;
        }
        
        try {
            // 设置系统编码属性
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("console.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");
            
            // 重新设置标准输出流为UTF-8
            System.setOut(new PrintStream(System.out, true, StandardCharsets.UTF_8.name()));
            System.setErr(new PrintStream(System.err, true, StandardCharsets.UTF_8.name()));
            
            // 在Windows系统上尝试设置控制台代码页
            if (isWindows()) {
                setWindowsConsoleToUTF8();
            }
            
            initialized = true;
            
        } catch (Exception e) {
            System.err.println("初始化控制台编码失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否为Windows系统
     */
    private static boolean isWindows() {
        return System.getProperty("os.name").toLowerCase().contains("windows");
    }
    
    /**
     * 设置Windows控制台为UTF-8编码
     */
    private static void setWindowsConsoleToUTF8() {
        try {
            // 执行chcp 65001命令设置控制台为UTF-8
            ProcessBuilder pb = new ProcessBuilder("cmd", "/c", "chcp", "65001");
            pb.redirectErrorStream(true);
            Process process = pb.start();
            process.waitFor();
            
        } catch (IOException | InterruptedException e) {
            // 忽略错误，不影响程序运行
        }
    }
    
    /**
     * 输出UTF-8编码的文本到控制台
     * 
     * @param text 要输出的文本
     */
    public static void printUTF8(String text) {
        try {
            byte[] bytes = text.getBytes(StandardCharsets.UTF_8);
            System.out.write(bytes);
            System.out.flush();
        } catch (IOException e) {
            // 回退到普通输出
            System.out.print(text);
        }
    }
    
    /**
     * 输出UTF-8编码的文本行到控制台
     * 
     * @param text 要输出的文本
     */
    public static void printlnUTF8(String text) {
        printUTF8(text + System.lineSeparator());
    }
    
    /**
     * 获取当前系统的默认编码
     * 
     * @return 默认编码名称
     */
    public static String getDefaultEncoding() {
        return System.getProperty("file.encoding", "Unknown");
    }
    
    /**
     * 检查编码是否已正确配置
     * 
     * @return true如果编码配置正确
     */
    public static boolean isEncodingConfigured() {
        return "UTF-8".equals(System.getProperty("file.encoding")) && initialized;
    }
}
