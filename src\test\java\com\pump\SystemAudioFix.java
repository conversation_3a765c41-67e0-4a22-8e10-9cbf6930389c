package com.pump;

import javax.sound.sampled.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 系统级音频修复工具
 * 最后的尝试 - 绕过Java音频系统直接操作系统
 */
public class SystemAudioFix {

    public static void main(String[] args) {
        System.out.println("🛠️ ========== 系统级音频修复 ==========");
        System.out.println("📢 最后的尝试 - 绕过Java音频系统...");
        System.out.println();

        // 1. 生成WAV文件并用系统播放器播放
        trySystemWavPlayer();
        
        // 2. 尝试调用Windows系统音频API
        tryWindowsSystemAudio();
        
        // 3. 检查并修复Java音频配置
        tryFixJavaAudioConfig();
        
        // 4. 提供最终解决方案
        provideFinalSolution();
        
        System.out.println("🛠️ ========== 系统修复完成 ==========");
    }

    /**
     * 生成WAV文件并用系统播放器播放
     */
    private static void trySystemWavPlayer() {
        System.out.println("🎵 === 尝试系统WAV播放器 ===");
        
        try {
            // 生成WAV文件
            String fileName = "test_audio.wav";
            File wavFile = new File(fileName);
            
            System.out.println("📝 生成WAV文件: " + wavFile.getAbsolutePath());
            generateWavFile(wavFile, 440.0, 2);
            
            if (wavFile.exists()) {
                System.out.println("✅ WAV文件生成成功，大小: " + wavFile.length() + " bytes");
                
                // 尝试用Windows媒体播放器播放
                System.out.println("🔊 尝试用系统播放器播放...");
                
                try {
                    ProcessBuilder pb = new ProcessBuilder("cmd", "/c", "start", "", fileName);
                    Process process = pb.start();
                    
                    System.out.println("✅ 已启动系统播放器");
                    System.out.println("📢 请检查是否有声音！如果有声音，说明硬件正常，问题在Java音频系统");
                    
                    // 等待5秒
                    Thread.sleep(5000);
                    
                } catch (Exception e) {
                    System.out.println("❌ 系统播放器启动失败: " + e.getMessage());
                }
                
                // 清理文件
                if (wavFile.delete()) {
                    System.out.println("🧹 临时文件已清理");
                }
                
            } else {
                System.out.println("❌ WAV文件生成失败");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 系统WAV播放器测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 尝试调用Windows系统音频API
     */
    private static void tryWindowsSystemAudio() {
        System.out.println("\n🪟 === 尝试Windows系统音频 ===");
        
        try {
            // 尝试播放Windows系统声音
            System.out.println("🔊 尝试播放Windows系统声音...");
            
            String[] systemSounds = {
                "SystemHand",           // 系统错误声
                "SystemQuestion",       // 系统问题声
                "SystemExclamation",    // 系统感叹声
                "SystemAsterisk",       // 系统星号声
                "SystemExit"            // 系统退出声
            };
            
            for (String sound : systemSounds) {
                try {
                    System.out.println("🎵 播放系统声音: " + sound);
                    
                    ProcessBuilder pb = new ProcessBuilder("powershell", "-Command", 
                        "[System.Media.SystemSounds]::" + sound + ".Play()");
                    Process process = pb.start();
                    process.waitFor();
                    
                    Thread.sleep(1000);
                    
                } catch (Exception e) {
                    System.out.println("⚠️ 系统声音播放失败: " + sound + " - " + e.getMessage());
                }
            }
            
            // 尝试用PowerShell播放蜂鸣声
            System.out.println("🔔 尝试PowerShell蜂鸣声...");
            try {
                ProcessBuilder pb = new ProcessBuilder("powershell", "-Command", 
                    "for($i=1; $i -le 5; $i++) { [Console]::Beep(800, 300); Start-Sleep -Milliseconds 200 }");
                Process process = pb.start();
                process.waitFor();
                
                System.out.println("✅ PowerShell蜂鸣完成");
                
            } catch (Exception e) {
                System.out.println("❌ PowerShell蜂鸣失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            System.out.println("❌ Windows系统音频测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查并修复Java音频配置
     */
    private static void tryFixJavaAudioConfig() {
        System.out.println("\n☕ === 修复Java音频配置 ===");
        
        try {
            // 显示当前Java音频属性
            System.out.println("📋 当前Java音频属性:");
            
            String[] audioProps = {
                "javax.sound.sampled.Clip",
                "javax.sound.sampled.Port", 
                "javax.sound.sampled.SourceDataLine",
                "javax.sound.sampled.TargetDataLine",
                "javax.sound.sampled.Mixer"
            };
            
            for (String prop : audioProps) {
                String value = System.getProperty(prop);
                System.out.println("   " + prop + " = " + (value != null ? value : "未设置"));
            }
            
            // 尝试设置音频属性
            System.out.println("\n🔧 尝试设置音频属性...");
            
            System.setProperty("javax.sound.sampled.Clip", "com.sun.media.sound.DirectAudioDeviceProvider");
            System.setProperty("javax.sound.sampled.Port", "com.sun.media.sound.PortMixerProvider");
            System.setProperty("javax.sound.sampled.SourceDataLine", "com.sun.media.sound.DirectAudioDeviceProvider");
            System.setProperty("javax.sound.sampled.TargetDataLine", "com.sun.media.sound.DirectAudioDeviceProvider");
            
            System.out.println("✅ 音频属性设置完成");
            
            // 重新测试音频
            System.out.println("\n🔊 重新测试Java音频...");
            testJavaAudioAfterFix();
            
        } catch (Exception e) {
            System.out.println("❌ Java音频配置修复失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 修复后重新测试Java音频
     */
    private static void testJavaAudioAfterFix() {
        try {
            // 重新获取音频设备
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            System.out.println("📊 重新检测到音频设备: " + mixers.length + " 个");
            
            // 尝试播放测试音频
            byte[] audioData = generateTestTone(440.0, 1, 44100);
            
            AudioFormat format = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                44100, 16, 1, 2, 44100, false
            );
            
            // 尝试每个设备
            for (Mixer.Info mixerInfo : mixers) {
                try {
                    Mixer mixer = AudioSystem.getMixer(mixerInfo);
                    DataLine.Info info = new DataLine.Info(SourceDataLine.class, format);
                    
                    if (mixer.isLineSupported(info)) {
                        System.out.println("🎧 测试设备: " + mixerInfo.getName());
                        
                        SourceDataLine line = (SourceDataLine) mixer.getLine(info);
                        line.open(format);
                        line.start();
                        
                        System.out.println("🔊 播放测试音频 - 请注意听！");
                        line.write(audioData, 0, audioData.length);
                        
                        line.drain();
                        line.stop();
                        line.close();
                        
                        System.out.println("✅ 设备测试完成");
                        
                        // 只测试第一个可用设备
                        break;
                    }
                    
                } catch (Exception e) {
                    System.out.println("⚠️ 设备测试失败: " + mixerInfo.getName());
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ 修复后测试失败: " + e.getMessage());
        }
    }

    /**
     * 提供最终解决方案
     */
    private static void provideFinalSolution() {
        System.out.println("\n💡 === 最终解决方案 ===");
        System.out.println("如果以上所有方法都没有声音，问题可能是：");
        System.out.println();
        
        System.out.println("🔧 1. 硬件/驱动问题:");
        System.out.println("   - 音频驱动程序损坏或过时");
        System.out.println("   - 音频设备硬件故障");
        System.out.println("   - Windows音频服务配置错误");
        System.out.println();
        
        System.out.println("🔧 2. 系统配置问题:");
        System.out.println("   - Windows音频独占模式被启用");
        System.out.println("   - 音频设备被其他程序独占");
        System.out.println("   - 系统音量被完全静音");
        System.out.println();
        
        System.out.println("🔧 3. Java特定问题:");
        System.out.println("   - Java音频子系统被禁用");
        System.out.println("   - JVM音频权限被限制");
        System.out.println("   - 音频库文件损坏");
        System.out.println();
        
        System.out.println("📋 建议的解决步骤:");
        System.out.println("1. 重启计算机");
        System.out.println("2. 更新音频驱动程序");
        System.out.println("3. 在其他程序中测试音频播放");
        System.out.println("4. 尝试不同版本的Java");
        System.out.println("5. 检查Windows音频服务");
        System.out.println("6. 联系系统管理员检查音频权限");
    }

    /**
     * 生成WAV文件
     */
    private static void generateWavFile(File file, double frequency, int durationSeconds) throws IOException {
        int sampleRate = 44100;
        int numSamples = durationSeconds * sampleRate;
        
        try (FileOutputStream fos = new FileOutputStream(file)) {
            // WAV文件头
            writeWavHeader(fos, numSamples, sampleRate);
            
            // 音频数据
            for (int i = 0; i < numSamples; i++) {
                double time = (double) i / sampleRate;
                short sample = (short) (0.7 * Short.MAX_VALUE * Math.sin(2 * Math.PI * frequency * time));
                
                // 写入16位PCM数据 (小端序)
                fos.write(sample & 0xFF);
                fos.write((sample >> 8) & 0xFF);
            }
        }
    }

    /**
     * 写入WAV文件头
     */
    private static void writeWavHeader(FileOutputStream fos, int numSamples, int sampleRate) throws IOException {
        int dataSize = numSamples * 2; // 16位 = 2字节
        int fileSize = 36 + dataSize;
        
        // RIFF头
        fos.write("RIFF".getBytes());
        writeInt(fos, fileSize);
        fos.write("WAVE".getBytes());
        
        // fmt块
        fos.write("fmt ".getBytes());
        writeInt(fos, 16); // fmt块大小
        writeShort(fos, 1); // PCM格式
        writeShort(fos, 1); // 单声道
        writeInt(fos, sampleRate);
        writeInt(fos, sampleRate * 2); // 字节率
        writeShort(fos, 2); // 块对齐
        writeShort(fos, 16); // 位深度
        
        // data块
        fos.write("data".getBytes());
        writeInt(fos, dataSize);
    }

    private static void writeInt(FileOutputStream fos, int value) throws IOException {
        fos.write(value & 0xFF);
        fos.write((value >> 8) & 0xFF);
        fos.write((value >> 16) & 0xFF);
        fos.write((value >> 24) & 0xFF);
    }

    private static void writeShort(FileOutputStream fos, int value) throws IOException {
        fos.write(value & 0xFF);
        fos.write((value >> 8) & 0xFF);
    }

    /**
     * 生成测试音调
     */
    private static byte[] generateTestTone(double frequency, int durationSeconds, int sampleRate) {
        int numSamples = durationSeconds * sampleRate;
        byte[] audioData = new byte[numSamples * 2];
        
        for (int i = 0; i < numSamples; i++) {
            double time = (double) i / sampleRate;
            short sample = (short) (0.7 * Short.MAX_VALUE * Math.sin(2 * Math.PI * frequency * time));
            
            audioData[i * 2] = (byte) (sample & 0xFF);
            audioData[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
        }
        
        return audioData;
    }
}
