---
title: "PUMP代币价格监控系统监控告警配置"
author: "监控工程师"
created: "2025-01-16"
updated: "2025-01-16"
version: "1.0"
category: "monitoring-alerting"
tags: ["monitoring", "alerting", "audio", "configuration"]
status: "active"
---

# PUMP代币价格监控系统监控告警配置

## 📋 功能说明

### 监控告警概述
系统提供基于价格差异阈值的智能音频告警功能，当检测到套利机会时自动播放对应的音频文件，帮助用户及时发现交易机会。

### 告警类型
- **买入告警**: 当Gate.io价格高于Jupiter买入价格超过阈值时触发
- **卖出告警**: 当Jupiter卖出价格高于Gate.io价格超过阈值时触发
- **系统告警**: API调用失败、网络异常等系统级告警

### 告警特性
- 多种音频模式支持（系统蜂鸣、自定义音频、多重蜂鸣）
- 冷却机制防止重复播放
- 异步播放不阻塞主流程
- 可配置的阈值和间隔

## 🛠️ 实现方式

### 告警服务核心实现
```java
@Service
public class AlertSoundService {
    
    /**
     * 检查买入告警条件
     */
    public void checkBuyAlert(BigDecimal buyDifference, BigDecimal jupiterPrice, BigDecimal gatePrice) {
        BigDecimal buyThreshold = new BigDecimal(configService.getBuyThreshold());
        
        if (buyDifference.compareTo(buyThreshold) > 0 && !isBuyAudioPlaying) {
            String message = String.format("买入机会: Gate.io($%.2f) - Jupiter($%.2f) = $%.2f > $%.2f",
                gatePrice, jupiterPrice, buyDifference, buyThreshold);
            
            triggerAlert(AlertType.BUY_OPPORTUNITY, message);
        }
    }
    
    /**
     * 检查卖出告警条件
     */
    public void checkSellAlert(BigDecimal sellDifference, BigDecimal jupiterPrice, BigDecimal gatePrice) {
        BigDecimal sellThreshold = new BigDecimal(configService.getSellThreshold());
        
        if (sellDifference.compareTo(sellThreshold) > 0 && !isSellAudioPlaying) {
            String message = String.format("卖出机会: Jupiter($%.2f) - Gate.io($%.2f) = $%.2f > $%.2f",
                jupiterPrice, gatePrice, sellDifference, sellThreshold);
            
            triggerAlert(AlertType.SELL_OPPORTUNITY, message);
        }
    }
    
    /**
     * 触发告警
     */
    private void triggerAlert(AlertType alertType, String message) {
        CompletableFuture.runAsync(() -> {
            try {
                // 设置播放状态
                setPlayingStatus(alertType, true);
                
                String soundType = configService.getSoundType();
                switch (soundType.toUpperCase()) {
                    case "SYSTEM":
                        playSystemBeep(alertType);
                        break;
                    case "CUSTOM":
                        playCustomSound(alertType);
                        break;
                    case "MULTIPLE":
                        playMultipleBeeps(alertType);
                        break;
                    default:
                        playSystemBeep(alertType);
                }
            } finally {
                // 重置播放状态
                setPlayingStatus(alertType, false);
            }
        });
    }
}
```

### 自定义音频播放实现
```java
/**
 * 播放自定义音频文件
 */
private void playCustomSound(AlertType alertType) {
    try {
        String audioFile = (alertType == AlertType.BUY_OPPORTUNITY) ? "up.wav" : "down.wav";
        
        // 从classpath加载音频文件
        InputStream audioStream = getClass().getClassLoader().getResourceAsStream(audioFile);
        if (audioStream == null) {
            logger.error("音频文件不存在: {}", audioFile);
            return;
        }
        
        // 使用AudioInputStream播放
        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream);
        Clip clip = AudioSystem.getClip();
        clip.open(audioInputStream);
        
        // 播放音频
        clip.start();
        
        // 等待播放完成
        while (clip.isRunning()) {
            Thread.sleep(100);
        }
        
        // 清理资源
        clip.close();
        audioInputStream.close();
        
    } catch (Exception e) {
        logger.error("播放自定义音频失败: {}", e.getMessage());
    }
}
```

## 📊 图示说明

### 告警触发流程
```mermaid
flowchart TD
    A[价格监控任务] --> B[获取价格数据]
    B --> C[计算价格差异]
    C --> D{买入差价检查}
    D -->|差价 > 阈值| E[触发买入告警]
    D -->|差价 ≤ 阈值| F{卖出差价检查}
    
    F -->|差价 > 阈值| G[触发卖出告警]
    F -->|差价 ≤ 阈值| H[继续监控]
    
    E --> I{买入音频播放中?}
    I -->|否| J[播放up.wav]
    I -->|是| H
    
    G --> K{卖出音频播放中?}
    K -->|否| L[播放down.wav]
    K -->|是| H
    
    J --> M[设置播放状态]
    L --> M
    M --> N[异步音频播放]
    N --> O[播放完成]
    O --> P[重置播放状态]
    P --> H
    
    style E fill:#e8f5e8
    style G fill:#fff3e0
    style J fill:#e1f5fe
    style L fill:#f3e5f5
```

### 音频播放状态机
```mermaid
stateDiagram-v2
    [*] --> 空闲状态
    空闲状态 --> 检查告警条件: 价格差异计算完成
    检查告警条件 --> 空闲状态: 条件不满足
    检查告警条件 --> 播放音频: 条件满足且未播放
    检查告警条件 --> 空闲状态: 条件满足但正在播放
    播放音频 --> 设置播放标志: 开始播放
    设置播放标志 --> 异步播放: 音频文件加载
    异步播放 --> 播放完成: 音频播放结束
    播放完成 --> 重置标志: 清理资源
    重置标志 --> 空闲状态: 等待下次检查
```

### 告警配置架构
```mermaid
graph TB
    subgraph "告警配置"
        A[pump-config.json] --> B[AlertSoundService]
        B --> C[告警阈值配置]
        B --> D[音频类型配置]
        B --> E[冷却时间配置]
    end
    
    subgraph "音频资源"
        F[up.wav<br/>买入告警音]
        G[down.wav<br/>卖出告警音]
        H[系统蜂鸣器]
    end
    
    subgraph "告警触发"
        I[买入告警检查]
        J[卖出告警检查]
        K[系统告警检查]
    end
    
    C --> I
    C --> J
    D --> F
    D --> G
    D --> H
    
    I --> F
    J --> G
    K --> H
    
    style B fill:#e1f5fe
    style F fill:#e8f5e8
    style G fill:#fff3e0
```

## ⚙️ 配置示例

### 告警配置 (pump-config.json)
```json
{
  "alert": {
    "enabled": true,
    "buyThreshold": 30.00,
    "sellThreshold": 30.00,
    "soundType": "CUSTOM",
    "continuousPlay": false,
    "cooldownPeriod": 30000,
    "volume": 0.8,
    "comment": "告警配置 - 阈值单位为美元，soundType可选: SYSTEM, MULTIPLE, CUSTOM"
  }
}
```

### Spring Boot配置 (application.properties)
```properties
# 报警音配置
pump.alert.enabled=true
pump.alert.buy-threshold=30.00
pump.alert.sell-threshold=30.00
pump.alert.cooldown=30000
pump.alert.sound-type=CUSTOM
```

### 音频文件配置
```java
// 音频文件映射
private static final Map<AlertType, String> AUDIO_FILES = Map.of(
    AlertType.BUY_OPPORTUNITY, "up.wav",
    AlertType.SELL_OPPORTUNITY, "down.wav"
);

// 音频文件验证
@PostConstruct
public void validateAudioFiles() {
    for (String audioFile : AUDIO_FILES.values()) {
        InputStream audioStream = getClass().getClassLoader().getResourceAsStream(audioFile);
        if (audioStream == null) {
            logger.warn("音频文件不存在: {}", audioFile);
        } else {
            try {
                audioStream.close();
                logger.info("音频文件验证成功: {}", audioFile);
            } catch (IOException e) {
                logger.error("音频文件验证失败: {}", audioFile, e);
            }
        }
    }
}
```

### 系统蜂鸣器配置
```java
/**
 * 播放系统蜂鸣器
 */
private void playSystemBeep(AlertType alertType) {
    try {
        if (alertType == AlertType.BUY_OPPORTUNITY) {
            // 买入告警：单次蜂鸣
            Toolkit.getDefaultToolkit().beep();
        } else {
            // 卖出告警：双次蜂鸣
            Toolkit.getDefaultToolkit().beep();
            Thread.sleep(200);
            Toolkit.getDefaultToolkit().beep();
        }
    } catch (Exception e) {
        logger.error("系统蜂鸣器播放失败", e);
    }
}
```

### 多重蜂鸣配置
```java
/**
 * 播放多重蜂鸣
 */
private void playMultipleBeeps(AlertType alertType) {
    try {
        int beepCount = (alertType == AlertType.BUY_OPPORTUNITY) ? 3 : 2;
        
        for (int i = 0; i < beepCount; i++) {
            Toolkit.getDefaultToolkit().beep();
            if (i < beepCount - 1) {
                Thread.sleep(300);
            }
        }
    } catch (Exception e) {
        logger.error("多重蜂鸣播放失败", e);
    }
}
```

## 📝 更新记录

| 日期 | 版本 | 更新内容 | 作者 |
|------|------|----------|------|
| 2025-01-16 | 1.0 | 初始版本，监控告警配置文档 | 监控工程师 |

---

**相关文档**: 
- [音频告警详解](音频告警详解_Audio-Alert-Details.md)
- [性能监控指标](性能监控指标_Performance-Metrics.md)
- [日志管理策略](日志管理策略_Log-Management.md)
