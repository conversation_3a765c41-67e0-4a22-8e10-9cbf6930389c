package com.pump.client;

import com.pump.model.PriceData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Gate.io API客户端
 * 负责从Gate.io获取PUMP价格数据
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
@Component
public class GateIoApiClient {
    
    private static final Logger logger = LoggerFactory.getLogger(GateIoApiClient.class);
    
    @Value("${gate.api.base-url}")
    private String baseUrl;
    
    @Value("${gate.api.timeout}")
    private int timeout;
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    // 交易对
    private static final String SYMBOL = "PUMP_USDT";
    
    public GateIoApiClient() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 获取PUMP价格数据
     * 
     * @return 价格数据
     */
    public PriceData getPumpPrice() {
        logger.debug("获取Gate.io PUMP价格数据");
        
        try {
            // 构建API URL
            String url = String.format("%s/spot/tickers?currency_pair=%s", baseUrl, SYMBOL);
            
            // 发送请求
            String response = restTemplate.getForObject(url, String.class);
            
            if (response == null || response.isEmpty()) {
                logger.error("Gate.io API返回空响应");
                return createErrorPriceData("API返回空响应");
            }
            
            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(response);
            
            // 检查是否为数组并获取第一个元素
            if (jsonNode.isArray() && jsonNode.size() > 0) {
                JsonNode tickerData = jsonNode.get(0);
                
                // 提取价格数据
                BigDecimal buyPrice = new BigDecimal(tickerData.get("highest_bid").asText());
                BigDecimal sellPrice = new BigDecimal(tickerData.get("lowest_ask").asText());
                BigDecimal lastPrice = new BigDecimal(tickerData.get("last").asText());
                
                // 创建价格数据对象
                PriceData priceData = new PriceData("Gate.io", buyPrice, sellPrice, lastPrice);
                
                // 设置额外信息
                if (tickerData.has("base_volume")) {
                    priceData.setVolume(new BigDecimal(tickerData.get("base_volume").asText()));
                }
                
                logger.debug("Gate.io价格数据获取成功: {}", priceData);
                return priceData;
                
            } else {
                logger.error("Gate.io API返回格式错误或无数据");
                return createErrorPriceData("API返回格式错误或无数据");
            }
            
        } catch (Exception e) {
            logger.error("获取Gate.io价格数据失败", e);
            return createErrorPriceData("获取价格数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单簿深度数据 (用于计算大额交易价格)
     * 
     * @param amount 交易数量
     * @return 实际交易价格
     */
    public BigDecimal getOrderBookPrice(BigDecimal amount, boolean isBuy) {
        logger.debug("获取Gate.io订单簿数据, 数量: {}, 买入: {}", amount, isBuy);
        
        try {
            // 构建订单簿API URL
            String url = String.format("%s/spot/order_book?currency_pair=%s&limit=10", baseUrl, SYMBOL);
            
            // 发送请求
            String response = restTemplate.getForObject(url, String.class);
            
            if (response == null || response.isEmpty()) {
                logger.warn("Gate.io订单簿API返回空响应，使用ticker价格");
                return null;
            }
            
            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(response);
            
            // 根据买入/卖出选择相应的订单簿
            JsonNode orders = isBuy ? jsonNode.get("asks") : jsonNode.get("bids");
            
            if (orders == null || orders.size() == 0) {
                logger.warn("Gate.io订单簿数据为空，使用ticker价格");
                return null;
            }
            
            // 简单实现：使用第一档价格
            String priceStr = orders.get(0).get(0).asText();
            BigDecimal price = new BigDecimal(priceStr);
            
            logger.debug("Gate.io订单簿价格: {}", price);
            return price;
            
        } catch (Exception e) {
            logger.error("获取Gate.io订单簿数据失败", e);
            return null;
        }
    }
    
    /**
     * 检查API连接状态
     * 
     * @return 是否连接正常
     */
    public boolean isApiHealthy() {
        try {
            String url = String.format("%s/spot/tickers?currency_pair=%s", baseUrl, SYMBOL);
            String response = restTemplate.getForObject(url, String.class);
            return response != null && !response.isEmpty();
        } catch (Exception e) {
            logger.error("Gate.io API健康检查失败", e);
            return false;
        }
    }
    
    /**
     * 创建错误价格数据
     * 
     * @param errorMessage 错误信息
     * @return 错误价格数据
     */
    private PriceData createErrorPriceData(String errorMessage) {
        PriceData priceData = new PriceData();
        priceData.setExchange("Gate.io");
        priceData.setErrorMessage(errorMessage);
        priceData.setTimestamp(LocalDateTime.now());
        return priceData;
    }
} 