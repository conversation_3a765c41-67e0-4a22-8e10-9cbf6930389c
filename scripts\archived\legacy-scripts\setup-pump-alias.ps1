# PowerShell Profile Setup for PUMP30
Write-Host "Setting up PUMP30 command alias..." -ForegroundColor Green

# Function to run PUMP30 with UTF-8 encoding
function Start-Pump {
    [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
    chcp 65001 | Out-Null
    java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar pump30.jar $args
}

# Create alias
Set-Alias -Name pump -Value Start-Pump

# Add to PowerShell profile for permanent use
$profilePath = $PROFILE.CurrentUserAllHosts
if (-not (Test-Path $profilePath)) {
    New-Item -Path $profilePath -Type File -Force | Out-Null
}

# Check if alias already exists in profile
$aliasExists = Get-Content $profilePath -ErrorAction SilentlyContinue | Select-String "Set-Alias.*pump"

if (-not $aliasExists) {
    Add-Content $profilePath @"

# PUMP30 UTF-8 Launcher
function Start-Pump {
    [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
    chcp 65001 | Out-Null
    java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar pump30.jar `$args
}
Set-Alias -Name pump -Value Start-Pump
"@
    Write-Host "Alias added to PowerShell profile: $profilePath" -ForegroundColor Yellow
    Write-Host "You can now use 'pump' command in any new PowerShell session!" -ForegroundColor Green
} else {
    Write-Host "Alias already exists in profile." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Usage:" -ForegroundColor Cyan
Write-Host "  pump      # Start PUMP30 with UTF-8 encoding" -ForegroundColor White
Write-Host ""
Write-Host "Testing the alias..." -ForegroundColor Green 