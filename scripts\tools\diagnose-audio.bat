@echo off
title PUMP音频诊断工具

echo ================================================
echo           PUMP音频系统诊断工具
echo ================================================
echo.

chcp 65001 >nul 2>&1

echo [信息] 正在诊断音频系统...
echo [注意] 请注意听测试音频，诊断过程中会播放测试声音
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Java环境，请先安装Java
    pause
    exit /b 1
)

REM 检查诊断工具是否存在
if not exist "tools\AudioDiagnosticTool.java" (
    echo [错误] 音频诊断工具不存在: tools\AudioDiagnosticTool.java
    echo [提示] 请确保工具文件存在于tools目录中
    pause
    exit /b 1
)

REM 编译诊断工具
echo [步骤1] 编译音频诊断工具...
javac -cp "pump30.jar" "tools\AudioDiagnosticTool.java"

if %errorlevel% neq 0 (
    echo [错误] 诊断工具编译失败
    echo [建议] 请检查Java环境和pump30.jar文件
    pause
    exit /b 1
)

echo [步骤2] 运行音频系统诊断...
echo [注意] 诊断过程中会测试音频播放，请注意听声音
echo.

REM 设置音频优化参数运行诊断
set AUDIO_OPTS=-Dsun.sound.useNewAudioEngine=false -Djavax.sound.sampled.Clip=com.sun.media.sound.DirectAudioDeviceProvider
java %AUDIO_OPTS% -cp "pump30.jar;tools" AudioDiagnosticTool

echo.
echo [信息] 音频诊断完成
echo [清理] 删除编译的class文件...
if exist "tools\AudioDiagnosticTool.class" del "tools\AudioDiagnosticTool.class"

echo.
echo [建议] 根据诊断结果:
echo   - 如果听到了测试音频，音频系统正常
echo   - 如果没有听到任何声音，请检查系统音量和音频设备
echo   - 如果只听到蜂鸣音，请使用 start-pump-audio-enhanced.bat 启动
echo.
pause 