# PUMP系统配置修复说明

## 修复的问题

### 问题1：脚本缺少监控金额设置功能

**问题描述**：
- `build-and-deploy.ps1` 和 `build-and-deploy.bat` 脚本只能修改买入/卖出阈值
- 无法修改 `pump-config.json` 中的 `monitor.amount` 字段
- 导致用户设置的监控金额不生效

**解决方案**：
- 在PowerShell脚本中添加了监控金额的读取、输入、验证和更新功能
- 在batch脚本中使用PowerShell命令实现相同功能
- 现在用户可以一次性修改：买入阈值、卖出阈值、监控数量

**修改内容**：
1. 读取当前监控金额：`$currentMonitorAmount = $config.monitor.amount`
2. 用户输入验证：确保输入的是有效的长整型数字
3. 配置更新：`$config.monitor.amount = $newMonitorAmount`

### 问题2：JAR包启动时配置信息不显示

**问题描述**：
- 用户无法在JAR包启动时看到当前配置信息
- 无法确认JAR包使用的具体配置参数
- 原因：`application.properties` 中日志级别设置为 `logging.level.com.pump=WARN`，INFO级别的配置信息被过滤

**解决方案**：
- 在 `PumpConfigService.logConfigSummary()` 方法中添加 `System.out.println` 输出
- 确保配置信息不受日志级别影响，一定会显示
- 使用美观的格式和emoji图标提升可读性

**修改内容**：
```java
// 同时使用System.out.println确保配置信息一定显示，不受日志级别影响
String separator = new String(new char[50]).replace('\0', '=');
System.out.println("\n" + separator);
System.out.println("🚀 PUMP价格监控系统配置信息");
System.out.println(separator);
System.out.println("📊 监控配置:");
System.out.println("   ⏰ 监控间隔: " + getMonitorInterval() + "ms");
System.out.println("   📈 监控数量: " + getMonitorAmount() + "个PUMP");
// ... 其他配置项
```

## 验证方法

### 验证脚本功能
1. 运行 `tools/build-and-deploy.ps1` 或 `tools/build-and-deploy.bat`
2. 查看是否显示当前的三个配置项：买入阈值、卖出阈值、监控数量
3. 输入新的监控数量，确认配置文件被正确更新

### 验证配置显示
1. 启动生成的JAR包
2. 在启动日志中查看是否显示完整的配置信息块
3. 确认监控数量等所有配置项都正确显示

## 影响范围

- ✅ 增强功能：支持修改监控金额
- ✅ 提升体验：启动时显示完整配置信息  
- ✅ 保持兼容：不影响现有功能
- ✅ 两个版本：PowerShell和batch脚本同步更新

## 修复时间

- 修复日期：2025-01-17
- 修复版本：当前开发版本
- 涉及文件：
  - `tools/build-and-deploy.ps1`
  - `tools/build-and-deploy.bat`
  - `src/main/java/com/pump/config/PumpConfigService.java` 