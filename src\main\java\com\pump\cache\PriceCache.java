package com.pump.cache;

import com.pump.model.PriceData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 价格数据缓存
 * 提供内存缓存功能，减少API调用并在网络异常时提供备用数据
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
@Component
public class PriceCache {
    
    private static final Logger logger = LoggerFactory.getLogger(PriceCache.class);
    
    // 缓存过期时间（秒）
    private static final long CACHE_TTL_SECONDS = 30;
    
    // 缓存存储
    private final ConcurrentHashMap<String, CacheEntry> cache = new ConcurrentHashMap<>();
    
    // 读写锁
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    
    /**
     * 获取缓存的价格数据
     * 
     * @param key 缓存键
     * @return 价格数据，如果不存在或已过期则返回null
     */
    public PriceData get(String key) {
        lock.readLock().lock();
        try {
            CacheEntry entry = cache.get(key);
            if (entry != null) {
                if (isValid(entry)) {
                    logger.debug("缓存命中: {}", key);
                    return entry.getData();
                } else {
                    logger.debug("缓存过期: {}", key);
                    // 异步清理过期缓存
                    cache.remove(key);
                }
            }
            return null;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 存储价格数据到缓存
     * 
     * @param key 缓存键
     * @param data 价格数据
     */
    public void put(String key, PriceData data) {
        if (data == null || !data.isValid()) {
            logger.debug("跳过缓存无效数据: {}", key);
            return;
        }
        
        lock.writeLock().lock();
        try {
            CacheEntry entry = new CacheEntry(data, LocalDateTime.now());
            cache.put(key, entry);
            logger.debug("数据已缓存: {}", key);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 获取过期但仍可用的缓存数据（用于网络异常时的备用数据）
     * 
     * @param key 缓存键
     * @return 价格数据，如果不存在则返回null
     */
    public PriceData getStale(String key) {
        lock.readLock().lock();
        try {
            CacheEntry entry = cache.get(key);
            if (entry != null) {
                logger.debug("获取过期缓存数据: {}", key);
                return entry.getData();
            }
            return null;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 清除指定键的缓存
     * 
     * @param key 缓存键
     */
    public void evict(String key) {
        lock.writeLock().lock();
        try {
            cache.remove(key);
            logger.debug("缓存已清除: {}", key);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 清除所有缓存
     */
    public void clear() {
        lock.writeLock().lock();
        try {
            cache.clear();
            logger.debug("所有缓存已清除");
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public CacheStats getStats() {
        lock.readLock().lock();
        try {
            int totalEntries = cache.size();
            int validEntries = 0;
            int expiredEntries = 0;
            
            for (CacheEntry entry : cache.values()) {
                if (isValid(entry)) {
                    validEntries++;
                } else {
                    expiredEntries++;
                }
            }
            
            return new CacheStats(totalEntries, validEntries, expiredEntries);
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 清理过期缓存
     */
    public void cleanupExpired() {
        lock.writeLock().lock();
        try {
            int removedCount = 0;
            cache.entrySet().removeIf(entry -> {
                if (!isValid(entry.getValue())) {
                    return true;
                }
                return false;
            });
            
            if (removedCount > 0) {
                logger.debug("清理了{}个过期缓存项", removedCount);
            }
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 检查缓存项是否有效
     */
    private boolean isValid(CacheEntry entry) {
        LocalDateTime now = LocalDateTime.now();
        long secondsSinceCreation = ChronoUnit.SECONDS.between(entry.getCreatedAt(), now);
        return secondsSinceCreation <= CACHE_TTL_SECONDS;
    }
    
    /**
     * 缓存项
     */
    private static class CacheEntry {
        private final PriceData data;
        private final LocalDateTime createdAt;
        
        public CacheEntry(PriceData data, LocalDateTime createdAt) {
            this.data = data;
            this.createdAt = createdAt;
        }
        
        public PriceData getData() {
            return data;
        }
        
        public LocalDateTime getCreatedAt() {
            return createdAt;
        }
    }
    
    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        private final int totalEntries;
        private final int validEntries;
        private final int expiredEntries;
        
        public CacheStats(int totalEntries, int validEntries, int expiredEntries) {
            this.totalEntries = totalEntries;
            this.validEntries = validEntries;
            this.expiredEntries = expiredEntries;
        }
        
        public int getTotalEntries() {
            return totalEntries;
        }
        
        public int getValidEntries() {
            return validEntries;
        }
        
        public int getExpiredEntries() {
            return expiredEntries;
        }
        
        @Override
        public String toString() {
            return String.format("CacheStats{total=%d, valid=%d, expired=%d}", 
                               totalEntries, validEntries, expiredEntries);
        }
    }
}
