package com.pump.analyzer;

import com.pump.model.ArbitrageResult;
import com.pump.model.PriceData;
import com.pump.service.PumpPriceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 套利分析器
 * 分析CEX和DEX之间的价格差异，生成交易建议
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
@Component
public class ArbitrageAnalyzer {
    
    private static final Logger logger = LoggerFactory.getLogger(ArbitrageAnalyzer.class);
    
    @Autowired
    private PumpPriceService pumpPriceService;
    
    @Value("${pump.monitor.amount}")
    private BigDecimal defaultAmount;
    
    /** 最小套利机会百分比阈值 */
    private static final BigDecimal MIN_ARBITRAGE_THRESHOLD = new BigDecimal("0.5");
    
    /** 价格计算精度 */
    private static final int PRICE_SCALE = 6;
    
    /**
     * 分析套利机会
     * 
     * @param amount 交易数量
     * @return 套利分析结果
     */
    public ArbitrageResult analyzeArbitrage(BigDecimal amount) {
        logger.debug("开始分析套利机会，交易数量: {}", amount);
        
        ArbitrageResult result = new ArbitrageResult();
        result.setAmount(amount);
        result.setAnalysisTime(LocalDateTime.now());
        
        try {
            // 获取CEX和DEX价格数据
            PriceData cexPrice = pumpPriceService.getCexPrice();
            PriceData dexPrice = pumpPriceService.getDexPrice();
            
            result.setCexPrice(cexPrice);
            result.setDexPrice(dexPrice);
            
            // 检查价格数据有效性
            if (!cexPrice.isValid() && !dexPrice.isValid()) {
                result.setErrorMessage("CEX和DEX价格数据都无效");
                return result;
            }
            
            if (!cexPrice.isValid()) {
                result.setErrorMessage("CEX价格数据无效: " + cexPrice.getErrorMessage());
                return result;
            }
            
            if (!dexPrice.isValid()) {
                result.setErrorMessage("DEX价格数据无效: " + dexPrice.getErrorMessage());
                return result;
            }
            
            // 获取具体的买入和卖出价格
            BigDecimal cexBuyPrice = pumpPriceService.getBuyPrice(amount, "CEX");
            BigDecimal cexSellPrice = pumpPriceService.getSellPrice(amount, "CEX");
            BigDecimal dexBuyPrice = pumpPriceService.getBuyPrice(amount, "DEX");
            BigDecimal dexSellPrice = pumpPriceService.getSellPrice(amount, "DEX");
            
            // 如果获取具体价格失败，使用基础价格
            if (cexBuyPrice == null) cexBuyPrice = cexPrice.getBuyPrice();
            if (cexSellPrice == null) cexSellPrice = cexPrice.getSellPrice();
            if (dexBuyPrice == null) dexBuyPrice = dexPrice.getBuyPrice();
            if (dexSellPrice == null) dexSellPrice = dexPrice.getSellPrice();
            
            result.setCexBuyPrice(cexBuyPrice);
            result.setCexSellPrice(cexSellPrice);
            result.setDexBuyPrice(dexBuyPrice);
            result.setDexSellPrice(dexSellPrice);
            
            // 计算套利机会
            analyzeArbitrageOpportunity(result);
            
            logger.debug("套利分析完成: {}", result);
            return result;
            
        } catch (Exception e) {
            logger.error("分析套利机会时发生异常", e);
            result.setErrorMessage("分析套利机会异常: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 分析套利机会
     * 
     * @param result 套利结果对象
     */
    private void analyzeArbitrageOpportunity(ArbitrageResult result) {
        logger.debug("分析套利机会");
        
        BigDecimal cexBuyPrice = result.getCexBuyPrice();
        BigDecimal cexSellPrice = result.getCexSellPrice();
        BigDecimal dexBuyPrice = result.getDexBuyPrice();
        BigDecimal dexSellPrice = result.getDexSellPrice();
        
        // 检查价格数据完整性
        if (cexBuyPrice == null || cexSellPrice == null || dexBuyPrice == null || dexSellPrice == null) {
            result.setErrorMessage("价格数据不完整");
            return;
        }
        
        // 计算两种套利场景
        // 场景1: CEX买入 -> DEX卖出
        BigDecimal scenario1Profit = dexSellPrice.subtract(cexBuyPrice);
        BigDecimal scenario1ProfitPercent = scenario1Profit.divide(cexBuyPrice, PRICE_SCALE, RoundingMode.HALF_UP)
                                                           .multiply(new BigDecimal("100"));
        
        // 场景2: DEX买入 -> CEX卖出
        BigDecimal scenario2Profit = cexSellPrice.subtract(dexBuyPrice);
        BigDecimal scenario2ProfitPercent = scenario2Profit.divide(dexBuyPrice, PRICE_SCALE, RoundingMode.HALF_UP)
                                                           .multiply(new BigDecimal("100"));
        
        // 选择最优套利机会
        if (scenario1ProfitPercent.compareTo(scenario2ProfitPercent) >= 0) {
            // 场景1更优: CEX买入 -> DEX卖出
            result.setPriceDifference(scenario1Profit);
            result.setPriceDifferencePercent(scenario1ProfitPercent);
            
            if (scenario1ProfitPercent.compareTo(MIN_ARBITRAGE_THRESHOLD) >= 0) {
                result.setHasArbitrageOpportunity(true);
                result.setTradeRecommendation("做升");
                result.setRecommendedPlatform("CEX买入，DEX卖出");
            } else {
                result.setHasArbitrageOpportunity(false);
                result.setTradeRecommendation("无明显套利机会");
                result.setRecommendedPlatform("等待更好机会");
            }
        } else {
            // 场景2更优: DEX买入 -> CEX卖出
            result.setPriceDifference(scenario2Profit);
            result.setPriceDifferencePercent(scenario2ProfitPercent);
            
            if (scenario2ProfitPercent.compareTo(MIN_ARBITRAGE_THRESHOLD) >= 0) {
                result.setHasArbitrageOpportunity(true);
                result.setTradeRecommendation("做跌");
                result.setRecommendedPlatform("DEX买入，CEX卖出");
            } else {
                result.setHasArbitrageOpportunity(false);
                result.setTradeRecommendation("无明显套利机会");
                result.setRecommendedPlatform("等待更好机会");
            }
        }
        
        logger.debug("套利机会分析完成 - 场景1利润: {}%, 场景2利润: {}%, 推荐: {}", 
                    scenario1ProfitPercent, scenario2ProfitPercent, result.getTradeRecommendation());
    }
    
    /**
     * 分析套利机会 (使用默认数量)
     * 
     * @return 套利分析结果
     */
    public ArbitrageResult analyzeArbitrage() {
        return analyzeArbitrage(defaultAmount);
    }
    
    /**
     * 格式化套利结果为控制台输出
     * 
     * @param result 套利结果
     * @return 格式化的字符串
     */
    public String formatArbitrageResult(ArbitrageResult result) {
        if (result.getErrorMessage() != null) {
            return String.format("错误: %s", result.getErrorMessage());
        }
        
        // 格式化时间戳
        String timestamp = result.getAnalysisTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        
        StringBuilder sb = new StringBuilder();
        
        // 第一行：分割线
        sb.append(timestamp).append("  : ===============PUMP监控 ===============\n");
        
        // 获取价格信息并计算100万个PUMP的成本
        BigDecimal dexBuyTotalCost = null;  // DEX买入100万个PUMP的总USDT成本
        BigDecimal cexBuyTotalCost = null;  // CEX买入100万个PUMP的总USDT成本
        BigDecimal dexSellTotalValue = null; // DEX卖出100万个PUMP的总USDT收入
        BigDecimal cexSellTotalValue = null; // CEX卖出100万个PUMP的总USDT收入

        BigDecimal amount = result.getAmount(); // 100万个PUMP

        if (result.getCexPrice() != null && result.getDexPrice() != null &&
            result.getCexPrice().isValid() && result.getDexPrice().isValid()) {

            // 计算买入成本（单价 * 数量）
            BigDecimal cexBuyPrice = result.getCexBuyPrice();
            BigDecimal dexBuyPrice = result.getDexBuyPrice();

            if (cexBuyPrice != null) {
                cexBuyTotalCost = cexBuyPrice.multiply(amount);
            }
            if (dexBuyPrice != null) {
                dexBuyTotalCost = dexBuyPrice.multiply(amount);
            }

            // 计算卖出收入（单价 * 数量）
            BigDecimal cexSellPrice = result.getCexSellPrice();
            BigDecimal dexSellPrice = result.getDexSellPrice();

            if (cexSellPrice != null) {
                cexSellTotalValue = cexSellPrice.multiply(amount);
            }
            if (dexSellPrice != null) {
                dexSellTotalValue = dexSellPrice.multiply(amount);
            }
        }
        
        // 第二行：买入信息（显示DEX买入成本，与CEX比较）
        if (dexBuyTotalCost != null && cexBuyTotalCost != null) {
            BigDecimal buyPriceDiff = dexBuyTotalCost.subtract(cexBuyTotalCost);
            String buyRecommendation = buyPriceDiff.compareTo(BigDecimal.ZERO) > 0 ? "做升" : "做跌";

            sb.append(timestamp).append("  : 池买入100W个PUMP: ")
              .append(String.format("%.2f", dexBuyTotalCost)).append("个USDT，差价：")
              .append(String.format("%.2f", buyPriceDiff)).append("，")
              .append(buyRecommendation).append("\n");
        } else {
            sb.append(timestamp).append("  : 池买入100W个PUMP: 价格获取失败\n");
        }

        // 第三行：卖出信息（显示DEX卖出收入，与CEX比较）
        if (dexSellTotalValue != null && cexSellTotalValue != null) {
            BigDecimal sellPriceDiff = dexSellTotalValue.subtract(cexSellTotalValue);
            String sellRecommendation = sellPriceDiff.compareTo(BigDecimal.ZERO) > 0 ? "做跌" : "做升";

            sb.append(timestamp).append("  : 池卖出100W个PUMP: ")
              .append(String.format("%.2f", dexSellTotalValue)).append("个USDT，差价：")
              .append(String.format("%.2f", sellPriceDiff)).append("  ，")
              .append(sellRecommendation);
        } else {
            sb.append(timestamp).append("  : 池卖出100W个PUMP: 价格获取失败");
        }
        
        return sb.toString();
    }
} 