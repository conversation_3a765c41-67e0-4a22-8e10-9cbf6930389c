---
title: "Java音频播放测试验证指南"
author: "测试工程师"
created: "2025-01-16"
updated: "2025-01-16"
version: "1.0"
category: "issue-fixes"
tags: ["audio", "testing", "verification", "guide"]
status: "active"
priority: "high"
---

# Java音频播放测试验证指南

## 📋 测试概述

本文档提供完整的音频播放测试方法，用于验证PUMP代币价格监控系统的音频告警功能是否正常工作。

### 测试目标
- 验证音频文件资源加载正确
- 确认音频播放设备功能正常
- 测试不同目录运行JAR包的音频功能
- 验证Windows系统音频兼容性
- 确保告警触发机制正常工作

## 🛠️ 测试环境要求

### 系统环境
- **操作系统**: Windows 10/11
- **Java版本**: 1.8.0_392 或更高
- **音频设备**: 任何标准音频输出设备
- **权限**: 标准用户权限（无需管理员）

### 测试工具
- `AudioDiagnosticTool.java` - 音频系统诊断工具
- `diagnose-audio.bat` - 音频诊断脚本
- `start-pump-audio-enhanced.bat` - 增强音频启动脚本
- 原始的 `pump30.jar` 文件

## 📝 详细测试步骤

### 阶段1: 基础音频系统验证

#### 测试1.1: 音频诊断工具测试
```powershell
# 运行音频诊断
.\diagnose-audio.bat
```

**预期结果**:
```
🔍 ========== PUMP音频系统诊断工具 ==========
📊 === 基础音频系统检查 ===
AudioSystem可用: ✅
✅ 系统蜂鸣器正常

🎧 === 音频设备详细检查 ===
发现音频设备数量: 6
设备 1:
  名称: Primary Sound Driver
  是否支持播放: ✅
  播放测试: ✅ 成功

📁 === 音频文件资源检查 ===
检查音频文件: up.wav
  ✅ 找到: /audio/up.wav (大小: 184320 bytes)
检查音频文件: down.wav
  ✅ 找到: /audio/down.wav (大小: 184320 bytes)

🔊 === 实际播放测试 ===
测试系统蜂鸣 (3声)...
🔔 🔔 🔔 
✅ 系统蜂鸣测试完成
测试音频文件播放: up.wav
开始播放 up.wav...
✅ up.wav 播放完成

📋 === 诊断报告和建议 ===
系统音频状态评估:
  系统蜂鸣器: ✅ 正常
  音频设备: 1/6 可用
  音频文件: ✅ 完整
✅ 音频系统基本正常
```

**判断标准**:
- ✅ 系统蜂鸣器正常
- ✅ 至少1个音频设备可用
- ✅ 音频文件完整
- ✅ 能听到测试音频播放

#### 测试1.2: 手动系统蜂鸣测试
```java
// 创建简单测试类
public class BeepTest {
    public static void main(String[] args) throws Exception {
        System.out.println("测试系统蜂鸣...");
        for (int i = 0; i < 5; i++) {
            java.awt.Toolkit.getDefaultToolkit().beep();
            Thread.sleep(500);
            System.out.println("蜂鸣 " + (i + 1));
        }
        System.out.println("测试完成");
    }
}
```

**验证方法**:
```powershell
javac BeepTest.java
java BeepTest
```

### 阶段2: 增强启动脚本验证

#### 测试2.1: 增强启动脚本测试
```powershell
# 使用增强音频启动脚本
.\start-pump-audio-enhanced.bat
```

**预期输出**:
```
================================================
           PUMP30 音频增强版启动器
================================================

[信息] 检查Java环境...
[信息] 检查JAR文件...
[信息] 配置Java音频参数...
[信息] 启动PUMP30价格监控系统...
[提示] 如果没有听到声音，请检查Windows音量混合器中Java应用的音量
[提示] 按 Ctrl+C 停止程序

鎰岀槯浠锋牸鐩戞帶浠诲姟 #1
PUMP价格监控系统已启动，监控间隔: 200ms
报警配置 - 启用: true, 买入阈值: $1.00, 卖出阈值: $1.00
```

**验证要点**:
- 程序正常启动
- 中文显示正确（无乱码）
- 价格监控正常运行
- 当差价超过$1.00时听到音频告警

#### 测试2.2: 不同目录运行测试
```powershell
# 创建测试目录
mkdir C:\temp\pump-test
copy pump30.jar C:\temp\pump-test\
copy start-pump-audio-enhanced.bat C:\temp\pump-test\
cd C:\temp\pump-test

# 从不同目录运行
.\start-pump-audio-enhanced.bat
```

**验证要点**:
- JAR包在不同目录正常运行
- 音频文件正确加载
- 告警功能正常工作

### 阶段3: 音频告警功能验证

#### 测试3.1: 告警阈值测试
修改配置文件 `pump-config.json`:
```json
{
  "alert": {
    "enabled": true,
    "buyThreshold": 0.01,
    "sellThreshold": 0.01,
    "soundType": "CUSTOM",
    "continuousPlay": true
  }
}
```

**验证方法**:
- 将阈值设置为极低值（$0.01）
- 启动系统，应该频繁触发告警
- 验证买入告警播放 `up.wav`
- 验证卖出告警播放 `down.wav`

#### 测试3.2: 音频模式切换测试
```json
// 测试系统蜂鸣模式
{
  "alert": {
    "soundType": "SYSTEM"
  }
}

// 测试多重蜂鸣模式
{
  "alert": {
    "soundType": "MULTIPLE"
  }
}

// 测试自定义音频模式
{
  "alert": {
    "soundType": "CUSTOM"
  }
}
```

### 阶段4: 故障模拟测试

#### 测试4.1: 音频文件缺失测试
```powershell
# 临时重命名音频文件
ren src\main\resources\audio\up.wav up.wav.bak
ren src\main\resources\audio\down.wav down.wav.bak

# 重新打包并测试
mvn clean package
java -jar target\pump30.jar
```

**预期行为**:
- 自动降级到系统蜂鸣音
- 日志显示音频文件加载失败但不崩溃

#### 测试4.2: 音频设备占用测试
```powershell
# 在其他程序（如音乐播放器）占用音频设备时测试
# 启动音乐播放
# 然后运行PUMP系统
.\start-pump-audio-enhanced.bat
```

**预期行为**:
- 系统正常启动
- 告警功能仍然工作（可能降级到系统蜂鸣）

## 📊 测试结果记录

### 测试记录表格
| 测试项目 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|-----|------|
| 音频诊断工具 | 系统正常 | ✅/❌ | 通过/失败 | |
| 系统蜂鸣测试 | 听到蜂鸣音 | ✅/❌ | 通过/失败 | |
| 增强启动脚本 | 正常启动 | ✅/❌ | 通过/失败 | |
| 自定义音频播放 | 听到WAV音频 | ✅/❌ | 通过/失败 | |
| 不同目录运行 | 功能正常 | ✅/❌ | 通过/失败 | |
| 告警阈值触发 | 正确触发 | ✅/❌ | 通过/失败 | |
| 音频模式切换 | 模式正确 | ✅/❌ | 通过/失败 | |
| 故障降级机制 | 降级成功 | ✅/❌ | 通过/失败 | |

### 音频质量评估
- **音量**: 适中/过大/过小
- **清晰度**: 清晰/模糊/失真
- **延迟**: 无延迟/轻微延迟/明显延迟
- **兼容性**: 完全兼容/部分兼容/不兼容

## 🔧 常见问题诊断

### 问题1: 诊断工具显示成功但听不到声音
**可能原因**:
- Windows音量混合器中Java应用被静音
- 默认音频设备设置错误
- 音频驱动问题

**解决步骤**:
1. 右键点击任务栏音量图标
2. 选择"打开音量混合器"
3. 查找Java应用，调整音量
4. 检查默认播放设备设置

### 问题2: 只有系统蜂鸣没有WAV音频
**可能原因**:
- 音频文件路径问题
- WAV文件格式不兼容
- Java音频引擎选择错误

**解决步骤**:
1. 验证JAR包内音频文件存在
2. 使用增强启动脚本
3. 手动设置音频引擎参数

### 问题3: IDE中有声音，JAR包中没有
**可能原因**:
- 资源文件打包问题
- ClassPath差异
- 运行目录权限问题

**解决步骤**:
1. 检查Maven打包配置
2. 验证资源文件包含在JAR中
3. 使用绝对路径运行JAR

## 📈 性能基准测试

### 音频响应时间测试
```java
// 测试音频播放延迟
long startTime = System.currentTimeMillis();
alertSoundService.forcePlayBuyAudio();
long endTime = System.currentTimeMillis();
System.out.println("音频响应时间: " + (endTime - startTime) + "ms");
```

**基准指标**:
- **优秀**: < 100ms
- **良好**: 100-300ms
- **可接受**: 300-500ms
- **需改进**: > 500ms

### 资源使用测试
- **内存使用**: 音频播放前后内存变化
- **CPU使用**: 音频播放时CPU占用率
- **文件句柄**: 确保音频资源正确释放

## 📝 测试报告模板

```markdown
# PUMP音频功能测试报告

## 测试环境
- 操作系统: Windows 11
- Java版本: 1.8.0_392
- 测试时间: 2025-01-16
- 测试人员: [姓名]

## 测试结果总结
- 总测试项: 8
- 通过项目: 7
- 失败项目: 1
- 通过率: 87.5%

## 详细测试结果
[填写测试记录表格]

## 问题描述
[描述遇到的问题]

## 解决方案
[记录解决步骤]

## 建议
[提出改进建议]
```

## 🎯 自动化测试脚本

### Windows批处理自动测试
创建 `run-all-tests.bat`:
```batch
@echo off
echo ========== PUMP音频功能自动测试 ==========

echo [测试1] 音频诊断
call diagnose-audio.bat

echo [测试2] 基础功能测试
timeout /t 5
java -Dtest.mode=true -jar pump30.jar

echo [测试3] 增强启动测试
timeout /t 5
call start-pump-audio-enhanced.bat

echo ========== 自动测试完成 ==========
pause
```

### 测试结果自动记录
```java
// 在AlertSoundService中添加测试模式
if (System.getProperty("test.mode") != null) {
    // 记录测试结果到文件
    logTestResult("audio_test_" + System.currentTimeMillis() + ".log");
}
```

## 📊 更新记录

| 日期 | 版本 | 更新内容 | 作者 |
|------|------|----------|------|
| 2025-01-16 | 1.0 | Java音频播放测试验证指南初版 | 测试工程师 |

---

**注意**: 此测试指南应该与Java音频播放问题系统性解决方案配合使用，确保解决方案的有效性和可靠性。 