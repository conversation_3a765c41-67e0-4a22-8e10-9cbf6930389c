---
title: "Java和Maven完整环境配置指南"
author: "环境配置工程师"
created: "2025-01-14"
updated: "2025-01-16"
version: "1.0"
category: "deployment-ops"
tags: ["environment", "java", "maven", "setup"]
status: "active"
---

# Java和Maven环境变量配置指南

## 📋 需要配置的环境变量

基于检测结果，需要配置以下环境变量：

1. **JAVA_HOME** - Java JDK根目录
2. **MAVEN_HOME** - Maven根目录
3. **Path** - 添加Java和Maven的bin目录

## 🔍 当前环境检测结果

- **Java位置**: `C:\Program Files (x86)\Common Files\Oracle\Java\java8path\java.exe`
- **Maven位置**: `F:\apache-maven-3.9.10`

## 🛠️ 环境变量配置步骤

### 步骤1: 找到JDK根目录

Java位置通常在：
```
C:\Program Files\Java\jdk1.8.0_XXX
C:\Program Files (x86)\Java\jdk1.8.0_XXX
```

检查你的系统中是否有这些目录：
- `C:\Program Files\Java\`
- `C:\Program Files (x86)\Java\`

### 步骤2: 配置环境变量

**手动配置（推荐）：**

1. 右键点击"此电脑" → "属性"
2. 点击"高级系统设置"
3. 点击"环境变量"
4. 在"系统变量"区域：

**配置JAVA_HOME：**
- 点击"新建"
- 变量名：`JAVA_HOME`
- 变量值：`C:\Program Files\Java\jdk1.8.0_XXX` (具体路径根据你的JDK版本)
- 点击"确定"

**配置MAVEN_HOME：**
- 点击"新建"
- 变量名：`MAVEN_HOME`
- 变量值：`F:\apache-maven-3.9.10`
- 点击"确定"

**修改Path变量：**
- 选中"Path"变量，点击"编辑"
- 点击"新建"，添加：`%JAVA_HOME%\bin`
- 再次点击"新建"，添加：`%MAVEN_HOME%\bin`
- 点击"确定"

### 步骤3: 验证配置

关闭当前PowerShell，重新打开，然后运行：

```powershell
# 验证Java
java -version

# 验证Maven
mvn -version
```

## 🚀 快速验证脚本

运行以下命令检查JDK位置：

```powershell
# 检查可能的JDK位置
Get-ChildItem "C:\Program Files\Java\" -ErrorAction SilentlyContinue
Get-ChildItem "C:\Program Files (x86)\Java\" -ErrorAction SilentlyContinue

# 检查Java版本
java -version
```

## 🔧 常见问题解决

### 1. 找不到JDK目录
如果找不到JDK目录，可能需要重新安装JDK：
- 下载：https://www.oracle.com/java/technologies/downloads/
- 安装完整的JDK（不只是JRE）

### 2. 权限问题
如果无法设置系统环境变量，可以：
- 以管理员身份运行
- 或者设置用户环境变量（仅对当前用户有效）

### 3. 路径包含空格
如果路径包含空格，环境变量值用双引号括起来：
```
"C:\Program Files\Java\jdk1.8.0_XXX"
```

## 📝 验证清单

配置完成后，检查以下项目：

- [ ] `java -version` 显示Java版本
- [ ] `mvn -version` 显示Maven版本
- [ ] 两个命令都没有报错
- [ ] Maven显示正确的Java版本

## 🎯 下一步

环境变量配置成功后，就可以：

1. 运行 `.\quick-build-run.bat` 进行一键打包运行
2. 或者手动运行 `mvn clean package -DskipTests` 打包
3. 使用 `java -jar target\pump-price-monitor-1.0-SNAPSHOT.jar` 运行

---

**重要提醒**：配置环境变量后，必须重新打开PowerShell窗口才能生效！ 