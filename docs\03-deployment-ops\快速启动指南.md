# PUMP30 快速启动指南

## 🎯 目标：直接启动无需记住复杂参数

以下是从最简单到最复杂的解决方案：

---

## ⚡ 方案1：使用 `pump` 命令（推荐）

### 批处理版本
```cmd
pump
```
*使用文件：`pump.bat`*

### PowerShell版本  
```powershell
.\run-pump.ps1
```

---

## 🔧 方案2：设置环境变量（一次设置）

### 步骤1：运行环境配置
```cmd
setup-environment.bat
```

### 步骤2：设置后可直接使用
```cmd
java -jar pump30.jar
```

**优点：** 设置一次，在当前命令窗口中直接使用原命令  
**缺点：** 只在当前窗口有效，关闭后需重新设置

---

## ⭐ 方案3：PowerShell别名（永久方案）

### 一次性设置
```powershell
.\setup-pump-alias.ps1
```

### 设置后永久使用
```powershell
pump
```

**优点：** 永久有效，任何新的PowerShell窗口都可用  
**缺点：** 只在PowerShell中有效

---

## 🚀 立即体验

### 最快方法（立即可用）
```cmd
pump
```

### 测试环境变量方法
```cmd
setup-environment.bat
```
然后直接使用：
```cmd
java -jar pump30.jar
```

---

## 📝 各方案对比

| 方案 | 命令 | 有效范围 | 设置复杂度 |
|------|------|----------|------------|
| pump.bat | `pump` | 当前目录 | ⭐ 简单 |
| 环境变量 | `java -jar pump30.jar` | 当前会话 | ⭐⭐ 中等 |
| PowerShell别名 | `pump` | 所有PowerShell | ⭐⭐⭐ 复杂 |

---

## ✅ 推荐使用

**最简单：** 直接使用 `pump` 命令  
**最接近原始：** 使用环境变量方案，然后可以用 `java -jar pump30.jar`

---

## 🔍 验证成功

当您看到以下输出时，说明中文显示正常：
```
执行价格监控任务 #1
PUMP价格监控系统已启动，监控间隔: 200ms
报警配置 - 启用: true, 买入阈值: $30.00, 卖出阈值: $30.00
```

而不是乱码：
```
鎵ц浠锋牸鐩戞帶浠诲姟 #1
``` 