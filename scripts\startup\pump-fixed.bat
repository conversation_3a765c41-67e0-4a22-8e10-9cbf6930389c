@echo off
REM 强制设置控制台编码为UTF-8以避免乱码
chcp 65001 > nul
echo 🎉 ====== PUMP价格监控系统 (音频已修复) ======
echo 📅 启动时间: %date% %time%
echo 🔊 音频方法: CUSTOM_SIMPLIFIED (已验证有效)
echo.

cd /d "%~dp0"

echo 🔍 检查JAR文件...
if not exist "target\pump.jar" (
    echo ❌ 未找到JAR文件，正在重新构建...
    echo.
    mvn package -DskipTests
    if errorlevel 1 (
        echo ❌ 构建失败！
        pause
        exit /b 1
    )
)

echo ✅ JAR文件准备就绪
echo.

echo 🎵 使用修复后的音频播放方法启动系统...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 💡 音频告警已修复！当价格差异超过阈值时，您将听到音频提示。
echo 📊 当前阈值: 买入和卖出差价均为 $1.00
echo ⚙️  音频类型: CUSTOM_SIMPLIFIED (基于成功实例的简化方法)
echo.

REM 使用增强的Java参数启动，包含UTF-8编码设置
java -Dfile.encoding=UTF-8 ^
     -Dconsole.encoding=UTF-8 ^
     -Xms256m -Xmx512m ^
     -Dsun.java2d.d3d=false ^
     -Djava.awt.headless=false ^
     -Djavax.sound.sampled.Clip=com.sun.media.sound.DirectAudioClip ^
     -Djavax.sound.sampled.Port=com.sun.media.sound.PortMixer ^
     -Djavax.sound.sampled.SourceDataLine=com.sun.media.sound.DirectAudioDevice$DirectSDL ^
     -Djavax.sound.sampled.TargetDataLine=com.sun.media.sound.DirectAudioDevice$DirectTDL ^
     -jar target/pump.jar

echo.
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 🏁 PUMP系统已退出

echo.
echo 💡 如果需要调整音频设置，可以运行：
echo    tools\update-audio-config.bat
echo.
echo 🧪 如果需要测试音频功能，可以运行：
echo    tools\test-simplified-audio.bat

pause 