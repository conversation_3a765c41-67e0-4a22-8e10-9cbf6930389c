---
title: "PUMP代币价格监控系统API集成指南"
author: "API集成工程师"
created: "2025-01-16"
updated: "2025-01-16"
version: "1.0"
category: "api-integration"
tags: ["api", "integration", "gate-io", "jupiter"]
status: "active"
---

# PUMP代币价格监控系统API集成指南

## 📋 功能说明

### API集成概述
系统集成了两个主要的API服务：
- **Gate.io API**: 获取中心化交易所的PUMP代币价格数据
- **Jupiter API**: 获取去中心化交易所的PUMP代币报价和价格数据

### 集成目标
- 实现实时价格数据获取
- 支持买入/卖出价格查询
- 提供容错和重试机制
- 优化API调用性能

## 🛠️ 实现方式

### Gate.io API集成

#### 基本信息
- **基础URL**: `https://api.gateio.ws/api/v4`
- **交易对**: `PUMP_USDT`
- **认证方式**: 无需认证（公开API）
- **请求限制**: 无明确限制，建议控制频率

#### 核心实现
```java
@Component
public class GateIoApiClient {
    private static final String BASE_URL = "https://api.gateio.ws/api/v4";
    private static final String SYMBOL = "PUMP_USDT";
    
    public PriceData getPumpPrice() {
        String url = String.format("%s/spot/tickers?currency_pair=%s", BASE_URL, SYMBOL);
        String response = restTemplate.getForObject(url, String.class);
        return parseTickerResponse(response);
    }
    
    private PriceData parseTickerResponse(String response) {
        JsonNode jsonArray = objectMapper.readTree(response);
        if (jsonArray.isArray() && jsonArray.size() > 0) {
            JsonNode ticker = jsonArray.get(0);
            BigDecimal lastPrice = new BigDecimal(ticker.get("last").asText());
            
            PriceData priceData = new PriceData();
            priceData.setExchange("Gate.io");
            priceData.setLastPrice(lastPrice);
            priceData.setTimestamp(LocalDateTime.now());
            priceData.setValid(true);
            
            return priceData;
        }
        return createErrorPriceData("API返回格式错误或无数据");
    }
}
```

### Jupiter API集成

#### 基本信息
- **Quote API**: `https://quote-api.jup.ag/v6/quote`
- **Price API**: `https://price.jup.ag/v4/price`
- **Ultra API**: `https://ultra-api.jup.ag/v1/quote`
- **PUMP代币地址**: `pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn`
- **请求限制**: 免费版每分钟60次请求

#### 核心实现
```java
@Component
public class JupiterApiClientFixed {
    private static final String QUOTE_BASE_URL = "https://quote-api.jup.ag/v6/quote";
    private static final String PRICE_BASE_URL = "https://price.jup.ag/v4/price";
    private static final String PUMP_TOKEN = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn";
    
    public BigDecimal getQuotePrice(BigDecimal amount, boolean isBuy) {
        String inputMint, outputMint;
        BigDecimal inputAmount;
        
        if (isBuy) {
            // 买入逻辑：用USDT买PUMP
            inputMint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"; // USDT
            outputMint = PUMP_TOKEN;
            // 估算需要的USDT数量
            PriceData basePrice = getPumpPrice();
            inputAmount = amount.multiply(basePrice.getLastPrice()).multiply(new BigDecimal("1.2"));
        } else {
            // 卖出逻辑：卖PUMP换USDT
            inputMint = PUMP_TOKEN;
            outputMint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"; // USDT
            inputAmount = amount;
        }
        
        String url = String.format("%s?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50",
            QUOTE_BASE_URL, inputMint, outputMint, inputAmount.toBigInteger());
        
        return executeWithRetry(() -> callQuoteAPI(url, amount, isBuy));
    }
}
```

## 📊 图示说明

### API调用架构图
```mermaid
graph TB
    subgraph "PUMP监控系统"
        A[PumpPriceService] --> B[GateIoApiClient]
        A --> C[JupiterApiClientFixed]
        A --> D[JupiterUltraApiClient]
    end
    
    subgraph "外部API服务"
        E[Gate.io API<br/>spot/tickers]
        F[Jupiter Quote API<br/>v6/quote]
        G[Jupiter Price API<br/>v4/price]
        H[Jupiter Ultra API<br/>v1/quote]
    end
    
    subgraph "缓存层"
        I[PriceCache<br/>30秒TTL]
    end
    
    B --> E
    C --> F
    C --> G
    D --> H
    C --> I
    D --> I
    
    style A fill:#e1f5fe
    style I fill:#f3e5f5
```

### API调用时序图
```mermaid
sequenceDiagram
    participant S as PumpPriceService
    participant G as GateIoApiClient
    participant J as JupiterApiClient
    participant C as PriceCache
    participant API as External APIs
    
    S->>G: getCexPrice()
    G->>API: GET /spot/tickers
    API-->>G: Price Response
    G-->>S: PriceData
    
    S->>J: getDexBuyPrice(1000000)
    J->>C: checkCache("jupiter:pump:price")
    alt Cache Hit
        C-->>J: Cached PriceData
    else Cache Miss
        J->>API: GET /v4/price
        API-->>J: Price Response
        J->>C: updateCache()
    end
    
    J->>API: GET /v6/quote (buy)
    API-->>J: Quote Response
    J-->>S: Buy Price
    
    S->>J: getDexSellPrice(1000000)
    J->>API: GET /v6/quote (sell)
    API-->>J: Quote Response
    J-->>S: Sell Price
```

### 错误处理流程
```mermaid
flowchart TD
    A[API请求] --> B{HTTP状态码}
    B -->|200| C[解析响应]
    B -->|429| D[限流处理]
    B -->|4xx/5xx| E[错误处理]
    
    D --> F[等待2秒]
    F --> G[重新请求]
    
    E --> H{重试次数<3?}
    H -->|是| I[指数退避]
    H -->|否| J[返回错误]
    
    I --> K[延迟重试]
    K --> A
    
    C --> L{数据有效?}
    L -->|是| M[返回成功]
    L -->|否| N[数据验证失败]
    
    style M fill:#e8f5e8
    style J fill:#ffebee
    style N fill:#fff3e0
```

## ⚙️ 配置示例

### API客户端配置
```properties
# Gate.io API配置
gate.api.base-url=https://api.gateio.ws/api/v4
gate.api.timeout=10000

# Jupiter API配置
jupiter.api.quote-url=https://quote-api.jup.ag/v6/quote
jupiter.api.price-url=https://price.jup.ag/v4/price
jupiter.api.ultra-url=https://ultra-api.jup.ag/v1/quote
jupiter.api.timeout=30000
jupiter.api.retry-delay=2000
```

### 重试策略配置
```java
// 重试配置
private static final int MAX_RETRIES = 3;
private static final long BASE_RETRY_DELAY = 1000;
private static final double BACKOFF_MULTIPLIER = 2.0;
private static final long MAX_RETRY_DELAY = 10000;

// 限流处理配置
private static final long RATE_LIMIT_WAIT = 2000; // 429错误等待时间
```

### 缓存配置
```java
// 缓存配置
private static final long PRICE_CACHE_TTL = 30000; // 30秒
private static final String CACHE_KEY_PRICE = "jupiter:pump:price";

// 缓存实现
@Component
public class PriceCache {
    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    
    public PriceData get(String key) {
        CacheEntry entry = cache.get(key);
        return (entry != null && !entry.isExpired()) ? entry.getData() : null;
    }
}
```

## 📝 更新记录

| 日期 | 版本 | 更新内容 | 作者 |
|------|------|----------|------|
| 2025-01-16 | 1.0 | 初始版本，API集成指南 | API集成工程师 |

---

**相关文档**: 
- [GateIO集成详解](GateIO集成详解_GateIO-Integration.md)
- [Jupiter集成详解](Jupiter集成详解_Jupiter-Integration.md)
- [API错误处理](API错误处理_Error-Handling.md)
