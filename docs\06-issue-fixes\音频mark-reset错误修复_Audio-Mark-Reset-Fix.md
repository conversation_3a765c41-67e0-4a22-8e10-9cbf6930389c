---
title: "音频播放mark/reset not supported错误修复方案"
author: "音频系统工程师"
created: "2025-01-16"
updated: "2025-01-16"
version: "1.0"
category: "issue-fixes"
tags: ["audio", "mark-reset", "jar", "inputstream", "fix"]
status: "active"
priority: "high"
---

# 音频播放mark/reset not supported错误修复方案

## 📋 功能说明

### 问题现象
PUMP代币价格监控系统的JAR包在非项目开发路径下运行时，出现"mark/reset not supported"错误，导致自定义音频文件无法播放，系统自动降级使用系统蜂鸣音效。

### 错误根因
1. **InputStream类型差异**：开发环境和JAR包环境下返回的InputStream类型不同
2. **AudioSystem要求**：音频格式解析需要支持mark/reset的流
3. **资源加载机制**：JAR包中的资源流可能不支持mark/reset操作

## 🛠️ 实现方式

### 解决方案1: BufferedInputStream包装（推荐）

```java
/**
 * 增强的音频播放服务 - 解决mark/reset问题
 */
@Service
public class EnhancedAlertSoundService {
    
    private static final Logger logger = LoggerFactory.getLogger(EnhancedAlertSoundService.class);
    
    /**
     * 安全的音频资源加载 - 确保支持mark/reset
     */
    private InputStream loadAudioResourceSafely(String audioFile) {
        try {
            // 1. 尝试多种路径加载
            InputStream rawStream = loadAudioResourceWithMultiplePaths(audioFile);
            if (rawStream == null) {
                logger.error("无法从任何路径加载音频文件: {}", audioFile);
                return null;
            }
            
            // 2. 检查是否支持mark/reset
            if (rawStream.markSupported()) {
                logger.debug("原始流支持mark/reset: {}", audioFile);
                return rawStream;
            }
            
            // 3. 使用BufferedInputStream包装以支持mark/reset
            logger.debug("原始流不支持mark/reset，使用BufferedInputStream包装: {}", audioFile);
            return new BufferedInputStream(rawStream, 8192); // 8KB缓冲区
            
        } catch (Exception e) {
            logger.error("加载音频资源时发生异常: {}", audioFile, e);
            return null;
        }
    }
    
    /**
     * 多路径音频资源加载
     */
    private InputStream loadAudioResourceWithMultiplePaths(String audioFile) {
        // 定义多个可能的资源路径
        String[] resourcePaths = {
            "/" + audioFile,           // 根路径
            "/audio/" + audioFile,     // 音频目录
            "/sounds/" + audioFile,    // 声音目录
            audioFile                  // 直接文件名
        };
        
        // 1. 尝试Class.getResourceAsStream()
        for (String path : resourcePaths) {
            InputStream stream = getClass().getResourceAsStream(path);
            if (stream != null) {
                logger.debug("通过Class.getResourceAsStream()加载成功: {}", path);
                return stream;
            }
        }
        
        // 2. 尝试ClassLoader.getResourceAsStream()
        for (String path : resourcePaths) {
            // 移除开头的斜杠（ClassLoader不需要）
            String cleanPath = path.startsWith("/") ? path.substring(1) : path;
            InputStream stream = getClass().getClassLoader().getResourceAsStream(cleanPath);
            if (stream != null) {
                logger.debug("通过ClassLoader.getResourceAsStream()加载成功: {}", cleanPath);
                return stream;
            }
        }
        
        // 3. 尝试系统ClassLoader
        for (String path : resourcePaths) {
            String cleanPath = path.startsWith("/") ? path.substring(1) : path;
            InputStream stream = ClassLoader.getSystemResourceAsStream(cleanPath);
            if (stream != null) {
                logger.debug("通过SystemClassLoader加载成功: {}", cleanPath);
                return stream;
            }
        }
        
        return null;
    }
    
    /**
     * 安全的音频播放方法
     */
    private void playCustomSoundSafely(AlertType alertType) {
        String audioFile = (alertType == AlertType.BUY_OPPORTUNITY) ? "up.wav" : "down.wav";
        
        try {
            // 1. 安全加载音频资源
            InputStream audioStream = loadAudioResourceSafely(audioFile);
            if (audioStream == null) {
                logger.warn("无法加载音频文件，降级到系统蜂鸣: {}", audioFile);
                playSystemBeepFallback(alertType);
                return;
            }
            
            // 2. 验证流是否支持mark/reset
            if (!audioStream.markSupported()) {
                logger.error("音频流仍然不支持mark/reset，这不应该发生");
                audioStream.close();
                playSystemBeepFallback(alertType);
                return;
            }
            
            // 3. 创建AudioInputStream
            AudioInputStream audioInputStream;
            try {
                audioInputStream = AudioSystem.getAudioInputStream(audioStream);
                logger.debug("成功创建AudioInputStream: {}", audioFile);
            } catch (UnsupportedAudioFileException e) {
                logger.error("不支持的音频文件格式: {}", audioFile, e);
                audioStream.close();
                playSystemBeepFallback(alertType);
                return;
            } catch (IOException e) {
                logger.error("读取音频文件时发生IO异常: {}", audioFile, e);
                audioStream.close();
                playSystemBeepFallback(alertType);
                return;
            }
            
            // 4. 播放音频
            playAudioInputStream(audioInputStream);
            
        } catch (Exception e) {
            logger.error("播放自定义音频时发生未预期异常: {}", audioFile, e);
            playSystemBeepFallback(alertType);
        }
    }
    
    /**
     * 播放AudioInputStream
     */
    private void playAudioInputStream(AudioInputStream audioInputStream) throws Exception {
        Clip clip = null;
        try {
            // 获取音频格式
            AudioFormat format = audioInputStream.getFormat();
            logger.debug("音频格式: {}Hz, {}bit, {}ch", 
                format.getSampleRate(), format.getSampleSizeInBits(), format.getChannels());
            
            // 检查格式支持
            DataLine.Info info = new DataLine.Info(Clip.class, format);
            if (!AudioSystem.isLineSupported(info)) {
                throw new UnsupportedAudioFileException("系统不支持该音频格式");
            }
            
            // 获取音频剪辑
            clip = (Clip) AudioSystem.getLine(info);
            clip.open(audioInputStream);
            
            // 播放音频
            clip.start();
            logger.debug("开始播放音频");
            
            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(100);
            }
            
            logger.debug("音频播放完成");
            
        } finally {
            // 清理资源
            if (clip != null) {
                clip.close();
            }
            audioInputStream.close();
        }
    }
    
    /**
     * 系统蜂鸣降级方案
     */
    private void playSystemBeepFallback(AlertType alertType) {
        try {
            if (alertType == AlertType.BUY_OPPORTUNITY) {
                // 买入告警：单次蜂鸣
                Toolkit.getDefaultToolkit().beep();
                logger.info("使用系统蜂鸣器播放买入告警");
            } else {
                // 卖出告警：双次蜂鸣
                Toolkit.getDefaultToolkit().beep();
                Thread.sleep(200);
                Toolkit.getDefaultToolkit().beep();
                logger.info("使用系统蜂鸣器播放卖出告警");
            }
        } catch (Exception e) {
            logger.error("系统蜂鸣器也无法使用", e);
        }
    }
    
    /**
     * 音频系统诊断
     */
    public void diagnoseAudioSystem() {
        logger.info("=== 音频系统诊断开始 ===");
        
        // 1. 检查音频文件资源
        String[] audioFiles = {"up.wav", "down.wav"};
        for (String audioFile : audioFiles) {
            InputStream stream = loadAudioResourceSafely(audioFile);
            if (stream != null) {
                try {
                    logger.info("✅ {} 加载成功，支持mark/reset: {}", 
                        audioFile, stream.markSupported());
                    
                    // 测试AudioInputStream创建
                    AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(stream);
                    AudioFormat format = audioInputStream.getFormat();
                    logger.info("  音频格式: {}Hz, {}bit, {}ch", 
                        format.getSampleRate(), format.getSampleSizeInBits(), format.getChannels());
                    
                    audioInputStream.close();
                } catch (Exception e) {
                    logger.error("❌ {} 格式验证失败: {}", audioFile, e.getMessage());
                }
            } else {
                logger.error("❌ {} 加载失败", audioFile);
            }
        }
        
        // 2. 检查音频设备
        Mixer.Info[] mixers = AudioSystem.getMixerInfo();
        logger.info("可用音频设备数量: {}", mixers.length);
        
        logger.info("=== 音频系统诊断完成 ===");
    }
}
```

### 解决方案2: 字节数组缓存方案

```java
/**
 * 字节数组缓存方案 - 适用于小音频文件
 */
private void playCustomSoundWithByteArray(AlertType alertType) {
    String audioFile = (alertType == AlertType.BUY_OPPORTUNITY) ? "up.wav" : "down.wav";
    
    try {
        // 1. 加载音频资源到字节数组
        byte[] audioData = loadAudioAsBytes(audioFile);
        if (audioData == null) {
            logger.warn("无法加载音频数据，降级到系统蜂鸣: {}", audioFile);
            playSystemBeepFallback(alertType);
            return;
        }
        
        // 2. 从字节数组创建ByteArrayInputStream（天然支持mark/reset）
        ByteArrayInputStream byteStream = new ByteArrayInputStream(audioData);
        
        // 3. 创建AudioInputStream
        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(byteStream);
        
        // 4. 播放音频
        playAudioInputStream(audioInputStream);
        
    } catch (Exception e) {
        logger.error("使用字节数组播放音频失败: {}", audioFile, e);
        playSystemBeepFallback(alertType);
    }
}

/**
 * 将音频资源加载为字节数组
 */
private byte[] loadAudioAsBytes(String audioFile) {
    try (InputStream inputStream = loadAudioResourceWithMultiplePaths(audioFile)) {
        if (inputStream == null) {
            return null;
        }
        
        // 读取所有字节到内存
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[4096];
        int bytesRead;
        
        while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, bytesRead);
        }
        
        byte[] audioData = buffer.toByteArray();
        logger.debug("音频文件 {} 加载完成，大小: {} bytes", audioFile, audioData.length);
        
        return audioData;
        
    } catch (IOException e) {
        logger.error("读取音频文件到字节数组失败: {}", audioFile, e);
        return null;
    }
}
```

## 📊 图示说明

### 问题原理图
```mermaid
sequenceDiagram
    participant App as 应用程序
    participant CL as ClassLoader
    participant IS as InputStream
    participant AS as AudioSystem
    
    App->>CL: getResourceAsStream("up.wav")
    CL-->>IS: 返回InputStream
    
    Note over IS: 开发环境: FileInputStream (支持mark/reset)<br/>JAR环境: JarInputStream (可能不支持)
    
    App->>AS: getAudioInputStream(stream)
    AS->>IS: stream.markSupported()?
    
    alt 支持mark/reset
        IS-->>AS: true
        AS->>IS: mark(1024)
        AS->>IS: 读取格式头
        AS->>IS: reset()
        AS-->>App: AudioInputStream
    else 不支持mark/reset
        IS-->>AS: false
        AS-->>App: 抛出异常或返回null
    end
```

### 解决方案对比
```mermaid
graph TB
    A[音频播放需求] --> B{选择解决方案}
    
    B --> C[BufferedInputStream包装]
    B --> D[字节数组缓存]
    
    C --> C1[优点: 内存效率高]
    C --> C2[缺点: 需要额外包装]
    
    D --> D1[优点: 天然支持mark/reset]
    D --> D2[缺点: 占用更多内存]
    
    C1 --> E[推荐用于大音频文件]
    D1 --> F[推荐用于小音频文件]
    
    style E fill:#e8f5e8
    style F fill:#e1f5fe
```

## ⚙️ 配置示例

### 修改AlertSoundService类
```java
// 在现有的AlertSoundService中替换playCustomSound方法
private void playCustomSound(AlertType alertType) {
    // 使用新的安全播放方法
    playCustomSoundSafely(alertType);
}

// 添加诊断功能到@PostConstruct
@PostConstruct
public void initializeAudioSystem() {
    if (logger.isDebugEnabled()) {
        diagnoseAudioSystem();
    }
}
```

### 测试验证方法
```java
// 创建测试类验证修复效果
public class AudioFixVerificationTest {
    
    public static void main(String[] args) {
        EnhancedAlertSoundService audioService = new EnhancedAlertSoundService();
        
        // 1. 诊断音频系统
        audioService.diagnoseAudioSystem();
        
        // 2. 测试音频播放
        System.out.println("测试买入告警音频...");
        audioService.playCustomSoundSafely(AlertType.BUY_OPPORTUNITY);
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("测试卖出告警音频...");
        audioService.playCustomSoundSafely(AlertType.SELL_OPPORTUNITY);
    }
}
```

## 📝 更新记录

| 日期 | 版本 | 更新内容 | 作者 |
|------|------|----------|------|
| 2025-01-16 | 1.0 | 初始版本，mark/reset错误修复方案 | 音频系统工程师 |

## 🚀 具体修改步骤

### 步骤1: 备份现有代码
```bash
# 备份当前的AlertSoundService.java
cp src/main/java/com/pump/service/AlertSoundService.java \
   src/main/java/com/pump/service/AlertSoundService.java.backup
```

### 步骤2: 修改AlertSoundService类
在 `src/main/java/com/pump/service/AlertSoundService.java` 中进行以下修改：

```java
// 1. 添加必要的import
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import javax.sound.sampled.DataLine;
import javax.sound.sampled.UnsupportedAudioFileException;

// 2. 替换现有的playCustomSound方法
private void playCustomSound(AlertType alertType) {
    String audioFile = (alertType == AlertType.BUY_OPPORTUNITY) ? "up.wav" : "down.wav";

    try {
        // 使用BufferedInputStream确保支持mark/reset
        InputStream rawStream = getClass().getClassLoader().getResourceAsStream(audioFile);
        if (rawStream == null) {
            logger.warn("无法加载音频文件，降级到系统蜂鸣: {}", audioFile);
            playSystemBeepFallback(alertType);
            return;
        }

        // 关键修复：使用BufferedInputStream包装
        InputStream audioStream = rawStream.markSupported() ?
            rawStream : new BufferedInputStream(rawStream, 8192);

        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream);
        Clip clip = AudioSystem.getClip();
        clip.open(audioInputStream);

        clip.start();

        // 等待播放完成
        while (clip.isRunning()) {
            Thread.sleep(100);
        }

        // 清理资源
        clip.close();
        audioInputStream.close();
        audioStream.close();

    } catch (Exception e) {
        logger.error("播放自定义音频失败: {}, 使用系统提示音", e.getMessage());
        playSystemBeepFallback(alertType);
    }
}

// 3. 添加系统蜂鸣降级方法
private void playSystemBeepFallback(AlertType alertType) {
    try {
        if (alertType == AlertType.BUY_OPPORTUNITY) {
            Toolkit.getDefaultToolkit().beep();
        } else {
            Toolkit.getDefaultToolkit().beep();
            Thread.sleep(200);
            Toolkit.getDefaultToolkit().beep();
        }
        logger.info("使用系统蜂鸣器播放告警: {}", alertType);
    } catch (Exception e) {
        logger.error("系统蜂鸣器也无法使用", e);
    }
}
```

### 步骤3: 测试验证
创建测试类 `src/test/java/com/pump/service/AudioFixTest.java`：

```java
@SpringBootTest
public class AudioFixTest {

    @Autowired
    private AlertSoundService alertSoundService;

    @Test
    public void testAudioPlaybackAfterFix() {
        // 测试买入告警
        assertDoesNotThrow(() -> {
            alertSoundService.checkBuyAlert(
                new BigDecimal("35.00"),
                new BigDecimal("5676.00"),
                new BigDecimal("5711.00")
            );
        });

        // 等待播放完成
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 测试卖出告警
        assertDoesNotThrow(() -> {
            alertSoundService.checkSellAlert(
                new BigDecimal("35.00"),
                new BigDecimal("5746.00"),
                new BigDecimal("5711.00")
            );
        });
    }
}
```

### 步骤4: 构建和部署测试
```bash
# 1. 重新编译项目
mvn clean compile

# 2. 运行测试
mvn test -Dtest=AudioFixTest

# 3. 打包JAR
mvn package

# 4. 在不同目录测试
mkdir /tmp/pump-test
cp target/pump30.jar /tmp/pump-test/
cd /tmp/pump-test
java -jar pump30.jar
```

### 步骤5: 验证修复效果
运行JAR包后，观察日志输出：
- ✅ 应该看到"使用系统蜂鸣器播放告警"而不是"mark/reset not supported"
- ✅ 音频告警应该正常工作（自定义音频或系统蜂鸣）
- ✅ 不应该再出现异常堆栈跟踪

## 🔧 最佳实践建议

### 1. 资源加载最佳实践
```java
// 推荐的资源加载模式
private InputStream loadResourceSafely(String resourceName) {
    InputStream stream = getClass().getResourceAsStream("/" + resourceName);
    if (stream == null) {
        stream = getClass().getClassLoader().getResourceAsStream(resourceName);
    }

    // 确保支持mark/reset
    return stream != null && stream.markSupported() ?
        stream : new BufferedInputStream(stream, 8192);
}
```

### 2. 音频文件格式建议
- 使用标准PCM WAV格式
- 采样率：44100Hz或22050Hz
- 位深度：16位
- 声道：单声道或立体声
- 文件大小：建议小于1MB

### 3. 错误处理策略
- 总是提供降级方案（系统蜂鸣）
- 记录详细的错误日志
- 不要让音频播放失败影响主业务流程

### 4. 跨平台兼容性
- 在Windows、Linux、macOS上都进行测试
- 考虑无头环境（headless）的处理
- 提供配置选项禁用音频功能

---

**相关文档**:
- [音频告警跨平台问题](音频告警跨平台问题_Audio-Alert-Cross-Platform-Issue.md)
- [自定义音频指南](../04-monitoring-alerting/自定义音频指南_Custom-Audio-Guide.md)
- [故障排查指南](故障排查指南_Troubleshooting-Guide.md)
