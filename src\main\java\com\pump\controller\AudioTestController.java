package com.pump.controller;

import com.pump.service.AlertSoundService;
import com.pump.util.PowerfulAudioTester;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * 音频测试控制器
 * 提供手动触发音频播放的接口，用于测试音频功能
 */
@RestController
@RequestMapping("/api/audio-test")
public class AudioTestController {

    @Autowired
    private AlertSoundService alertSoundService;

    /**
     * 测试买入音频告警
     * 访问: http://localhost:8080/api/audio-test/buy
     */
    @GetMapping("/buy")
    public String testBuyAlert(@RequestParam(defaultValue = "5.0") double difference) {
        System.out.println("🎵 [手动测试] 开始测试买入音频告警 - 差价: $" + difference);
        
        try {
            BigDecimal buyDifference = new BigDecimal(String.valueOf(difference));
            BigDecimal buyPrice = new BigDecimal("6700.00");
            BigDecimal gatePrice = new BigDecimal("6705.00");
            
            alertSoundService.checkBuyAlert(buyDifference, buyPrice, gatePrice);
            
            String result = "✅ 买入音频告警测试完成 - 差价: $" + difference;
            System.out.println(result);
            return result;
            
        } catch (Exception e) {
            String error = "❌ 买入音频告警测试失败: " + e.getMessage();
            System.out.println(error);
            e.printStackTrace();
            return error;
        }
    }

    /**
     * 测试卖出音频告警
     * 访问: http://localhost:8080/api/audio-test/sell
     */
    @GetMapping("/sell")
    public String testSellAlert(@RequestParam(defaultValue = "3.5") double difference) {
        System.out.println("🎵 [手动测试] 开始测试卖出音频告警 - 差价: $" + difference);
        
        try {
            BigDecimal sellDifference = new BigDecimal(String.valueOf(difference));
            BigDecimal sellPrice = new BigDecimal("6708.50");
            BigDecimal gatePrice = new BigDecimal("6705.00");
            
            alertSoundService.checkSellAlert(sellDifference, sellPrice, gatePrice);
            
            String result = "✅ 卖出音频告警测试完成 - 差价: $" + difference;
            System.out.println(result);
            return result;
            
        } catch (Exception e) {
            String error = "❌ 卖出音频告警测试失败: " + e.getMessage();
            System.out.println(error);
            e.printStackTrace();
            return error;
        }
    }

    /**
     * 获取音频配置信息
     * 访问: http://localhost:8080/api/audio-test/config
     */
    @GetMapping("/config")
    public String getAudioConfig() {
        try {
            String config = "音频配置：已简化，仅支持SYSTEM和CUSTOM两种模式";
            System.out.println("📋 [手动测试] 音频配置信息: " + config);
            return config;
        } catch (Exception e) {
            String error = "❌ 获取音频配置失败: " + e.getMessage();
            System.out.println(error);
            return error;
        }
    }

    /**
     * 连续测试音频播放
     * 访问: http://localhost:8080/api/audio-test/continuous
     */
    @GetMapping("/continuous")
    public String testContinuous(@RequestParam(defaultValue = "3") int count) {
        System.out.println("🎵 [手动测试] 开始连续音频测试 - 次数: " + count);
        
        try {
            for (int i = 1; i <= count; i++) {
                System.out.println("🔊 [手动测试] 第 " + i + " 次测试");
                
                // 测试买入音频
                BigDecimal buyDifference = new BigDecimal("2.0");
                BigDecimal buyPrice = new BigDecimal("6700.00");
                BigDecimal gatePrice = new BigDecimal("6702.00");
                alertSoundService.checkBuyAlert(buyDifference, buyPrice, gatePrice);
                
                // 等待3秒
                Thread.sleep(3000);
                
                // 测试卖出音频
                BigDecimal sellDifference = new BigDecimal("1.5");
                BigDecimal sellPrice = new BigDecimal("6706.50");
                alertSoundService.checkSellAlert(sellDifference, sellPrice, gatePrice);
                
                // 等待3秒
                if (i < count) {
                    Thread.sleep(3000);
                }
            }
            
            String result = "✅ 连续音频测试完成 - 总次数: " + count;
            System.out.println(result);
            return result;
            
        } catch (Exception e) {
            String error = "❌ 连续音频测试失败: " + e.getMessage();
            System.out.println(error);
            e.printStackTrace();
            return error;
        }
    }

    /**
     * 强制播放买入音频（绕过所有条件检查）
     * 访问: http://localhost:8080/api/audio-test/force-buy
     */
    @GetMapping("/force-buy")
    public String forcePlayBuyAudio() {
        System.out.println("🎵 [强制测试] 强制播放买入音频");

        try {
            alertSoundService.forcePlayBuyAudio();

            String result = "✅ 强制买入音频播放完成";
            System.out.println(result);
            return result;

        } catch (Exception e) {
            String error = "❌ 强制买入音频播放失败: " + e.getMessage();
            System.out.println(error);
            e.printStackTrace();
            return error;
        }
    }

    /**
     * 强制播放卖出音频（绕过所有条件检查）
     * 访问: http://localhost:8080/api/audio-test/force-sell
     */
    @GetMapping("/force-sell")
    public String forcePlaySellAudio() {
        System.out.println("🎵 [强制测试] 强制播放卖出音频");

        try {
            alertSoundService.forcePlaySellAudio();

            String result = "✅ 强制卖出音频播放完成";
            System.out.println(result);
            return result;

        } catch (Exception e) {
            String error = "❌ 强制卖出音频播放失败: " + e.getMessage();
            System.out.println(error);
            e.printStackTrace();
            return error;
        }
    }

    /**
     * 强力音频测试 - 全面测试所有音频功能
     * 访问: http://localhost:8080/api/audio-test/powerful
     */
    @GetMapping("/powerful")
    public String powerfulAudioTest() {
        System.out.println("🎵 [强力测试] 开始强力音频测试");

        try {
            // 在新线程中运行，避免阻塞HTTP响应
            new Thread(() -> {
                PowerfulAudioTester.runFullAudioTest();
            }).start();

            String result = "✅ 强力音频测试已启动，请查看控制台输出并注意听声音";
            System.out.println(result);
            return result;

        } catch (Exception e) {
            String error = "❌ 强力音频测试启动失败: " + e.getMessage();
            System.out.println(error);
            e.printStackTrace();
            return error;
        }
    }

    /**
     * 强力系统蜂鸣测试
     * 访问: http://localhost:8080/api/audio-test/powerful-beep
     */
    @GetMapping("/powerful-beep")
    public String powerfulBeepTest() {
        System.out.println("📢 [强力测试] 开始强力系统蜂鸣测试");

        try {
            // 在新线程中运行，避免阻塞HTTP响应
            new Thread(() -> {
                PowerfulAudioTester.testPowerfulSystemBeep();
            }).start();

            String result = "✅ 强力系统蜂鸣测试已启动，请注意听声音";
            System.out.println(result);
            return result;

        } catch (Exception e) {
            String error = "❌ 强力系统蜂鸣测试失败: " + e.getMessage();
            System.out.println(error);
            e.printStackTrace();
            return error;
        }
    }

    /**
     * 强力WAV播放测试
     * 访问: http://localhost:8080/api/audio-test/powerful-wav
     */
    @GetMapping("/powerful-wav")
    public String powerfulWavTest() {
        System.out.println("🎵 [强力测试] 开始强力WAV播放测试");

        try {
            // 在新线程中运行，避免阻塞HTTP响应
            new Thread(() -> {
                PowerfulAudioTester.testPowerfulWavPlayback();
            }).start();

            String result = "✅ 强力WAV播放测试已启动，请注意听声音";
            System.out.println(result);
            return result;

        } catch (Exception e) {
            String error = "❌ 强力WAV播放测试失败: " + e.getMessage();
            System.out.println(error);
            e.printStackTrace();
            return error;
        }
    }
}
