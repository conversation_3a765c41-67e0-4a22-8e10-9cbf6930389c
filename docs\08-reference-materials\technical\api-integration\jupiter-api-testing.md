# Jupiter API 测试指南

## 📋 测试目标

验证Jupiter API客户端修改后的实现是否正确解决了价格选择策略问题：
- 确保买入和卖出价格被正确分离
- 验证Price API V2的响应解析
- 测试Quote API的价格计算逻辑

## 🔧 测试环境准备

### 1. 依赖检查
确保项目包含以下依赖：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
</dependency>
```

### 2. 配置验证
检查 `application.properties` 中的配置：
```properties
jupiter.api.base-url=https://api.jup.ag/price/v2
jupiter.api.quote-url=https://quote-api.jup.ag/v6/quote
jupiter.api.timeout=5000
```

### 3. 代币地址确认
> **重要**: 确保使用正确的PUMP代币地址
- 当前使用的是示例地址，需要更新为真实的PUMP代币地址
- 验证USDT地址: `EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v`

## 📊 测试用例

### 测试1: Price API V2 基础功能测试

**测试目的**: 验证Price API V2能否正确获取买入和卖出价格

**测试步骤**:
1. 启动应用
2. 调用 `JupiterApiClient.getPumpPrice()`
3. 检查返回的PriceData对象

**预期结果**:
```java
PriceData result = jupiterClient.getPumpPrice();
assertNotNull(result);
assertNotNull(result.getBuyPrice());
assertNotNull(result.getSellPrice());
assertNotEquals(result.getBuyPrice(), result.getSellPrice()); // 买入价和卖出价应该不同
assertTrue(result.getBuyPrice().compareTo(BigDecimal.ZERO) > 0);
assertTrue(result.getSellPrice().compareTo(BigDecimal.ZERO) > 0);
```

**日志验证**:
- 应该看到: "从quotedPrice获取买入价格: X.XXXXXX"
- 应该看到: "从quotedPrice获取卖出价格: X.XXXXXX"
- 买入价和卖出价应该不同

### 测试2: Quote API 交易报价测试

**测试目的**: 验证Quote API能否正确计算大额交易的价格

**测试步骤**:
1. 测试买入报价: `getQuotePrice(new BigDecimal("100000"), true)`
2. 测试卖出报价: `getQuotePrice(new BigDecimal("100000"), false)`

**预期结果**:
```java
BigDecimal buyQuote = jupiterClient.getQuotePrice(new BigDecimal("100000"), true);
BigDecimal sellQuote = jupiterClient.getQuotePrice(new BigDecimal("100000"), false);

assertNotNull(buyQuote);
assertNotNull(sellQuote);
assertTrue(buyQuote.compareTo(BigDecimal.ZERO) > 0);
assertTrue(sellQuote.compareTo(BigDecimal.ZERO) > 0);
```

### 测试3: API健康检查测试

**测试目的**: 验证API连接状态检查

**测试步骤**:
1. 调用 `isApiHealthy()`
2. 验证返回值和日志

**预期结果**:
```java
boolean isHealthy = jupiterClient.isApiHealthy();
assertTrue(isHealthy);
```

### 测试4: 价格差异验证测试

**测试目的**: 验证买入价和卖出价的差异合理性

**测试步骤**:
1. 获取价格数据
2. 计算买入价和卖出价的差异
3. 验证差异在合理范围内

**预期结果**:
```java
PriceData priceData = jupiterClient.getPumpPrice();
BigDecimal buyPrice = priceData.getBuyPrice();
BigDecimal sellPrice = priceData.getSellPrice();

// 价格差异应该在合理范围内 (例如不超过5%)
BigDecimal priceDifference = buyPrice.subtract(sellPrice).abs();
BigDecimal averagePrice = buyPrice.add(sellPrice).divide(new BigDecimal("2"), 6, RoundingMode.HALF_UP);
BigDecimal priceDifferencePercent = priceDifference.divide(averagePrice, 6, RoundingMode.HALF_UP)
                                                   .multiply(new BigDecimal("100"));

assertTrue(priceDifferencePercent.compareTo(new BigDecimal("5")) <= 0);
```

## 🚨 错误处理测试

### 测试5: 网络异常处理

**测试目的**: 验证网络异常时的错误处理

**测试方法**:
1. 临时修改API URL为无效地址
2. 调用相关方法
3. 验证错误处理

**预期结果**:
- 方法应该返回包含错误信息的PriceData对象
- 不应该抛出未处理的异常
- 应该记录适当的错误日志

### 测试6: 响应格式异常处理

**测试目的**: 验证API响应格式异常时的处理

**测试场景**:
- 空响应
- 格式错误的JSON
- 缺少必要字段的响应

## 📈 性能测试

### 测试7: 响应时间测试

**测试目的**: 验证API响应时间在可接受范围内

**测试方法**:
```java
long startTime = System.currentTimeMillis();
PriceData result = jupiterClient.getPumpPrice();
long endTime = System.currentTimeMillis();
long responseTime = endTime - startTime;

assertTrue(responseTime < 5000); // 响应时间应该小于5秒
```

### 测试8: 并发请求测试

**测试目的**: 验证并发请求的处理能力

**测试方法**:
```java
ExecutorService executor = Executors.newFixedThreadPool(10);
List<Future<PriceData>> futures = new ArrayList<>();

for (int i = 0; i < 20; i++) {
    futures.add(executor.submit(() -> jupiterClient.getPumpPrice()));
}

for (Future<PriceData> future : futures) {
    PriceData result = future.get();
    assertNotNull(result);
}
```

## 🔄 集成测试

### 测试9: 完整系统集成测试

**测试目的**: 验证修改后的Jupiter客户端在完整系统中的表现

**测试步骤**:
1. 启动完整的PUMP监控系统
2. 观察输出格式
3. 验证价格数据的准确性

**预期结果**:
- 输出应该显示明确区分的买入和卖出价格
- 价格差异应该反映真实的市场状况
- 系统应该稳定运行而不出现异常

## 📝 手动测试清单

### 准备工作
- [ ] 检查Jupiter API文档确认端点地址
- [ ] 确认PUMP代币的正确地址
- [ ] 验证网络连接和API访问权限

### 功能测试
- [ ] Price API V2基础功能正常
- [ ] Quote API交易报价正常
- [ ] 买入价和卖出价格正确分离
- [ ] 价格差异在合理范围内
- [ ] API健康检查正常
- [ ] 错误处理机制有效

### 集成测试
- [ ] 与Gate.io API集成正常
- [ ] 套利分析逻辑正确
- [ ] 输出格式符合要求
- [ ] 系统稳定性良好

## 🎯 验收标准

### 核心功能验收
1. **价格分离**: 买入价和卖出价必须来自不同的数据源或计算方式
2. **数据准确性**: 价格数据应该反映真实的市场状况
3. **错误处理**: 所有异常情况都应该被妥善处理
4. **性能标准**: API响应时间应该在5秒以内

### 质量标准
1. **日志记录**: 所有重要操作都应该有适当的日志记录
2. **代码质量**: 代码应该易于理解和维护
3. **测试覆盖**: 主要功能应该有相应的测试覆盖

## 📊 测试报告模板

```markdown
# Jupiter API测试报告

## 测试环境
- 测试日期: YYYY-MM-DD
- 测试人员: 
- 系统版本: 

## 测试结果
| 测试用例 | 状态 | 备注 |
|----------|------|------|
| Price API V2基础功能 | ✅/❌ | |
| Quote API交易报价 | ✅/❌ | |
| 价格差异验证 | ✅/❌ | |
| 错误处理 | ✅/❌ | |
| 性能测试 | ✅/❌ | |
| 集成测试 | ✅/❌ | |

## 问题和建议
1. 
2. 
3. 

## 结论
- 总体评价: 
- 是否可以部署: 
- 下一步行动: 
```

---

**文档版本**: 1.0  
**创建日期**: 2025-01-15  
**维护者**: AI Agent 