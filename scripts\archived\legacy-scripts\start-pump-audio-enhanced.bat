@echo off
title PUMP音频增强版启动器

echo ================================================
echo           PUMP30 音频增强版启动器
echo ================================================
echo.

REM 设置UTF-8控制台
chcp 65001 >nul 2>&1

echo [信息] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Java环境，请先安装Java
    pause
    exit /b 1
)

echo [信息] 检查JAR文件...
if not exist "pump30.jar" (
    echo [错误] pump30.jar 文件不存在
    pause
    exit /b 1
)

echo [信息] 配置Java音频参数...

REM Java音频系统优化参数
set AUDIO_OPTS=-Dsun.sound.useNewAudioEngine=false
set AUDIO_OPTS=%AUDIO_OPTS% -Djavax.sound.sampled.Clip=com.sun.media.sound.DirectAudioDeviceProvider
set AUDIO_OPTS=%AUDIO_OPTS% -Djavax.sound.sampled.SourceDataLine.bufferSize=4096
set AUDIO_OPTS=%AUDIO_OPTS% -Djava.awt.headless=false

REM 编码和时区参数
set ENCODING_OPTS=-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.timezone=Asia/Shanghai

REM 内存和性能参数
set JVM_OPTS=-Xms256m -Xmx512m -XX:+UseG1GC

echo [信息] 启动PUMP30价格监控系统...
echo [提示] 如果没有听到声音，请检查Windows音量混合器中Java应用的音量
echo [提示] 按 Ctrl+C 停止程序
echo.

REM 合并所有参数启动
java %JVM_OPTS% %AUDIO_OPTS% %ENCODING_OPTS% -jar pump30.jar

echo.
echo [信息] PUMP30价格监控系统已停止
echo [提示] 如果遇到音频问题，请运行 diagnose-audio.bat 进行诊断
pause 