# PUMP代币价格监控系统 - 技术文档中心

## 📚 文档结构概览

本文档中心为PUMP代币价格监控系统提供完整的技术文档支持，采用标准化的Markdown格式和清晰的分类结构。

## 📁 文档分类结构

```
docs/
├── 01-system-design/           # 系统设计
├── 02-api-integration/         # API集成
├── 03-deployment-ops/          # 部署运维
├── 04-monitoring-alerting/     # 告警与监控
├── 05-testing-docs/           # 测试文档
├── 06-issue-fixes/            # 问题修复
├── 07-project-management/     # 项目管理
├── 08-reference-materials/    # 参考资料
└── templates/                 # 文档模板
```

## 🎯 快速导航

### 👨‍💻 开发人员
- [系统架构概览](01-system-design/系统架构概览_Architecture-Overview.md)
- [系统架构详解](01-system-design/系统架构详解_Architecture-Details.md)
- [核心实现逻辑](01-system-design/核心实现逻辑_Implementation-Logic.md)
- [实现逻辑补充](01-system-design/实现逻辑补充_Implementation-Details.md)
- [API集成指南](02-api-integration/API集成指南_Integration-Guide.md)
- [Jupiter集成详解](02-api-integration/Jupiter集成详解_Jupiter-Integration.md)

### 🚀 运维人员
- [部署配置指南](03-deployment-ops/部署配置指南_Deployment-Guide.md)
- [完整环境配置](03-deployment-ops/完整环境配置_Complete-Environment-Setup.md)
- [JSON配置说明](03-deployment-ops/JSON配置说明_JSON-Configuration-Guide.md)
- [JAR构建指南](03-deployment-ops/JAR构建指南_JAR-Build-Guide.md)
- [监控告警配置](04-monitoring-alerting/监控告警配置_Monitoring-Setup.md)
- [自定义音频指南](04-monitoring-alerting/自定义音频指南_Custom-Audio-Guide.md)

### 🧪 测试人员
- [测试策略文档](05-testing-docs/测试策略文档_Testing-Strategy.md)
- [测试策略补充](05-testing-docs/测试策略补充_Testing-Strategy-Details.md)
- [验证报告](05-testing-docs/验证报告_Verification-Report.md)

### 🔧 问题排查
- [故障排查指南](06-issue-fixes/故障排查指南_Troubleshooting-Guide.md)
- [系统优化修复](06-issue-fixes/系统优化修复_System-Optimization-Fix.md)
- [价格计算修复](06-issue-fixes/价格计算修复_Price-Calculation-Fix.md)

### 📋 项目管理
- [项目状态报告](07-project-management/项目状态报告_Project-Status.md)
- [项目状态详情](07-project-management/项目状态详情_Project-Status-Details.md)
- [MVP版本说明](07-project-management/MVP版本说明_MVP-Version-Guide.md)

## 📖 文档标准

### YAML元数据格式
```yaml
---
title: "文档标题"
author: "作者姓名"
created: "2025-01-16"
updated: "2025-01-16"
version: "1.0"
category: "文档分类"
tags: ["tag1", "tag2"]
status: "active"
---
```

### 标准章节结构
1. **功能说明** - 功能概述和目标
2. **实现方式** - 技术实现细节
3. **图示说明** - Mermaid图表展示
4. **配置示例** - 具体配置代码
5. **更新记录** - 版本变更历史
- **构建JAR** → [jar-build-guide.md](jar-build-guide.md)

## 🔄 文档维护

### 更新流程
1. 选择合适的文档模板 (参考 `templates/` 目录)
2. 修改相应的.md文件，遵循标准章节结构
3. 更新YAML元数据中的updated字段和version
4. 在更新记录表格中添加变更说明
5. 提交到版本控制系统并进行文档审核

### 质量标准
- ✅ 包含完整的YAML元数据 (title, author, created, updated, version, category, tags, status)
- ✅ 遵循标准章节结构 (功能说明 → 实现方式 → 图示说明 → 配置示例 → 更新记录)
- ✅ 包含Mermaid图表说明 (架构图、流程图、时序图等)
- ✅ 代码示例可执行且包含注释
- ✅ 链接有效性检查和相关文档引用

### 文档模板使用
- 📋 **新建文档**: 从 `templates/` 目录选择合适模板
- 🔧 **命名规范**: `中文功能描述_English-Name.md`
- 📊 **图表标准**: 使用Mermaid语法，包含样式配置
- 📝 **代码规范**: 包含语言标识和详细注释

## 📊 项目状态

- **当前版本**: MVP 1.0 (标准化文档体系)
- **文档完整度**: 90% (核心文档已完成)
- **模板覆盖率**: 100% (8个分类，24个模板)
- **最后更新**: 2025-01-16
- **维护状态**: 活跃维护中

### 文档统计
| 分类 | 文档数量 | 完成状态 | 模板可用 |
|------|----------|----------|----------|
| 系统设计 | 4 | ✅ 完成 | ✅ 可用 |
| API集成 | 5 | ✅ 完成 | ✅ 可用 |
| 部署运维 | 6 | ✅ 完成 | ✅ 可用 |
| 监控告警 | 4 | ✅ 完成 | ✅ 可用 |
| 测试文档 | 3 | ✅ 完成 | ✅ 可用 |
| 问题修复 | 8 | ✅ 完成 | ✅ 可用 |
| 项目管理 | 3 | ✅ 完成 | ✅ 可用 |
| 参考资料 | 多个 | ✅ 整理完成 | ✅ 可用 |
| 文档模板 | 1 | ✅ 完成 | ✅ 可用 |

---

**文档维护团队**: PUMP开发团队
**技术栈**: Java 11 + Spring Boot + Gate.io/Jupiter API
**文档标准**: Markdown + YAML Front Matter + Mermaid图表
**联系方式**: 通过项目Issue反馈文档问题

💡 **提示**: 所有文档都使用标准化Markdown格式编写，包含YAML元数据和Mermaid图表，可在任何支持Markdown的编辑器中查看。
