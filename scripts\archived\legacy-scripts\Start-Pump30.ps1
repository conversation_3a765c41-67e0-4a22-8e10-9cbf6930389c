# PowerShell script for PUMP30 with proper UTF-8 support
# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "           PUMP30 价格监控系统" -ForegroundColor Yellow
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host ""

# Check Java environment
try {
    $javaVersion = java -version 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Java not found"
    }
    Write-Host "✅ Java环境检查通过" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到Java环境，请先安装Java 8+" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""

# Check JAR file
if (Test-Path "pump30.jar") {
    Write-Host "✅ JAR文件存在: pump30.jar" -ForegroundColor Green
} else {
    Write-Host "❌ 错误: JAR文件不存在: pump30.jar" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "ℹ️  配置已内置到JAR文件中" -ForegroundColor Blue
Write-Host ""
Write-Host "🚀 启动PUMP30价格监控系统..." -ForegroundColor Yellow
Write-Host "💡 按 Ctrl+C 停止程序" -ForegroundColor Gray
Write-Host ""

# Set environment variables for UTF-8
$env:JAVA_TOOL_OPTIONS = "-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8"

try {
    # Run the JAR with proper encoding
    java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar pump30.jar
} catch {
    Write-Host ""
    Write-Host "❌ 程序运行出错: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "🛑 PUMP30价格监控系统已停止" -ForegroundColor Yellow
    Read-Host "按任意键退出"
}
