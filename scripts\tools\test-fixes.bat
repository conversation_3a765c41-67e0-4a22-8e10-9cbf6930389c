@echo off
REM 强制设置控制台编码为UTF-8以避免乱码
chcp 65001 > nul
echo 🧪 ====== 修复效果测试脚本 ======
echo 📅 测试时间: %date% %time%
echo.

cd /d "%~dp0"

echo 📋 正在验证修复效果...
echo.

REM 1. 验证编码修复
echo 🧪 1. 验证控制台编码修复...
echo ✅ 如果您能正常看到这些emoji和中文字符，说明编码问题已修复！
echo 🎵 音频播放 🔊 系统监控 📊 价格差异 💰 套利机会
echo.

REM 2. 验证配置文件
echo 🧪 2. 检查配置文件...
if exist "src\main\resources\pump-config.json" (
    findstr /C:"CUSTOM_SIMPLIFIED" "src\main\resources\pump-config.json" > nul
    if !errorlevel! == 0 (
        echo ✅ 配置文件已正确设置为 CUSTOM_SIMPLIFIED
    ) else (
        echo ⚠️ 配置文件设置可能不正确
    )
) else (
    echo ❌ 配置文件不存在
)

echo.

REM 3. 测试音频播放修复
echo 🧪 3. 测试修复后的音频播放功能...
choice /M "是否要测试音频播放功能"
if !errorlevel! == 1 (
    echo.
    echo 🎵 启动音频测试...
    tools\test-simplified-audio.bat
) else (
    echo ⏭️ 跳过音频测试
)

echo.

REM 4. 快速系统测试
echo 🧪 4. 快速系统测试...
choice /M "是否要启动PUMP系统进行完整测试"
if !errorlevel! == 1 (
    echo.
    echo 🚀 启动PUMP系统...
    echo 💡 提示：启动后可以手动触发测试音频播放
    echo.
    pump-fixed.bat
) else (
    echo ⏭️ 跳过系统测试
)

echo.
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 📊 修复总结:
echo.
echo ✅ 已修复的问题:
echo    🔤 控制台编码乱码问题
echo    🎵 简化音频播放方法的空指针异常
echo    📁 配置文件设置为CUSTOM_SIMPLIFIED
echo    🔧 增强的启动脚本和工具
echo.
echo 🚀 推荐使用方式:
echo    pump-fixed.bat      (修复版启动脚本)
echo    pump30.bat          (标准启动脚本，已修复编码)
echo.
echo 🛠️  工具脚本:
echo    tools\test-simplified-audio.bat    (测试音频功能)
echo    tools\update-audio-config.bat      (切换音频方法)
echo    verify-audio-fix.bat               (验证修复状态)
echo.

pause 