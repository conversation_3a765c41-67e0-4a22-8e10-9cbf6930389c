package com.pump.util;

import javax.sound.sampled.*;
import java.awt.Toolkit;
import java.io.InputStream;
import java.io.BufferedInputStream;

/**
 * 强力音频测试工具
 * 提供多种音频播放方式，确保用户能听到声音
 */
public class PowerfulAudioTester {
    
    /**
     * 执行全面的音频测试
     */
    public static void runFullAudioTest() {
        System.out.println("🎵 ========== 开始强力音频测试 ==========");
        
        // 测试1: 强力系统蜂鸣
        testPowerfulSystemBeep();
        
        // 等待3秒
        sleep(3000);
        
        // 测试2: 测试所有音频设备
        testAllAudioDevices();
        
        // 等待3秒
        sleep(3000);
        
        // 测试3: 强力WAV播放
        testPowerfulWavPlayback();
        
        System.out.println("🎵 ========== 强力音频测试完成 ==========");
    }
    
    /**
     * 强力系统蜂鸣测试
     */
    public static void testPowerfulSystemBeep() {
        System.out.println("\n📢 === 强力系统蜂鸣测试 ===");
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            
            // 连续播放多次强力蜂鸣
            for (int round = 1; round <= 3; round++) {
                System.out.println("🔊 第 " + round + " 轮强力蜂鸣 (每轮10次)");
                
                for (int i = 0; i < 10; i++) {
                    System.out.print("🔔 ");
                    toolkit.beep();
                    Thread.sleep(100); // 快速连续蜂鸣
                }
                
                System.out.println("\n⏳ 等待1秒...");
                Thread.sleep(1000);
            }
            
            System.out.println("✅ 强力系统蜂鸣测试完成");
            
        } catch (Exception e) {
            System.out.println("❌ 强力系统蜂鸣测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试所有音频设备
     */
    public static void testAllAudioDevices() {
        System.out.println("\n🎧 === 测试所有音频设备 ===");
        try {
            Mixer.Info[] mixerInfos = AudioSystem.getMixerInfo();
            System.out.println("📋 发现 " + mixerInfos.length + " 个音频设备");
            
            for (int i = 0; i < mixerInfos.length; i++) {
                Mixer.Info info = mixerInfos[i];
                System.out.println("\n🎵 测试设备 " + (i + 1) + ": " + info.getName());
                System.out.println("   描述: " + info.getDescription());
                
                try {
                    Mixer mixer = AudioSystem.getMixer(info);
                    Line.Info[] sourceLineInfos = mixer.getSourceLineInfo();
                    
                    if (sourceLineInfos.length > 0) {
                        System.out.println("   ✅ 设备支持音频输出，尝试播放测试音频...");
                        testDeviceWithWav(mixer, "up.wav");
                    } else {
                        System.out.println("   ⚠️ 设备不支持音频输出");
                    }
                    
                } catch (Exception e) {
                    System.out.println("   ❌ 设备测试失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ 音频设备测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 在指定设备上测试WAV播放
     */
    private static void testDeviceWithWav(Mixer mixer, String fileName) {
        InputStream rawStream = null;
        InputStream audioStream = null;
        AudioInputStream audioInputStream = null;
        Clip clip = null;
        
        try {
            // 加载音频资源
            String resourcePath = "/audio/" + fileName;
            rawStream = PowerfulAudioTester.class.getResourceAsStream(resourcePath);
            if (rawStream == null) {
                System.out.println("     ❌ 无法加载音频文件: " + fileName);
                return;
            }
            
            // 处理音频流
            audioStream = rawStream.markSupported() ? 
                rawStream : new BufferedInputStream(rawStream, 8192);
            
            // 创建AudioInputStream
            audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            
            // 尝试在指定设备上创建Clip
            try {
                clip = (Clip) mixer.getLine(new DataLine.Info(Clip.class, audioInputStream.getFormat()));
                System.out.println("     ✅ 使用指定设备创建音频剪辑");
            } catch (Exception e) {
                clip = AudioSystem.getClip();
                System.out.println("     ⚠️ 使用默认设备创建音频剪辑");
            }
            
            clip.open(audioInputStream);
            
            // 设置音量到最大
            if (clip.isControlSupported(FloatControl.Type.MASTER_GAIN)) {
                FloatControl gainControl = (FloatControl) clip.getControl(FloatControl.Type.MASTER_GAIN);
                gainControl.setValue(gainControl.getMaximum());
                System.out.println("     🔊 音量设置到最大: " + gainControl.getValue() + " dB");
            }
            
            // 播放音频
            System.out.println("     🎵 开始播放音频...");
            clip.start();
            
            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(50);
            }
            
            System.out.println("     ✅ 音频播放完成");
            
        } catch (Exception e) {
            System.out.println("     ❌ 设备音频播放失败: " + e.getMessage());
        } finally {
            // 清理资源
            try {
                if (clip != null) clip.close();
                if (audioInputStream != null) audioInputStream.close();
                if (audioStream != null) audioStream.close();
                if (rawStream != null && rawStream != audioStream) rawStream.close();
            } catch (Exception e) {
                // 忽略清理异常
            }
        }
    }
    
    /**
     * 强力WAV播放测试
     */
    public static void testPowerfulWavPlayback() {
        System.out.println("\n🎵 === 强力WAV播放测试 ===");
        
        String[] audioFiles = {"up.wav", "down.wav"};
        
        for (String fileName : audioFiles) {
            System.out.println("\n🎧 测试播放: " + fileName);
            
            // 连续播放3次，确保能听到
            for (int i = 1; i <= 3; i++) {
                System.out.println("🔊 第 " + i + " 次播放 " + fileName);
                playWavFile(fileName);
                sleep(1000); // 间隔1秒
            }
        }
    }
    
    /**
     * 播放WAV文件
     */
    private static void playWavFile(String fileName) {
        InputStream rawStream = null;
        InputStream audioStream = null;
        AudioInputStream audioInputStream = null;
        Clip clip = null;
        
        try {
            // 加载音频资源
            String resourcePath = "/audio/" + fileName;
            rawStream = PowerfulAudioTester.class.getResourceAsStream(resourcePath);
            if (rawStream == null) {
                System.out.println("   ❌ 无法加载音频文件: " + fileName);
                return;
            }
            
            // 处理音频流
            audioStream = rawStream.markSupported() ? 
                rawStream : new BufferedInputStream(rawStream, 8192);
            
            // 创建AudioInputStream
            audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            
            // 创建音频剪辑
            clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            
            // 设置音量到最大
            if (clip.isControlSupported(FloatControl.Type.MASTER_GAIN)) {
                FloatControl gainControl = (FloatControl) clip.getControl(FloatControl.Type.MASTER_GAIN);
                gainControl.setValue(gainControl.getMaximum());
            }
            
            // 播放音频
            clip.start();
            
            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(50);
            }
            
            System.out.println("   ✅ " + fileName + " 播放完成");
            
        } catch (Exception e) {
            System.out.println("   ❌ " + fileName + " 播放失败: " + e.getMessage());
        } finally {
            // 清理资源
            try {
                if (clip != null) clip.close();
                if (audioInputStream != null) audioInputStream.close();
                if (audioStream != null) audioStream.close();
                if (rawStream != null && rawStream != audioStream) rawStream.close();
            } catch (Exception e) {
                // 忽略清理异常
            }
        }
    }
    
    /**
     * 线程睡眠工具方法
     */
    private static void sleep(int milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
