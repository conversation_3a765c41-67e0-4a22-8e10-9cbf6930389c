package com.pump;

import javax.sound.sampled.*;
import java.io.BufferedReader;
import java.io.InputStreamReader;

/**
 * Windows系统音频诊断工具
 * 专门针对Windows系统的音频问题进行诊断
 */
public class WindowsAudioDiagnostic {

    public static void main(String[] args) {
        System.out.println("🪟 ========== Windows音频系统诊断 ==========");
        System.out.println("📢 专门针对Windows系统进行音频诊断...");
        System.out.println();

        // 1. 检查Windows音频服务
        checkWindowsAudioService();
        
        // 2. 检查音频设备状态
        checkAudioDeviceStatus();
        
        // 3. 检查Java音频在Windows上的特殊问题
        checkJavaAudioOnWindows();
        
        // 4. 尝试强制使用DirectSound
        tryDirectSound();
        
        // 5. 提供解决建议
        provideSolutions();
        
        System.out.println("🪟 ========== Windows诊断完成 ==========");
    }

    /**
     * 检查Windows音频服务
     */
    private static void checkWindowsAudioService() {
        System.out.println("🔧 === Windows音频服务检查 ===");
        
        try {
            // 检查Windows Audio服务状态
            System.out.println("📋 检查Windows Audio服务状态...");
            
            ProcessBuilder pb = new ProcessBuilder("sc", "query", "AudioSrv");
            Process process = pb.start();
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            boolean serviceFound = false;
            
            while ((line = reader.readLine()) != null) {
                System.out.println("   " + line);
                if (line.contains("RUNNING")) {
                    serviceFound = true;
                    System.out.println("✅ Windows Audio服务正在运行");
                }
            }
            
            if (!serviceFound) {
                System.out.println("❌ Windows Audio服务可能未运行！");
                System.out.println("💡 建议：请以管理员身份运行 'net start AudioSrv'");
            }
            
        } catch (Exception e) {
            System.out.println("⚠️ 无法检查Windows Audio服务: " + e.getMessage());
            System.out.println("💡 请手动检查Windows音频服务是否正常运行");
        }
    }

    /**
     * 检查音频设备状态
     */
    private static void checkAudioDeviceStatus() {
        System.out.println("\n🎧 === 音频设备状态检查 ===");
        
        try {
            // 使用PowerShell检查音频设备
            System.out.println("📋 检查音频播放设备...");
            
            ProcessBuilder pb = new ProcessBuilder("powershell", "-Command", 
                "Get-AudioDevice -List | Where-Object {$_.Type -eq 'Playback'}");
            Process process = pb.start();
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            boolean hasPlaybackDevice = false;
            
            while ((line = reader.readLine()) != null) {
                System.out.println("   " + line);
                if (line.contains("Default") || line.contains("扬声器") || line.contains("Speaker")) {
                    hasPlaybackDevice = true;
                }
            }
            
            if (hasPlaybackDevice) {
                System.out.println("✅ 检测到音频播放设备");
            } else {
                System.out.println("❌ 未检测到可用的音频播放设备！");
            }
            
        } catch (Exception e) {
            System.out.println("⚠️ 无法检查音频设备状态: " + e.getMessage());
            
            // 备用方法：检查Java能看到的设备
            System.out.println("📋 使用Java检查音频设备...");
            checkJavaAudioDevices();
        }
    }

    /**
     * 使用Java检查音频设备
     */
    private static void checkJavaAudioDevices() {
        try {
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            System.out.println("📊 Java检测到的音频设备数量: " + mixers.length);
            
            for (int i = 0; i < mixers.length; i++) {
                Mixer.Info info = mixers[i];
                System.out.println("🎧 设备 " + (i+1) + ":");
                System.out.println("   名称: " + info.getName());
                System.out.println("   描述: " + info.getDescription());
                System.out.println("   供应商: " + info.getVendor());
                
                // 检查设备是否可用
                try {
                    Mixer mixer = AudioSystem.getMixer(info);
                    Line.Info[] sourceLines = mixer.getSourceLineInfo();
                    Line.Info[] targetLines = mixer.getTargetLineInfo();
                    
                    System.out.println("   输出线路: " + sourceLines.length);
                    System.out.println("   输入线路: " + targetLines.length);
                    
                    // 尝试打开设备
                    if (sourceLines.length > 0) {
                        System.out.println("   状态: 支持音频输出");
                        
                        // 尝试创建一个Clip来测试设备
                        AudioFormat testFormat = new AudioFormat(44100, 16, 2, true, false);
                        DataLine.Info clipInfo = new DataLine.Info(Clip.class, testFormat);
                        
                        if (mixer.isLineSupported(clipInfo)) {
                            System.out.println("   兼容性: ✅ 支持标准音频格式");
                        } else {
                            System.out.println("   兼容性: ❌ 不支持标准音频格式");
                        }
                    } else {
                        System.out.println("   状态: 不支持音频输出");
                    }
                    
                } catch (Exception e) {
                    System.out.println("   状态: ❌ 设备访问失败 - " + e.getMessage());
                }
                
                System.out.println();
            }
            
        } catch (Exception e) {
            System.out.println("❌ Java音频设备检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查Java音频在Windows上的特殊问题
     */
    private static void checkJavaAudioOnWindows() {
        System.out.println("☕ === Java音频Windows特殊问题检查 ===");
        
        try {
            // 检查Java版本
            String javaVersion = System.getProperty("java.version");
            String javaVendor = System.getProperty("java.vendor");
            System.out.println("📋 Java版本: " + javaVersion + " (" + javaVendor + ")");
            
            // 检查操作系统
            String osName = System.getProperty("os.name");
            String osVersion = System.getProperty("os.version");
            System.out.println("📋 操作系统: " + osName + " " + osVersion);
            
            // 检查音频系统属性
            System.out.println("📋 音频系统属性:");
            String[] audioProps = {
                "javax.sound.sampled.Clip",
                "javax.sound.sampled.Port",
                "javax.sound.sampled.SourceDataLine",
                "javax.sound.sampled.TargetDataLine"
            };
            
            for (String prop : audioProps) {
                String value = System.getProperty(prop);
                System.out.println("   " + prop + " = " + (value != null ? value : "未设置"));
            }
            
            // 检查是否有音频相关的安全限制
            System.out.println("📋 检查音频权限...");
            try {
                AudioSystem.getMixerInfo();
                System.out.println("✅ 音频系统访问权限正常");
            } catch (SecurityException e) {
                System.out.println("❌ 音频系统访问被安全策略阻止: " + e.getMessage());
            }
            
        } catch (Exception e) {
            System.out.println("❌ Java音频Windows检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 尝试强制使用DirectSound
     */
    private static void tryDirectSound() {
        System.out.println("\n🎮 === 尝试DirectSound ===");
        
        try {
            System.out.println("📋 当前音频系统属性:");
            
            // 显示当前的音频混合器
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            for (Mixer.Info info : mixers) {
                if (info.getName().toLowerCase().contains("direct") || 
                    info.getName().toLowerCase().contains("primary")) {
                    System.out.println("🎧 找到DirectSound相关设备: " + info.getName());
                    
                    // 尝试使用这个设备
                    try {
                        Mixer mixer = AudioSystem.getMixer(info);
                        System.out.println("✅ DirectSound设备可访问");
                        
                        // 尝试在这个设备上播放测试音
                        testDirectSoundPlayback(mixer, info.getName());
                        
                    } catch (Exception e) {
                        System.out.println("❌ DirectSound设备访问失败: " + e.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ DirectSound测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 在DirectSound设备上测试播放
     */
    private static void testDirectSoundPlayback(Mixer mixer, String deviceName) {
        System.out.println("🔊 在DirectSound设备上测试播放: " + deviceName);
        
        try {
            // 生成简单的测试音频
            int sampleRate = 44100;
            int duration = 1;
            byte[] audioData = generateTestTone(440.0, duration, sampleRate);
            
            AudioFormat format = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                sampleRate, 16, 1, 2, sampleRate, false
            );
            
            // 尝试在指定设备上创建SourceDataLine
            DataLine.Info info = new DataLine.Info(SourceDataLine.class, format);
            
            if (mixer.isLineSupported(info)) {
                SourceDataLine line = (SourceDataLine) mixer.getLine(info);
                line.open(format);
                line.start();
                
                System.out.println("🎵 DirectSound播放测试 - 请注意听！");
                line.write(audioData, 0, audioData.length);
                
                line.drain();
                line.stop();
                line.close();
                
                System.out.println("✅ DirectSound播放测试完成");
                
            } else {
                System.out.println("❌ DirectSound设备不支持所需格式");
            }
            
        } catch (Exception e) {
            System.out.println("❌ DirectSound播放测试失败: " + e.getMessage());
        }
    }

    /**
     * 生成测试音调
     */
    private static byte[] generateTestTone(double frequency, int durationSeconds, int sampleRate) {
        int numSamples = durationSeconds * sampleRate;
        byte[] audioData = new byte[numSamples * 2];
        
        for (int i = 0; i < numSamples; i++) {
            double time = (double) i / sampleRate;
            short sample = (short) (0.5 * Short.MAX_VALUE * Math.sin(2 * Math.PI * frequency * time));
            
            audioData[i * 2] = (byte) (sample & 0xFF);
            audioData[i * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
        }
        
        return audioData;
    }

    /**
     * 提供解决建议
     */
    private static void provideSolutions() {
        System.out.println("\n💡 === 解决建议 ===");
        System.out.println("如果以上所有测试都没有声音，请尝试以下解决方案：");
        System.out.println();
        
        System.out.println("🔧 1. 系统级解决方案:");
        System.out.println("   - 检查Windows音量混合器中Java的音量设置");
        System.out.println("   - 确认默认播放设备设置正确");
        System.out.println("   - 重启Windows Audio服务");
        System.out.println("   - 更新音频驱动程序");
        System.out.println();
        
        System.out.println("🔧 2. Java特定解决方案:");
        System.out.println("   - 尝试不同版本的Java (OpenJDK vs Oracle JDK)");
        System.out.println("   - 添加JVM参数: -Djavax.sound.sampled.Clip=com.sun.media.sound.DirectAudioDeviceProvider");
        System.out.println("   - 添加JVM参数: -Dsun.sound.useNewAudioEngine=false");
        System.out.println();
        
        System.out.println("🔧 3. IDE特定解决方案:");
        System.out.println("   - 重启IDE");
        System.out.println("   - 检查IDE的音频权限设置");
        System.out.println("   - 尝试在命令行中直接运行Java程序");
        System.out.println();
        
        System.out.println("🔧 4. 硬件检查:");
        System.out.println("   - 确认扬声器/耳机连接正常");
        System.out.println("   - 测试其他应用程序的音频播放");
        System.out.println("   - 检查音频设备是否被其他程序独占");
    }
}
