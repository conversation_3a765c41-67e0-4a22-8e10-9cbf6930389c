package com.pump.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pump.model.PriceData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * Jupiter Ultra API客户端
 * 使用统一路由引擎，整合Metis v1和Jupiter Z
 * 提供更好的价格发现和更多功能参数
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
@Component
public class JupiterUltraApiClient {
    
    private static final Logger logger = LoggerFactory.getLogger(JupiterUltraApiClient.class);
    
    @Value("${jupiter.api.ultra-url}")
    private String ultraUrl;
    
    @Value("${jupiter.api.timeout}")
    private int timeout;
    
    @Value("${proxy.enabled:false}")
    private boolean proxyEnabled;
    
    @Value("${proxy.host:127.0.0.1}")
    private String proxyHost;
    
    @Value("${proxy.port:7890}")
    private int proxyPort;
    
    @Value("${proxy.type:HTTP}")
    private String proxyType;

    // JSON解析器
    private final ObjectMapper objectMapper = new ObjectMapper();

    // PUMP代币地址
    private static final String PUMP_TOKEN = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn";
    
    // 固定的taker地址（用于获取可执行交易）
    private static final String TAKER_ADDRESS = "778v7yvRRtW6YiHxZGhEjNsE8vwz3i9eSWtspxpiC1y7";
    
    /**
     * 使用Ultra API获取报价价格
     * 整合了Metis v1和Jupiter Z的统一路由引擎
     *
     * @param amount 交易数量（PUMP代币数量）
     * @param isBuy 是否为买入操作
     * @return 单个PUMP的USDT价格
     */
    public BigDecimal getUltraQuotePrice(BigDecimal amount, boolean isBuy) {
        logger.debug("获取Jupiter Ultra报价，PUMP数量: {}, 买入: {}", amount, isBuy);

        try {
            String inputMint, outputMint;
            BigDecimal queryAmount;

            if (isBuy) {
                // 买入PUMP：用USDT买PUMP
                inputMint = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"; // USDT
                outputMint = PUMP_TOKEN; // PUMP
                queryAmount = new BigDecimal("6000"); // 固定使用$6000
                logger.debug("买入PUMP，使用固定金额: {}USDT", queryAmount);
            } else {
                // 卖出PUMP：用PUMP换USDT
                inputMint = PUMP_TOKEN; // PUMP
                outputMint = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"; // USDT
                queryAmount = amount; // 使用100万个PUMP
                logger.debug("卖出{}个PUMP", amount);
            }

            // 转换为最小单位（6位精度）
            BigDecimal amountInSmallestUnit = queryAmount.multiply(new BigDecimal("1000000"));

            // 构建Ultra API URL - 包含更多参数
            String url = String.format("%s?inputMint=%s&outputMint=%s&amount=%s&swapMode=ExactIn&slippageBps=50" +
                                     "&broadcastFeeType=maxCap&priorityFeeLamports=1000000&useWsol=false" +
                                     "&asLegacyTransaction=false&excludeDexes=&excludeRouters=&taker=%s",
                                     ultraUrl, inputMint, outputMint, amountInSmallestUnit.toBigInteger(),
                                     TAKER_ADDRESS);

            logger.debug("Jupiter Ultra API请求URL: {}", url);
            logger.debug("请求参数: inputMint={}, outputMint={}, amount={}, isBuy={}",
                        inputMint, outputMint, amountInSmallestUnit.toBigInteger(), isBuy);

            // 创建连接并发送请求，带重试机制
            HttpURLConnection connection = createConnection(url);
            int responseCode = connection.getResponseCode();

            // 处理429限流错误
            if (responseCode == 429) {
                logger.warn("Jupiter Ultra API限流，等待后重试...");
                Thread.sleep(2000); // 等待2秒
                connection = createConnection(url);
                responseCode = connection.getResponseCode();
            }

            if (responseCode == 200) {
                String responseBody = readResponse(connection);
                logger.debug("Jupiter Ultra API响应: {}", responseBody);

                // 解析Ultra API响应
                BigDecimal unitPrice = parseUltraResponse(responseBody, amount, isBuy);
                if (unitPrice != null) {
                    logger.debug("Jupiter Ultra报价获取成功: {}个PUMP单价 = {} USDT ({})",
                                amount, unitPrice, isBuy ? "买入" : "卖出");
                    return unitPrice;
                } else {
                    logger.error("解析Jupiter Ultra API响应失败");
                }
            } else {
                String errorBody = readErrorResponse(connection);
                logger.error("Jupiter Ultra API请求失败，状态码: {}, 错误信息: {}", responseCode, errorBody);
            }

        } catch (Exception e) {
            logger.error("Jupiter Ultra API调用异常", e);
        }

        return null;
    }
    
    /**
     * 解析Ultra API响应
     * Ultra API返回更丰富的信息，包括可执行交易
     *
     * @param responseBody API响应体
     * @param amount 查询数量
     * @param isBuy 是否为买入
     * @return 单价
     */
    private BigDecimal parseUltraResponse(String responseBody, BigDecimal amount, boolean isBuy) {
        try {
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            // Ultra API响应结构
            String inAmount = jsonNode.path("inAmount").asText();
            String outAmount = jsonNode.path("outAmount").asText();
            
            if (inAmount.isEmpty() || outAmount.isEmpty()) {
                logger.error("Ultra API响应缺少必要字段: inAmount={}, outAmount={}", inAmount, outAmount);
                return null;
            }
            
            // 转换为正常单位
            BigDecimal inNormal = new BigDecimal(inAmount).divide(new BigDecimal("1000000"), 8, BigDecimal.ROUND_HALF_UP);
            BigDecimal outNormal = new BigDecimal(outAmount).divide(new BigDecimal("1000000"), 8, BigDecimal.ROUND_HALF_UP);
            
            BigDecimal unitPrice;
            
            if (isBuy) {
                // 买入：输入$6000 USDT，输出PUMP
                // 单价 = 输入USDT / 输出PUMP
                unitPrice = inNormal.divide(outNormal, 8, BigDecimal.ROUND_HALF_UP);
                logger.debug("买入解析: {}USDT -> {}PUMP, 单价 = {} USDT/PUMP", 
                            inNormal, outNormal, unitPrice);
                
                // 计算用$6000能买到多少个PUMP
                BigDecimal pumpsBought = outNormal;
                logger.debug("用$6000能买到: {}个PUMP", pumpsBought);
            } else {
                // 卖出：输入PUMP，输出USDT  
                // 单价 = 输出USDT / 输入PUMP
                unitPrice = outNormal.divide(inNormal, 8, BigDecimal.ROUND_HALF_UP);
                logger.debug("卖出解析: {}PUMP -> {}USDT, 单价 = {} USDT/PUMP", 
                            inNormal, outNormal, unitPrice);
            }
            
            // 检查Ultra API特有字段
            if (jsonNode.has("transaction")) {
                String transaction = jsonNode.path("transaction").asText();
                logger.debug("Ultra API返回可执行交易: {}", transaction.isEmpty() ? "空" : "有效");
            }
            
            if (jsonNode.has("swapType")) {
                String swapType = jsonNode.path("swapType").asText();
                logger.debug("交换类型: {}", swapType);
            }
            
            return unitPrice;
            
        } catch (Exception e) {
            logger.error("解析Ultra API响应失败", e);
            return null;
        }
    }
    
    /**
     * 创建HTTP连接
     */
    private HttpURLConnection createConnection(String urlString) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection;
        
        if (proxyEnabled) {
            Proxy.Type type = "SOCKS".equalsIgnoreCase(proxyType) ? Proxy.Type.SOCKS : Proxy.Type.HTTP;
            Proxy proxy = new Proxy(type, new InetSocketAddress(proxyHost, proxyPort));
            connection = (HttpURLConnection) url.openConnection(proxy);
            logger.debug("使用代理连接: {}://{}:{}", proxyType, proxyHost, proxyPort);
        } else {
            connection = (HttpURLConnection) url.openConnection();
        }
        
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(timeout);
        connection.setReadTimeout(timeout);
        connection.setRequestProperty("User-Agent", "PUMP-Monitor-Ultra/1.0");
        connection.setRequestProperty("Accept", "application/json");
        
        return connection;
    }
    
    /**
     * 读取响应内容
     */
    private String readResponse(HttpURLConnection connection) throws Exception {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        }
    }
    
    /**
     * 读取错误响应
     */
    private String readErrorResponse(HttpURLConnection connection) {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        } catch (Exception e) {
            return "无法读取错误响应: " + e.getMessage();
        }
    }
}
