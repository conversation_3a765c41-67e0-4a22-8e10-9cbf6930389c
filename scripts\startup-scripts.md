# PUMP系统启动脚本说明

## 🚀 推荐启动脚本

### `pump30.bat` - 主启动脚本
```batch
# 标准启动脚本，已修复UTF-8编码
pump30.bat
```

### `pump-fixed.bat` - 增强启动脚本
```batch
# 包含音频优化参数的启动脚本
pump-fixed.bat
```

## 📁 脚本目录结构

```
scripts/
├── startup/                # 启动脚本
│   ├── pump30.bat         # 主启动脚本
│   └── pump-fixed.bat     # 增强启动脚本
│
├── tools/                 # 工具脚本
│   ├── quick-test.bat     # 快速测试
│   └── verify-audio-fix.bat # 音频验证
│
└── archived/              # 历史脚本归档
    ├── legacy-scripts/    # 旧版脚本
    └── test-scripts/      # 测试脚本
```

## 🧹 清理计划

已移除的冗余脚本：
- pump.bat (简化版)
- start.bat (基础版)
- 各种UTF-8测试脚本
- 历史JAR文件 