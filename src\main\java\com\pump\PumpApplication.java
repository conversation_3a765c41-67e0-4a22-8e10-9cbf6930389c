package com.pump;

import com.pump.config.EncodingInitializer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import java.nio.charset.Charset;
import java.util.TimeZone;

/**
 * PUMP价格监控系统主程序
 * Spring Boot应用启动类
 *
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
@SpringBootApplication
@EnableScheduling
public class PumpApplication {

    // 静态代码块：在类加载时立即设置编码
    static {
        // 使用专门的编码初始化器强制设置UTF-8
        EncodingInitializer.forceInitializeUTF8();
    }

    /**
     * 主程序入口点
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            // 再次确保UTF-8编码设置
            EncodingInitializer.forceInitializeUTF8();

            // 设置UTF-8编码和时区
            configureSystemEncoding();

            // 设置系统属性 - 禁用Spring Boot启动横幅和详细日志
            System.setProperty("spring.main.banner-mode", "off");
            System.setProperty("spring.output.ansi.enabled", "never");

            // 启动Spring Boot应用
            SpringApplication app = new SpringApplication(PumpApplication.class);
            app.setLogStartupInfo(false);
            app.run(args);

        } catch (Exception e) {
            System.err.println("系统启动失败: " + e.getMessage());
            System.exit(1);
        }
    }

    /**
     * 配置系统编码为UTF-8
     * 确保中文字符正确显示
     */
    private static void configureSystemEncoding() {
        try {
            // 强制设置系统编码属性
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("console.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");
            System.setProperty("user.timezone", "Asia/Shanghai");

            // 设置默认时区
            TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));

            // 在Windows系统上强制设置控制台代码页
            if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                try {
                    // 执行chcp命令设置UTF-8代码页
                    ProcessBuilder pb = new ProcessBuilder("cmd", "/c", "chcp", "65001");
                    pb.redirectErrorStream(true);
                    Process process = pb.start();
                    process.waitFor();

                    // 等待一下让设置生效
                    Thread.sleep(100);
                } catch (Exception e) {
                    // 忽略错误，继续运行
                }
            }

            // 强制重新设置标准输出流
            try {
                System.setOut(new java.io.PrintStream(System.out, true, "UTF-8"));
                System.setErr(new java.io.PrintStream(System.err, true, "UTF-8"));
            } catch (Exception e) {
                // 忽略错误
            }

        } catch (Exception e) {
            System.err.println("配置系统编码时出错: " + e.getMessage());
        }
    }
} 