---
title: "PUMP代币价格监控系统故障排查指南"
author: "运维工程师"
created: "2025-01-16"
updated: "2025-01-16"
version: "1.0"
category: "issue-fixes"
tags: ["troubleshooting", "debugging", "maintenance", "support"]
status: "active"
---

# PUMP代币价格监控系统故障排查指南

## 📋 功能说明

### 故障排查概述
本指南提供PUMP代币价格监控系统常见问题的诊断方法和解决方案，帮助运维人员快速定位和解决系统故障。

### 故障分类
- **启动问题**: 系统无法正常启动
- **价格获取问题**: API调用失败或数据异常
- **告警问题**: 音频告警不工作或异常
- **音频播放问题**: mark/reset not supported错误
- **性能问题**: 响应慢或资源消耗过高
- **编码问题**: 中文字符显示异常

## 🛠️ 实现方式

### 系统健康检查
```java
public class DiagnosticTool {
    
    /**
     * 执行系统健康检查
     */
    public static void performHealthCheck() {
        System.out.println("=== PUMP系统健康检查 ===");
        
        // 1. Java环境检查
        checkJavaEnvironment();
        
        // 2. 网络连接检查
        checkNetworkConnectivity();
        
        // 3. API可用性检查
        checkApiAvailability();
        
        // 4. 音频系统检查
        checkAudioSystem();
        
        // 5. 配置文件检查
        checkConfiguration();
    }
    
    private static void checkJavaEnvironment() {
        System.out.println("\n--- Java环境检查 ---");
        System.out.println("Java版本: " + System.getProperty("java.version"));
        System.out.println("Java厂商: " + System.getProperty("java.vendor"));
        System.out.println("操作系统: " + System.getProperty("os.name"));
        System.out.println("文件编码: " + System.getProperty("file.encoding"));
        System.out.println("时区: " + System.getProperty("user.timezone"));
        
        // 检查内存使用
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory() / 1024 / 1024;
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        long usedMemory = totalMemory - freeMemory;
        
        System.out.printf("内存使用: %dMB / %dMB (最大: %dMB)%n", 
            usedMemory, totalMemory, maxMemory);
    }
    
    private static void checkNetworkConnectivity() {
        System.out.println("\n--- 网络连接检查 ---");
        
        String[] testUrls = {
            "https://api.gateio.ws",
            "https://quote-api.jup.ag",
            "https://price.jup.ag"
        };
        
        for (String url : testUrls) {
            try {
                HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                
                int responseCode = connection.getResponseCode();
                System.out.printf("%-30s: %s%n", url, 
                    responseCode == 200 ? "✅ 可访问" : "❌ 不可访问 (" + responseCode + ")");
                
            } catch (Exception e) {
                System.out.printf("%-30s: ❌ 连接失败 (%s)%n", url, e.getMessage());
            }
        }
    }
}
```

### 常见问题诊断
```java
public class CommonIssuesDiagnostic {
    
    /**
     * 诊断价格获取问题
     */
    public static void diagnosePriceIssues() {
        System.out.println("=== 价格获取问题诊断 ===");
        
        // 检查Gate.io API
        try {
            String gateUrl = "https://api.gateio.ws/api/v4/spot/tickers?currency_pair=PUMP_USDT";
            String response = callApi(gateUrl);
            
            if (response != null && response.contains("PUMP_USDT")) {
                System.out.println("✅ Gate.io API正常");
            } else {
                System.out.println("❌ Gate.io API响应异常");
            }
        } catch (Exception e) {
            System.out.println("❌ Gate.io API调用失败: " + e.getMessage());
        }
        
        // 检查Jupiter API
        try {
            String jupiterUrl = "https://price.jup.ag/v4/price?ids=pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn";
            String response = callApi(jupiterUrl);
            
            if (response != null && response.contains("price")) {
                System.out.println("✅ Jupiter Price API正常");
            } else {
                System.out.println("❌ Jupiter Price API响应异常");
            }
        } catch (Exception e) {
            System.out.println("❌ Jupiter Price API调用失败: " + e.getMessage());
        }
    }
    
    /**
     * 诊断音频告警问题 - 增强版
     */
    public static void diagnoseAudioIssues() {
        System.out.println("=== 音频告警问题诊断 ===");

        try {
            // 1. 检查音频设备
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            System.out.println("可用音频设备数量: " + mixers.length);

            for (Mixer.Info mixerInfo : mixers) {
                System.out.println("  - " + mixerInfo.getName() + ": " + mixerInfo.getDescription());
            }

            // 2. 检查音频文件 - 多路径检查
            String[] audioFiles = {"up.wav", "down.wav"};
            String[] resourcePaths = {"/", "/audio/", "/sounds/", ""};

            for (String audioFile : audioFiles) {
                boolean found = false;

                // 尝试多种加载方式
                for (String path : resourcePaths) {
                    InputStream stream = CommonIssuesDiagnostic.class.getResourceAsStream(path + audioFile);
                    if (stream != null) {
                        System.out.println("✅ " + audioFile + " 文件存在于路径: " + path);
                        try {
                            System.out.println("    文件大小: " + stream.available() + " bytes");
                            stream.close();
                        } catch (IOException e) {
                            System.out.println("    文件读取异常: " + e.getMessage());
                        }
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    // 尝试ClassLoader加载
                    InputStream stream = CommonIssuesDiagnostic.class.getClassLoader().getResourceAsStream(audioFile);
                    if (stream != null) {
                        System.out.println("✅ " + audioFile + " 通过ClassLoader加载成功");
                        stream.close();
                        found = true;
                    }
                }

                if (!found) {
                    System.out.println("❌ " + audioFile + " 文件在所有路径都未找到");
                }
            }

            // 3. 检查音频格式支持
            System.out.println("\n--- 音频格式支持检查 ---");
            AudioFormat[] formats = {
                new AudioFormat(44100, 16, 2, true, false),
                new AudioFormat(22050, 16, 1, true, false),
                new AudioFormat(8000, 8, 1, true, false)
            };

            for (AudioFormat format : formats) {
                DataLine.Info info = new DataLine.Info(Clip.class, format);
                boolean supported = AudioSystem.isLineSupported(info);
                System.out.printf("格式 [%.0fHz, %dbit, %dch]: %s%n",
                    format.getSampleRate(), format.getSampleSizeInBits(),
                    format.getChannels(), supported ? "✅ 支持" : "❌ 不支持");
            }

            // 4. 测试系统蜂鸣器
            System.out.println("\n--- 系统蜂鸣器测试 ---");
            Toolkit.getDefaultToolkit().beep();
            System.out.println("✅ 系统蜂鸣器正常");

            // 5. 测试音频播放
            System.out.println("\n--- 音频播放测试 ---");
            testAudioPlayback();

        } catch (Exception e) {
            System.out.println("❌ 音频系统检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testAudioPlayback() {
        try {
            InputStream audioStream = CommonIssuesDiagnostic.class.getResourceAsStream("/up.wav");
            if (audioStream == null) {
                audioStream = CommonIssuesDiagnostic.class.getClassLoader().getResourceAsStream("up.wav");
            }

            if (audioStream != null) {
                AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream);
                AudioFormat format = audioInputStream.getFormat();

                System.out.printf("音频格式: %.0fHz, %dbit, %dch%n",
                    format.getSampleRate(), format.getSampleSizeInBits(), format.getChannels());

                DataLine.Info info = new DataLine.Info(Clip.class, format);
                if (AudioSystem.isLineSupported(info)) {
                    System.out.println("✅ 音频格式兼容，可以播放");
                } else {
                    System.out.println("❌ 音频格式不兼容");
                }

                audioInputStream.close();
                audioStream.close();
            } else {
                System.out.println("❌ 无法加载测试音频文件");
            }
        } catch (Exception e) {
            System.out.println("❌ 音频播放测试失败: " + e.getMessage());
        }
    }
}
```

## 📊 图示说明

### 故障排查流程
```mermaid
flowchart TD
    A[发现问题] --> B[收集信息]
    B --> C[问题分类]
    
    C --> D{启动问题?}
    C --> E{价格问题?}
    C --> F{告警问题?}
    C --> G{音频播放问题?}
    C --> H{性能问题?}
    C --> I{编码问题?}
    
    D --> D1[检查Java环境]
    D1 --> D2[检查配置文件]
    D2 --> D3[检查依赖JAR]
    
    E --> E1[检查网络连接]
    E1 --> E2[检查API状态]
    E2 --> E3[检查API限制]
    
    F --> F1[检查音频设备]
    F1 --> F2[检查音频文件]
    F2 --> F3[检查告警配置]

    G --> G1[检查mark/reset支持]
    G1 --> G2[使用BufferedInputStream]
    G2 --> G3[验证音频格式]

    H --> H1[检查内存使用]
    H1 --> H2[检查CPU使用]
    H2 --> H3[检查网络延迟]

    I --> I1[检查编码设置]
    I1 --> I2[检查字体支持]
    I2 --> I3[检查控制台配置]
    
    D3 --> J[应用解决方案]
    E3 --> J
    F3 --> J
    G3 --> J
    H3 --> J
    I3 --> J
    
    J --> K[验证修复]
    K --> L{问题解决?}
    L -->|是| M[记录解决方案]
    L -->|否| N[升级处理]
    
    style M fill:#e8f5e8
    style N fill:#fff3e0
```

### 问题优先级矩阵
```mermaid
graph TB
    subgraph "高优先级"
        A1[系统无法启动]
        A2[价格获取完全失败]
        A3[内存泄漏]
    end
    
    subgraph "中优先级"
        B1[部分API调用失败]
        B2[告警不工作]
        B3[响应时间慢]
    end
    
    subgraph "低优先级"
        C1[中文显示异常]
        C2[日志格式问题]
        C3[配置优化]
    end
    
    A1 --> D[立即处理]
    A2 --> D
    A3 --> D
    
    B1 --> E[4小时内处理]
    B2 --> E
    B3 --> E
    
    C1 --> F[计划处理]
    C2 --> F
    C3 --> F
    
    style D fill:#ffebee
    style E fill:#fff3e0
    style F fill:#e8f5e8
```

### 监控指标仪表板
```mermaid
graph LR
    subgraph "系统指标"
        A[CPU使用率]
        B[内存使用率]
        C[磁盘使用率]
    end
    
    subgraph "业务指标"
        D[API成功率]
        E[价格获取频率]
        F[告警触发次数]
    end
    
    subgraph "告警阈值"
        G[CPU > 80%]
        H[内存 > 90%]
        I[API失败率 > 10%]
    end
    
    A --> G
    B --> H
    D --> I
    
    G --> J[发送告警]
    H --> J
    I --> J
    
    style J fill:#ffebee
```

## ⚙️ 配置示例

### 故障排查脚本 (troubleshoot.bat)
```batch
@echo off
echo === PUMP系统故障排查工具 ===

echo.
echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo ❌ Java环境异常
    goto :end
)

echo.
echo 2. 检查JAR文件...
if not exist pump30.jar (
    echo ❌ pump30.jar文件不存在
    goto :end
)

echo.
echo 3. 检查网络连接...
ping -n 1 api.gateio.ws >nul
if %errorlevel% neq 0 (
    echo ❌ 无法连接到Gate.io API
) else (
    echo ✅ Gate.io API连接正常
)

ping -n 1 quote-api.jup.ag >nul
if %errorlevel% neq 0 (
    echo ❌ 无法连接到Jupiter API
) else (
    echo ✅ Jupiter API连接正常
)

echo.
echo 4. 检查系统资源...
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list

echo.
echo 5. 运行健康检查...
java -cp pump30.jar com.pump.diagnostic.DiagnosticTool

:end
pause
```

### 日志分析脚本 (analyze-logs.ps1)
```powershell
# PUMP系统日志分析脚本
param(
    [string]$LogFile = "pump-monitor.log",
    [int]$Hours = 24
)

Write-Host "=== PUMP系统日志分析 ===" -ForegroundColor Yellow

if (-not (Test-Path $LogFile)) {
    Write-Host "❌ 日志文件不存在: $LogFile" -ForegroundColor Red
    exit 1
}

# 分析最近N小时的日志
$StartTime = (Get-Date).AddHours(-$Hours)
$LogContent = Get-Content $LogFile | Where-Object {
    $_ -match '\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
}

# 统计错误信息
$ErrorCount = ($LogContent | Select-String "ERROR").Count
$WarnCount = ($LogContent | Select-String "WARN").Count
$InfoCount = ($LogContent | Select-String "INFO").Count

Write-Host "📊 日志统计 (最近 $Hours 小时):" -ForegroundColor Cyan
Write-Host "  ERROR: $ErrorCount" -ForegroundColor Red
Write-Host "  WARN:  $WarnCount" -ForegroundColor Yellow
Write-Host "  INFO:  $InfoCount" -ForegroundColor Green

# 分析API调用失败
$ApiErrors = $LogContent | Select-String "API.*失败|API.*异常"
if ($ApiErrors.Count -gt 0) {
    Write-Host "🔍 API调用问题:" -ForegroundColor Red
    $ApiErrors | ForEach-Object { Write-Host "  $_" }
}

# 分析告警触发
$AlertTriggers = $LogContent | Select-String "告警|Alert"
if ($AlertTriggers.Count -gt 0) {
    Write-Host "🔔 告警触发记录:" -ForegroundColor Yellow
    $AlertTriggers | ForEach-Object { Write-Host "  $_" }
}
```

### 性能监控配置
```java
@Component
public class PerformanceMonitor {
    
    private final AtomicLong apiCallCount = new AtomicLong(0);
    private final AtomicLong successCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private final AtomicLong totalResponseTime = new AtomicLong(0);
    
    public void recordApiCall(long responseTime, boolean success) {
        apiCallCount.incrementAndGet();
        totalResponseTime.addAndGet(responseTime);
        
        if (success) {
            successCount.incrementAndGet();
        } else {
            errorCount.incrementAndGet();
        }
    }
    
    @Scheduled(fixedRate = 60000) // 每分钟输出一次统计
    public void printStatistics() {
        long total = apiCallCount.get();
        if (total > 0) {
            double successRate = (double) successCount.get() / total * 100;
            double avgResponseTime = (double) totalResponseTime.get() / total;
            
            logger.info("性能统计 - 成功率: {:.2f}%, 平均响应时间: {:.2f}ms, 总调用: {}",
                successRate, avgResponseTime, total);
        }
    }
}
```

## 📝 更新记录

| 日期 | 版本 | 更新内容 | 作者 |
|------|------|----------|------|
| 2025-01-16 | 1.0 | 初始版本，故障排查指南 | 运维工程师 |

---

**相关文档**: 
- [常见问题解答](常见问题解答_FAQ.md)
- [错误代码参考](错误代码参考_Error-Codes.md)
- [性能问题诊断](性能问题诊断_Performance-Issues.md)
