package com.pump;

import java.awt.Toolkit;

/**
 * 简单的系统蜂鸣测试
 * 最基础的音频测试，如果这个都听不到，说明系统音频有问题
 * 
 * 使用方法：
 * 1. 在IDE中右键点击这个类
 * 2. 选择 "Run SimpleBeepTest.main()"
 * 3. 注意听系统蜂鸣声
 */
public class SimpleBeepTest {

    public static void main(String[] args) {
        System.out.println("🔔 ========== 简单蜂鸣测试 ==========");
        System.out.println("📢 即将播放系统蜂鸣声，请注意听！");
        System.out.println();

        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            
            // 播放10次蜂鸣，间隔1秒
            for (int i = 1; i <= 10; i++) {
                System.out.println("🔊 第 " + i + " 次蜂鸣 - 请注意听！");
                toolkit.beep();
                
                // 等待1秒
                Thread.sleep(1000);
            }
            
            System.out.println();
            System.out.println("✅ 蜂鸣测试完成");
            System.out.println("📋 如果您听到了蜂鸣声，说明系统音频正常");
            System.out.println("📋 如果没有听到，请检查系统音量设置");
            
        } catch (Exception e) {
            System.out.println("❌ 蜂鸣测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("🔔 ========== 测试结束 ==========");
    }
}
