# PBI-001-2: 价格数据获取服务开发

## 描述

开发TON价格数据获取服务(`TonPoolServiceImpl`)，负责从外部API获取实时TON价格数据，并提供给价格分析引擎使用。该服务需要具备高可靠性、低延迟和错误恢复能力。

**Parent Task List**: [PBI-001任务列表](./tasks.md)

## 状态历史

| 时间戳 | 事件 | 从状态 | 到状态 | 详情 | 用户 |
|--------|------|--------|--------|------|------|
| 2025-01-15 01:45:00 | 任务创建 | - | Proposed | 创建价格数据获取服务开发任务 | AI Agent |

## 需求

### 功能需求
1. **实时价格获取**
   - 从外部API获取TON/USDT实时价格
   - 支持多个价格源（主要和备用）
   - 价格数据格式标准化处理

2. **数据验证**
   - 价格数据有效性验证
   - 异常数据过滤和处理
   - 价格变化幅度合理性检查

3. **缓存机制**
   - 实现本地缓存减少API调用频率
   - 缓存失效和更新策略
   - 支持缓存预热

4. **错误处理**
   - API调用失败时的重试机制
   - 降级处理（使用备用数据源）
   - 错误日志记录和监控

### 非功能需求
- **性能要求**：单次API调用响应时间<1秒
- **可靠性要求**：API调用成功率>95%
- **并发要求**：支持多线程并发调用
- **扩展性要求**：易于添加新的价格数据源

## 实现方案

### 类设计
```java
@Service
public class TonPoolServiceImpl {
    
    private final RestTemplate restTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ConfigurationProperties config;
    
    /**
     * 获取TON实时价格
     * @return PriceData 价格数据对象
     * @throws PriceServiceException 价格服务异常
     */
    public PriceData getCurrentPrice() throws PriceServiceException;
    
    /**
     * 获取买入价格
     * @param amount TON数量
     * @return BigDecimal 买入所需USDT数量
     */
    public BigDecimal getBuyPrice(BigDecimal amount);
    
    /**
     * 获取卖出价格
     * @param amount TON数量
     * @return BigDecimal 卖出获得USDT数量
     */
    public BigDecimal getSellPrice(BigDecimal amount);
    
    /**
     * 健康检查
     * @return boolean 服务是否正常
     */
    public boolean healthCheck();
}
```

### 数据模型
```java
public class PriceData {
    private BigDecimal buyPrice;      // 买入价格
    private BigDecimal sellPrice;     // 卖出价格
    private BigDecimal bidPrice;      // 买一价
    private BigDecimal askPrice;      // 卖一价
    private Long timestamp;           // 时间戳
    private String source;            // 数据源
    private BigDecimal volume24h;     // 24小时成交量
}
```

### 配置管理
```properties
# 价格API配置
price.api.primary.url=https://api.primaryexchange.com/v1/ton/price
price.api.primary.key=your-api-key
price.api.fallback.url=https://api.fallbackexchange.com/v1/ton/price
price.api.fallback.key=your-fallback-key

# 缓存配置
price.cache.ttl=2000
price.cache.max-size=1000

# 重试配置
price.retry.max-attempts=3
price.retry.delay=1000
```

## 测试方案

### 单元测试
1. **价格获取测试**
   - 正常价格获取流程测试
   - API响应数据解析测试
   - 价格数据验证测试

2. **错误处理测试**
   - API调用失败重试测试
   - 网络超时处理测试
   - 数据格式异常处理测试

3. **缓存功能测试**
   - 缓存存储和读取测试
   - 缓存过期机制测试
   - 缓存更新策略测试

### 集成测试
1. **外部API集成测试**
   - 真实API调用测试
   - 多数据源切换测试
   - 数据一致性验证

2. **性能测试**
   - 并发调用性能测试
   - 响应时间测试
   - 内存使用情况测试

### 测试覆盖率要求
- 单元测试覆盖率：>90%
- 集成测试覆盖率：>80%
- 关键路径测试覆盖率：100%

## 验证标准

### 功能验证
- [ ] 能够成功获取TON实时价格数据
- [ ] 价格数据格式正确且完整
- [ ] 缓存机制正常工作
- [ ] 错误处理和重试机制有效
- [ ] 支持多个价格数据源

### 性能验证
- [ ] API调用响应时间<1秒
- [ ] 支持至少10个并发请求
- [ ] 内存使用量<100MB
- [ ] CPU使用率<20%（正常负载）

### 可靠性验证
- [ ] API调用成功率>95%
- [ ] 系统运行24小时无内存泄漏
- [ ] 网络异常恢复时间<30秒
- [ ] 错误日志记录完整准确

## 修改的文件

### 新增文件
- `src/main/java/com/bitcoin/alarm/service/TonPoolService.java` - 服务接口
- `src/main/java/com/bitcoin/alarm/service/impl/TonPoolServiceImpl.java` - 服务实现
- `src/main/java/com/bitcoin/alarm/model/PriceData.java` - 价格数据模型
- `src/main/java/com/bitcoin/alarm/exception/PriceServiceException.java` - 服务异常类
- `src/main/java/com/bitcoin/alarm/config/PriceServiceConfig.java` - 配置类

### 修改文件
- `src/main/resources/application.yml` - 添加价格服务配置
- `pom.xml` - 添加相关依赖

### 测试文件
- `src/test/java/com/bitcoin/alarm/service/TonPoolServiceTest.java` - 单元测试
- `src/test/java/com/bitcoin/alarm/integration/PriceServiceIntegrationTest.java` - 集成测试

## 依赖关系

### 前置依赖
- [PBI-001-1: 系统架构设计](./PBI-001-1.md) - 需要确定技术栈和架构设计

### 后置依赖
- [PBI-001-3: 价格分析引擎开发](./PBI-001-3.md) - 需要使用价格数据
- [PBI-001-4: 定时任务调度器开发](./PBI-001-4.md) - 需要调用价格获取服务
- [PBI-001-6: 错误处理机制开发](./PBI-001-6.md) - 需要集成错误处理

## 风险和缓解措施

### 技术风险
1. **外部API不稳定**
   - 风险：第三方API服务中断或变更
   - 缓解：使用多个备用API源，实现降级策略

2. **网络延迟**
   - 风险：网络延迟导致价格数据不及时
   - 缓解：优化网络配置，使用CDN加速

3. **数据格式变化**
   - 风险：外部API数据格式变更
   - 缓解：实现灵活的数据解析器，版本兼容处理

### 业务风险
1. **价格数据准确性**
   - 风险：获取到错误的价格数据
   - 缓解：多数据源交叉验证，异常数据过滤

2. **服务可用性**
   - 风险：服务不可用影响整体系统
   - 缓解：实现熔断机制，提供降级服务

---

**创建日期**: 2025-01-15  
**最后更新**: 2025-01-15  
**负责人**: AI Agent  
**预计工期**: 3-5天  
**优先级**: 高

---

**相关链接**:
- [PBI-001任务列表](./tasks.md)
- [PBI-001详细文档](./prd.md)
- [系统架构设计](../../technical/architecture.md) 