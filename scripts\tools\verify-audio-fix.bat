@echo off
chcp 65001 > nul
echo 🔍 ====== 音频修复验证脚本 ======
echo 📅 验证时间: %date% %time%
echo.

cd /d "%~dp0"

echo 📋 正在验证音频修复状态...
echo.

REM 1. 检查配置文件
echo 🧪 1. 检查配置文件设置...
if exist "src\main\resources\pump-config.json" (
    findstr /C:"CUSTOM_SIMPLIFIED" "src\main\resources\pump-config.json" > nul
    if !errorlevel! == 0 (
        echo ✅ 配置文件已正确设置为 CUSTOM_SIMPLIFIED
    ) else (
        echo ❌ 配置文件设置错误
        echo 🔧 正在修复配置...
        tools\update-audio-config.bat
    )
) else (
    echo ❌ 配置文件不存在
)

echo.

REM 2. 检查音频文件
echo 🧪 2. 检查音频文件...
if exist "src\main\resources\audio\up.wav" (
    echo ✅ up.wav 文件存在
) else (
    echo ❌ up.wav 文件缺失
)

if exist "src\main\resources\audio\down.wav" (
    echo ✅ down.wav 文件存在
) else (
    echo ❌ down.wav 文件缺失
)

echo.

REM 3. 检查JAR文件
echo 🧪 3. 检查JAR文件...
if exist "target\pump.jar" (
    echo ✅ JAR文件存在
    
    REM 检查JAR中的音频文件
    jar tf "target\pump.jar" | findstr "audio" > nul
    if !errorlevel! == 0 (
        echo ✅ JAR包含音频文件
    ) else (
        echo ❌ JAR中缺少音频文件
    )
) else (
    echo ❌ JAR文件不存在，需要重新构建
)

echo.

REM 4. 检查Java环境
echo 🧪 4. 检查Java环境...
java -version 2>&1 | head -1

echo.

REM 5. 运行快速音频测试
echo 🧪 5. 运行快速音频测试...
choice /M "是否要运行音频测试以确认修复效果"
if !errorlevel! == 1 (
    echo.
    echo 🎵 启动音频测试...
    tools\test-simplified-audio.bat
) else (
    echo ⏭️ 跳过音频测试
)

echo.
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 📊 验证摘要:
echo.
echo ✅ 已应用的修复措施:
echo    📁 配置文件设置为 CUSTOM_SIMPLIFIED
echo    🎵 集成了基于成功实例的简化音频播放方法
echo    🔧 创建了测试和配置工具
echo    📦 重新打包了系统JAR文件
echo.
echo 🚀 推荐启动方式:
echo    pump-fixed.bat     (使用修复后的音频功能)
echo    pump30.bat         (原有启动方式，但使用新音频方法)
echo.
echo 🛠️  如果遇到问题:
echo    tools\update-audio-config.bat  (切换音频方法)
echo    tools\test-simplified-audio.bat (测试音频功能)
echo.

pause 