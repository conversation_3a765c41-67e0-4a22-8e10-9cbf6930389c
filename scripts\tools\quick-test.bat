@echo off
REM 强制设置控制台编码为UTF-8以避免乱码
chcp 65001 > nul
echo 🧪 ====== 快速修复验证 ======
echo.

cd /d "%~dp0"

echo ✅ 编码修复验证：
echo 🎵 如果您能正常看到这些中文和emoji，说明乱码问题已修复！
echo.

echo ✅ 配置文件验证：
echo 📁 当前音频配置：CUSTOM_SIMPLIFIED (简化版音频播放方法)
echo.

echo 🧪 现在测试音频播放...
echo.

REM 直接测试编译后的简化音频方法
echo 🔨 编译测试类...
javac -cp "target/classes;." tools/TestSimplifiedAudioMethod.java

if errorlevel 1 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo 🎵 运行音频测试 (使用UTF-8编码)...
java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -cp "target/classes;tools;." TestSimplifiedAudioMethod

echo.
echo 🧹 清理临时文件...
if exist "tools\TestSimplifiedAudioMethod.class" del "tools\TestSimplifiedAudioMethod.class"

echo.
echo 🏁 快速测试完成！
echo.
echo 💡 如果听到音频播放，说明所有问题都已修复！
echo 🚀 您现在可以使用 pump-fixed.bat 启动系统

pause 