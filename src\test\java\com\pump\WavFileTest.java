package com.pump;

import javax.sound.sampled.*;
import java.io.InputStream;
import java.io.BufferedInputStream;

/**
 * WAV文件播放测试
 * 专门测试up.wav和down.wav文件的播放
 * 
 * 使用方法：
 * 1. 在IDE中右键点击这个类
 * 2. 选择 "Run WavFileTest.main()"
 * 3. 注意听音频文件播放
 */
public class WavFileTest {

    public static void main(String[] args) {
        System.out.println("🎵 ========== WAV文件播放测试 ==========");
        System.out.println("📢 即将播放音频文件，请注意听！");
        System.out.println();

        // 测试播放up.wav
        System.out.println("🔊 测试1: 播放 up.wav (买入音频)");
        playWavFile("up.wav");
        
        // 等待3秒
        sleep(3000);
        
        // 测试播放down.wav
        System.out.println("🔊 测试2: 播放 down.wav (卖出音频)");
        playWavFile("down.wav");
        
        System.out.println();
        System.out.println("🎵 ========== 测试完成 ==========");
        System.out.println("📋 如果您听到了音频，说明WAV播放功能正常");
        System.out.println("📋 如果没有听到，可能是音频文件或系统设置问题");
    }

    /**
     * 播放WAV文件
     */
    private static void playWavFile(String fileName) {
        System.out.println("\n🎧 开始播放: " + fileName);
        
        InputStream rawStream = null;
        InputStream audioStream = null;
        AudioInputStream audioInputStream = null;
        Clip clip = null;
        
        try {
            // 加载音频资源
            String resourcePath = "/audio/" + fileName;
            System.out.println("📂 加载路径: " + resourcePath);
            
            rawStream = WavFileTest.class.getResourceAsStream(resourcePath);
            if (rawStream == null) {
                System.out.println("❌ 找不到音频文件: " + resourcePath);
                System.out.println("💡 请确保音频文件位于 src/main/resources/audio/ 目录下");
                return;
            }
            System.out.println("✅ 音频文件加载成功");

            // 处理音频流
            audioStream = rawStream.markSupported() ? 
                rawStream : new BufferedInputStream(rawStream, 8192);

            // 创建AudioInputStream
            audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            AudioFormat format = audioInputStream.getFormat();
            System.out.println("📊 音频格式: " + format.getSampleRate() + "Hz, " + 
                             format.getSampleSizeInBits() + "bit, " + 
                             format.getChannels() + "声道");

            // 创建并打开音频剪辑
            clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            
            long durationMillis = clip.getMicrosecondLength() / 1000;
            System.out.println("⏱️ 音频时长: " + durationMillis + "ms");

            // 设置音量到最大
            if (clip.isControlSupported(FloatControl.Type.MASTER_GAIN)) {
                FloatControl gainControl = (FloatControl) clip.getControl(FloatControl.Type.MASTER_GAIN);
                float maxGain = gainControl.getMaximum();
                gainControl.setValue(maxGain);
                System.out.println("🔊 音量设置到最大: " + maxGain + " dB");
            }

            // 播放音频
            System.out.println("🎵 开始播放 " + fileName + " - 请注意听！");
            clip.start();

            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(50);
            }

            System.out.println("✅ " + fileName + " 播放完成");

        } catch (UnsupportedAudioFileException e) {
            System.out.println("❌ 不支持的音频格式: " + e.getMessage());
        } catch (LineUnavailableException e) {
            System.out.println("❌ 音频设备不可用: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("❌ 播放失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 清理资源
            try {
                if (clip != null) clip.close();
                if (audioInputStream != null) audioInputStream.close();
                if (audioStream != null) audioStream.close();
                if (rawStream != null && rawStream != audioStream) rawStream.close();
            } catch (Exception e) {
                System.out.println("⚠️ 资源清理异常: " + e.getMessage());
            }
        }
    }

    /**
     * 线程睡眠
     */
    private static void sleep(int milliseconds) {
        try {
            System.out.println("⏳ 等待 " + (milliseconds/1000) + " 秒...");
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
