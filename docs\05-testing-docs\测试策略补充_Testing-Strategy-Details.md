# PUMP代币价格监控系统 - 测试验证策略

## 📋 目录
- [测试概述](#测试概述)
- [单元测试计划](#单元测试计划)
- [集成测试设计](#集成测试设计)
- [性能测试标准](#性能测试标准)
- [错误场景测试](#错误场景测试)
- [验证工具集](#验证工具集)
- [测试执行指南](#测试执行指南)

---

## 🎯 测试概述

### 测试目标
确保PUMP代币价格监控系统在各种场景下的稳定性、准确性和性能表现，重点验证：
- **价格获取准确性**: CEX和DEX价格数据的正确性
- **差价计算精度**: 买入/卖出差价算法的准确性
- **告警触发可靠性**: 基于阈值的音频告警机制
- **系统稳定性**: 长时间运行的稳定性和资源消耗
- **异常处理能力**: 网络异常、API限流等场景的处理

### 测试环境要求
- **Java版本**: JDK 11+
- **网络环境**: 能够访问Gate.io和Jupiter API
- **音频设备**: 支持WAV格式音频播放
- **测试数据**: 100万PUMP代币价格监控场景

### 测试工具栈
- **单元测试**: JUnit 5 + Mockito
- **集成测试**: Spring Boot Test
- **性能测试**: JMeter + 自定义监控脚本
- **API测试**: 专用测试工具类

---

## 🧪 单元测试计划

### 1. 价格获取模块测试

#### Gate.io API客户端测试
```java
@ExtendWith(MockitoExtension.class)
class GateIoApiClientTest {
    
    @Mock
    private RestTemplate restTemplate;
    
    @InjectMocks
    private GateIoApiClient gateIoApiClient;
    
    @Test
    void testGetPumpPrice_Success() {
        // 模拟API响应
        String mockResponse = "[{\"last\":\"0.005711\",\"volume\":\"1000000\"}]";
        when(restTemplate.getForObject(anyString(), eq(String.class)))
            .thenReturn(mockResponse);
        
        PriceData result = gateIoApiClient.getPumpPrice();
        
        assertThat(result).isNotNull();
        assertThat(result.isValid()).isTrue();
        assertThat(result.getLastPrice()).isEqualByComparingTo("0.005711");
        assertThat(result.getExchange()).isEqualTo("Gate.io");
    }
    
    @Test
    void testGetPumpPrice_ApiError() {
        when(restTemplate.getForObject(anyString(), eq(String.class)))
            .thenThrow(new RestClientException("API调用失败"));
        
        PriceData result = gateIoApiClient.getPumpPrice();
        
        assertThat(result).isNotNull();
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrorMessage()).contains("API调用失败");
    }
}
```

#### Jupiter API客户端测试
```java
@ExtendWith(MockitoExtension.class)
class JupiterApiClientFixedTest {
    
    @Mock
    private PriceCache priceCache;
    
    @InjectMocks
    private JupiterApiClientFixed jupiterApiClient;
    
    @Test
    void testGetQuotePrice_BuyOperation() {
        BigDecimal amount = new BigDecimal("1000000");
        boolean isBuy = true;
        
        // 模拟基础价格
        PriceData mockBasePrice = new PriceData();
        mockBasePrice.setLastPrice(new BigDecimal("0.005711"));
        mockBasePrice.setValid(true);
        
        BigDecimal result = jupiterApiClient.getQuotePrice(amount, isBuy);
        
        assertThat(result).isNotNull();
        assertThat(result).isGreaterThan(BigDecimal.ZERO);
        // 验证买入价格在合理范围内 (0.005-0.007 USDT/PUMP)
        assertThat(result).isBetween(new BigDecimal("0.005"), new BigDecimal("0.007"));
    }
    
    @Test
    void testGetQuotePrice_SellOperation() {
        BigDecimal amount = new BigDecimal("1000000");
        boolean isBuy = false;
        
        BigDecimal result = jupiterApiClient.getQuotePrice(amount, isBuy);
        
        assertThat(result).isNotNull();
        assertThat(result).isGreaterThan(BigDecimal.ZERO);
        // 验证卖出价格略低于买入价格
    }
}
```

### 2. 差价计算模块测试

#### 价格差异计算测试
```java
class PriceDifferenceCalculationTest {
    
    @Test
    void testBuyDifferenceCalculation() {
        BigDecimal gatePrice = new BigDecimal("5711.00");  // Gate.io总价
        BigDecimal jupiterBuyPrice = new BigDecimal("5681.00");  // Jupiter买入总价
        
        BigDecimal buyDifference = gatePrice.subtract(jupiterBuyPrice);
        
        assertThat(buyDifference).isEqualByComparingTo("30.00");
        assertThat(buyDifference).isGreaterThan(BigDecimal.ZERO); // 正差价表示套利机会
    }
    
    @Test
    void testSellDifferenceCalculation() {
        BigDecimal jupiterSellPrice = new BigDecimal("5741.00");  // Jupiter卖出总价
        BigDecimal gatePrice = new BigDecimal("5711.00");  // Gate.io总价
        
        BigDecimal sellDifference = jupiterSellPrice.subtract(gatePrice);
        
        assertThat(sellDifference).isEqualByComparingTo("30.00");
        assertThat(sellDifference).isGreaterThan(BigDecimal.ZERO); // 正差价表示套利机会
    }
    
    @Test
    void testPrecisionHandling() {
        BigDecimal price1 = new BigDecimal("5711.123456");
        BigDecimal price2 = new BigDecimal("5681.654321");
        
        BigDecimal difference = price1.subtract(price2);
        String formatted = String.format("%.2f", difference);
        
        assertThat(formatted).isEqualTo("29.47");
    }
}
```

### 3. 告警系统测试

#### 音频告警服务测试
```java
@ExtendWith(MockitoExtension.class)
class AlertSoundServiceTest {
    
    @Mock
    private PumpConfigService configService;
    
    @InjectMocks
    private AlertSoundService alertSoundService;
    
    @Test
    void testCheckBuyAlert_TriggerCondition() {
        when(configService.getBuyThreshold()).thenReturn(30.0);
        when(configService.getSoundType()).thenReturn("SYSTEM");
        
        BigDecimal buyDifference = new BigDecimal("35.00"); // 超过阈值
        BigDecimal jupiterPrice = new BigDecimal("5676.00");
        BigDecimal gatePrice = new BigDecimal("5711.00");
        
        // 验证告警触发
        alertSoundService.checkBuyAlert(buyDifference, jupiterPrice, gatePrice);
        
        // 验证播放状态
        // 注意：实际测试中需要模拟音频播放或使用测试替身
    }
    
    @Test
    void testCheckBuyAlert_BelowThreshold() {
        when(configService.getBuyThreshold()).thenReturn(30.0);
        
        BigDecimal buyDifference = new BigDecimal("25.00"); // 低于阈值
        BigDecimal jupiterPrice = new BigDecimal("5686.00");
        BigDecimal gatePrice = new BigDecimal("5711.00");
        
        // 验证告警不触发
        alertSoundService.checkBuyAlert(buyDifference, jupiterPrice, gatePrice);
        
        // 验证没有音频播放
    }
}
```

### 4. 配置管理测试

#### 配置服务测试
```java
class PumpConfigServiceTest {
    
    private PumpConfigService configService;
    
    @BeforeEach
    void setUp() {
        configService = new PumpConfigService();
        configService.loadConfig();
    }
    
    @Test
    void testLoadConfig_DefaultValues() {
        assertThat(configService.getMonitorInterval()).isEqualTo(2000L);
        assertThat(configService.getMonitorAmount()).isEqualTo(1000000L);
        assertThat(configService.getBuyThreshold()).isEqualTo(30.0);
        assertThat(configService.getSellThreshold()).isEqualTo(30.0);
    }
    
    @Test
    void testConfigValidation() {
        // 验证配置参数的有效性
        assertThat(configService.getMonitorInterval()).isGreaterThan(0);
        assertThat(configService.getMonitorAmount()).isGreaterThan(0);
        assertThat(configService.getBuyThreshold()).isGreaterThanOrEqualTo(0);
    }
}
```

---

## 🔗 集成测试设计

### 1. 端到端价格监控测试

#### 完整监控流程测试
```java
@SpringBootTest
@TestPropertySource(properties = {
    "pump.monitor.interval=5000",
    "pump.alert.enabled=false" // 测试时禁用音频
})
class PriceMonitoringIntegrationTest {
    
    @Autowired
    private PriceMonitorScheduler scheduler;
    
    @Autowired
    private PumpPriceService priceService;
    
    @Test
    void testCompleteMonitoringCycle() {
        // 执行一次完整的监控周期
        scheduler.monitorPrices();
        
        // 验证价格数据获取
        PriceData cexPrice = priceService.getCexPrice();
        assertThat(cexPrice).isNotNull();
        
        BigDecimal dexBuyPrice = priceService.getDexBuyPrice(new BigDecimal("1000000"));
        BigDecimal dexSellPrice = priceService.getDexSellPrice(new BigDecimal("1000000"));
        
        // 验证价格数据有效性
        if (cexPrice.isValid()) {
            assertThat(cexPrice.getLastPrice()).isGreaterThan(BigDecimal.ZERO);
        }
        
        if (dexBuyPrice != null) {
            assertThat(dexBuyPrice).isGreaterThan(BigDecimal.ZERO);
        }
        
        if (dexSellPrice != null) {
            assertThat(dexSellPrice).isGreaterThan(BigDecimal.ZERO);
        }
    }
}
```

### 2. API集成测试

#### 多API协调测试
```java
@SpringBootTest
class ApiIntegrationTest {
    
    @Autowired
    private GateIoApiClient gateIoClient;
    
    @Autowired
    private JupiterApiClientFixed jupiterClient;
    
    @Test
    void testApiCoordination() {
        // 同时调用多个API
        CompletableFuture<PriceData> gateFuture = CompletableFuture
            .supplyAsync(() -> gateIoClient.getPumpPrice());
        
        CompletableFuture<BigDecimal> jupiterBuyFuture = CompletableFuture
            .supplyAsync(() -> jupiterClient.getQuotePrice(new BigDecimal("1000000"), true));
        
        CompletableFuture<BigDecimal> jupiterSellFuture = CompletableFuture
            .supplyAsync(() -> jupiterClient.getQuotePrice(new BigDecimal("1000000"), false));
        
        // 等待所有API调用完成
        CompletableFuture.allOf(gateFuture, jupiterBuyFuture, jupiterSellFuture)
            .join();
        
        // 验证结果
        PriceData gatePrice = gateFuture.join();
        BigDecimal jupiterBuy = jupiterBuyFuture.join();
        BigDecimal jupiterSell = jupiterSellFuture.join();
        
        // 验证价格合理性
        if (gatePrice != null && gatePrice.isValid() && jupiterBuy != null && jupiterSell != null) {
            // 验证买入价格通常高于卖出价格
            assertThat(jupiterBuy).isGreaterThanOrEqualTo(jupiterSell);
        }
    }
}
```

---

## ⚡ 性能测试标准

### 1. 响应时间基准

#### API响应时间测试
```java
@Test
void testApiResponseTime() {
    int iterations = 100;
    List<Long> responseTimes = new ArrayList<>();
    
    for (int i = 0; i < iterations; i++) {
        long startTime = System.currentTimeMillis();
        
        PriceData result = gateIoClient.getPumpPrice();
        
        long endTime = System.currentTimeMillis();
        responseTimes.add(endTime - startTime);
    }
    
    // 计算统计数据
    double avgResponseTime = responseTimes.stream()
        .mapToLong(Long::longValue)
        .average()
        .orElse(0.0);
    
    long maxResponseTime = responseTimes.stream()
        .mapToLong(Long::longValue)
        .max()
        .orElse(0L);
    
    // 性能断言
    assertThat(avgResponseTime).isLessThan(2000.0); // 平均响应时间 < 2秒
    assertThat(maxResponseTime).isLessThan(10000L); // 最大响应时间 < 10秒
    
    System.out.printf("平均响应时间: %.2f ms, 最大响应时间: %d ms%n", 
        avgResponseTime, maxResponseTime);
}
```

### 2. 内存使用监控

#### 内存泄漏测试
```java
@Test
void testMemoryUsage() {
    Runtime runtime = Runtime.getRuntime();
    
    // 记录初始内存使用
    long initialMemory = runtime.totalMemory() - runtime.freeMemory();
    
    // 执行1000次监控周期
    for (int i = 0; i < 1000; i++) {
        scheduler.monitorPrices();
        
        // 每100次检查一次内存
        if (i % 100 == 0) {
            System.gc(); // 建议垃圾回收
            long currentMemory = runtime.totalMemory() - runtime.freeMemory();
            long memoryIncrease = currentMemory - initialMemory;
            
            System.out.printf("第%d次执行后内存使用: %d MB, 增长: %d MB%n",
                i, currentMemory / 1024 / 1024, memoryIncrease / 1024 / 1024);
            
            // 内存增长不应超过100MB
            assertThat(memoryIncrease).isLessThan(100 * 1024 * 1024);
        }
    }
}
```

### 3. 并发性能测试

#### 多线程访问测试
```java
@Test
void testConcurrentAccess() throws InterruptedException {
    int threadCount = 10;
    int iterationsPerThread = 50;
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);
    CountDownLatch latch = new CountDownLatch(threadCount);
    AtomicInteger successCount = new AtomicInteger(0);
    AtomicInteger errorCount = new AtomicInteger(0);
    
    for (int i = 0; i < threadCount; i++) {
        executor.submit(() -> {
            try {
                for (int j = 0; j < iterationsPerThread; j++) {
                    try {
                        PriceData result = gateIoClient.getPumpPrice();
                        if (result != null && result.isValid()) {
                            successCount.incrementAndGet();
                        } else {
                            errorCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        errorCount.incrementAndGet();
                    }
                }
            } finally {
                latch.countDown();
            }
        });
    }
    
    latch.await(60, TimeUnit.SECONDS);
    executor.shutdown();
    
    int totalRequests = threadCount * iterationsPerThread;
    double successRate = (double) successCount.get() / totalRequests * 100;
    
    System.out.printf("并发测试结果: 成功率 %.2f%% (%d/%d)%n", 
        successRate, successCount.get(), totalRequests);
    
    // 成功率应该 > 90%
    assertThat(successRate).isGreaterThan(90.0);
}
```

---

## 🚨 错误场景测试

### 1. 网络异常测试

#### 网络超时模拟
```java
@Test
void testNetworkTimeout() {
    // 配置超短超时时间
    RestTemplate timeoutRestTemplate = new RestTemplate();
    timeoutRestTemplate.setRequestFactory(new HttpComponentsClientHttpRequestFactory() {{
        setConnectTimeout(100); // 100ms超时
        setReadTimeout(100);
    }});
    
    GateIoApiClient timeoutClient = new GateIoApiClient();
    // 注入超时配置的RestTemplate
    
    PriceData result = timeoutClient.getPumpPrice();
    
    // 验证超时处理
    assertThat(result).isNotNull();
    assertThat(result.isValid()).isFalse();
    assertThat(result.getErrorMessage()).contains("超时");
}
```

### 2. API限流测试

#### 限流处理验证
```java
@Test
void testApiRateLimit() {
    // 快速连续调用触发限流
    List<CompletableFuture<BigDecimal>> futures = new ArrayList<>();
    
    for (int i = 0; i < 100; i++) {
        CompletableFuture<BigDecimal> future = CompletableFuture
            .supplyAsync(() -> jupiterClient.getQuotePrice(new BigDecimal("1000000"), true));
        futures.add(future);
    }
    
    // 等待所有请求完成
    List<BigDecimal> results = futures.stream()
        .map(CompletableFuture::join)
        .collect(Collectors.toList());
    
    // 统计成功和失败的请求
    long successCount = results.stream().filter(Objects::nonNull).count();
    long failureCount = results.size() - successCount;
    
    System.out.printf("限流测试结果: 成功 %d, 失败 %d%n", successCount, failureCount);
    
    // 验证系统能够处理限流情况
    assertThat(successCount).isGreaterThan(0); // 至少有部分请求成功
}
```

### 3. 数据异常测试

#### 异常数据处理
```java
@Test
void testInvalidDataHandling() {
    // 模拟异常的API响应
    String[] invalidResponses = {
        "", // 空响应
        "[]", // 空数组
        "{\"error\":\"Invalid request\"}", // 错误响应
        "invalid json", // 无效JSON
        "[{\"last\":\"invalid_number\"}]" // 无效数字
    };
    
    for (String invalidResponse : invalidResponses) {
        // 模拟RestTemplate返回异常数据
        when(restTemplate.getForObject(anyString(), eq(String.class)))
            .thenReturn(invalidResponse);
        
        PriceData result = gateIoClient.getPumpPrice();
        
        // 验证异常处理
        assertThat(result).isNotNull();
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrorMessage()).isNotEmpty();
    }
}
```

---

## 🛠️ 验证工具集

### 现有测试工具
项目已提供多个专用测试工具，位于`tools/`目录：

#### 1. 基础功能测试工具
- **TestAlertSound.java**: 音频告警系统测试
- **TestBuySellPriceDifference.java**: 买卖价差计算测试
- **TestCurrentLogic.java**: 当前逻辑验证
- **Test100WPumpPrice.java**: 100万PUMP价格测试

#### 2. API集成测试工具
- **JupiterApiTest.java**: Jupiter API功能测试
- **TestJupiterQuoteAPIFix.java**: Jupiter Quote API修复验证
- **TestUltraAPI.java**: Ultra API功能测试
- **TestUltraAPIWithProxy.java**: 代理环境下的API测试

#### 3. 系统集成测试工具
- **TestCacheIntegration.java**: 缓存集成测试
- **TestSchedulerFix.java**: 调度器修复验证
- **TestSimplifiedOutput.java**: 简化输出格式测试

### 测试工具使用示例

#### 运行音频告警测试
```bash
cd tools
javac -cp "../target/classes:../target/lib/*" TestAlertSound.java
java -cp ".:../target/classes:../target/lib/*" TestAlertSound
```

#### 运行价格差异测试
```bash
cd tools
javac -cp "../target/classes:../target/lib/*" TestBuySellPriceDifference.java
java -cp ".:../target/classes:../target/lib/*" TestBuySellPriceDifference
```

---

## 📋 测试执行指南

### 1. 测试环境准备

#### 环境检查清单
- [ ] Java 11+ 已安装
- [ ] Maven 3.8+ 已安装
- [ ] 网络连接正常 (能访问Gate.io和Jupiter API)
- [ ] 音频设备可用
- [ ] 项目依赖已下载 (`mvn dependency:resolve`)

#### 配置测试环境
```bash
# 1. 编译项目
mvn clean compile

# 2. 运行单元测试
mvn test

# 3. 运行集成测试
mvn integration-test

# 4. 生成测试报告
mvn surefire-report:report
```

### 2. 测试执行顺序

#### 推荐测试流程
1. **单元测试** → 验证各组件独立功能
2. **集成测试** → 验证组件间协作
3. **性能测试** → 验证系统性能指标
4. **错误场景测试** → 验证异常处理能力
5. **端到端测试** → 验证完整业务流程

### 3. 测试结果验证

#### 成功标准
- **单元测试覆盖率** > 80%
- **集成测试通过率** > 95%
- **API响应时间** < 2秒 (平均)
- **内存使用增长** < 100MB (1000次执行)
- **并发成功率** > 90%

#### 失败处理
1. **查看详细日志**: 检查测试输出和错误信息
2. **网络环境检查**: 确认API访问正常
3. **配置验证**: 检查配置文件和环境变量
4. **依赖检查**: 确认所有依赖正确安装

### 4. 持续集成建议

#### CI/CD流水线配置
```yaml
# GitHub Actions示例
name: PUMP Monitor Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up JDK 11
      uses: actions/setup-java@v2
      with:
        java-version: '11'
    - name: Run tests
      run: |
        mvn clean test
        mvn integration-test
    - name: Generate test report
      run: mvn surefire-report:report
```

---

## 🛠️ 实用测试工具详解

### 现有测试工具使用指南

#### 1. 音频告警测试工具
<augment_code_snippet path="tools/TestAlertSound.java" mode="EXCERPT">
````java
/**
 * 音频告警系统测试工具
 * 测试不同类型的音频告警功能
 */
public class TestAlertSound {
    public static void main(String[] args) {
        System.out.println("=== 音频告警系统测试 ===");

        // 测试系统蜂鸣器
        testSystemBeep();

        // 测试自定义音频文件
        testCustomAudio();

        // 测试多重蜂鸣
        testMultipleBeeps();
    }

    private static void testCustomAudio() {
        try {
            System.out.println("测试自定义音频播放...");

            // 测试买入告警音频
            playAudioFile("up.wav");
            Thread.sleep(2000);

            // 测试卖出告警音频
            playAudioFile("down.wav");

        } catch (Exception e) {
            System.err.println("自定义音频测试失败: " + e.getMessage());
        }
    }
}
````
</augment_code_snippet>

**使用方法**:
```bash
cd tools
javac -cp "../target/classes:../target/lib/*" TestAlertSound.java
java -cp ".:../target/classes:../target/lib/*" TestAlertSound
```

#### 2. 价格差异计算测试工具
<augment_code_snippet path="tools/TestBuySellPriceDifference.java" mode="EXCERPT">
````java
/**
 * 买卖价差计算测试工具
 * 验证价格差异计算算法的正确性
 */
public class TestBuySellPriceDifference {
    public static void main(String[] args) {
        System.out.println("=== 价格差异计算测试 ===");

        // 测试场景1: 正常价格差异
        testNormalPriceDifference();

        // 测试场景2: 极端价格差异
        testExtremePriceDifference();

        // 测试场景3: 精度处理
        testPrecisionHandling();

        // 测试场景4: 边界条件
        testBoundaryConditions();
    }

    private static void testNormalPriceDifference() {
        System.out.println("\n--- 正常价格差异测试 ---");

        BigDecimal gatePrice = new BigDecimal("5711.00");
        BigDecimal jupiterBuyPrice = new BigDecimal("5681.00");
        BigDecimal jupiterSellPrice = new BigDecimal("5741.00");

        // 买入差价 = Gate总价 - Jupiter买入总价
        BigDecimal buyDifference = gatePrice.subtract(jupiterBuyPrice);
        System.out.printf("买入差价: $%.2f (Gate: $%.2f - Jupiter买入: $%.2f)%n",
            buyDifference, gatePrice, jupiterBuyPrice);

        // 卖出差价 = Jupiter卖出总价 - Gate总价
        BigDecimal sellDifference = jupiterSellPrice.subtract(gatePrice);
        System.out.printf("卖出差价: $%.2f (Jupiter卖出: $%.2f - Gate: $%.2f)%n",
            sellDifference, jupiterSellPrice, gatePrice);

        // 验证结果
        assert buyDifference.compareTo(new BigDecimal("30.00")) == 0 : "买入差价计算错误";
        assert sellDifference.compareTo(new BigDecimal("30.00")) == 0 : "卖出差价计算错误";

        System.out.println("✅ 正常价格差异测试通过");
    }
}
````
</augment_code_snippet>

#### 3. Jupiter API集成测试工具
<augment_code_snippet path="tools/TestJupiterQuoteAPIFix.java" mode="EXCERPT">
````java
/**
 * Jupiter Quote API修复验证工具
 * 测试Jupiter API的买入/卖出价格获取功能
 */
public class TestJupiterQuoteAPIFix {
    private static final String PUMP_TOKEN = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn";
    private static final String USDT_TOKEN = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";

    public static void main(String[] args) {
        System.out.println("=== Jupiter Quote API修复验证 ===");

        // 测试买入价格获取
        testBuyPriceQuote();

        // 测试卖出价格获取
        testSellPriceQuote();

        // 测试价格合理性
        testPriceReasonableness();

        // 测试API限流处理
        testRateLimitHandling();
    }

    private static void testBuyPriceQuote() {
        System.out.println("\n--- 买入价格获取测试 ---");

        try {
            BigDecimal amount = new BigDecimal("1000000"); // 100万PUMP

            // 模拟买入操作：用USDT买PUMP
            String url = buildQuoteUrl(USDT_TOKEN, PUMP_TOKEN, "6000000000", "50"); // 6000 USDT

            System.out.println("请求URL: " + url);

            // 发送请求并解析响应
            String response = sendHttpRequest(url);
            BigDecimal unitPrice = parseQuoteResponse(response, amount, true);

            if (unitPrice != null) {
                BigDecimal totalPrice = unitPrice.multiply(amount);
                System.out.printf("买入价格: 单价 $%.6f, 总价 $%.2f%n", unitPrice, totalPrice);

                // 验证价格合理性
                if (totalPrice.compareTo(new BigDecimal("5000")) > 0 &&
                    totalPrice.compareTo(new BigDecimal("7000")) < 0) {
                    System.out.println("✅ 买入价格在合理范围内");
                } else {
                    System.out.println("❌ 买入价格超出预期范围");
                }
            } else {
                System.out.println("❌ 买入价格获取失败");
            }

        } catch (Exception e) {
            System.err.println("买入价格测试失败: " + e.getMessage());
        }
    }
}
````
</augment_code_snippet>

### 自动化测试脚本

#### 完整验证脚本
```bash
#!/bin/bash
# full-verification.bat 的Linux版本

echo "=== PUMP价格监控系统完整验证 ==="

# 1. 环境检查
echo "1. 检查Java环境..."
java -version
if [ $? -ne 0 ]; then
    echo "❌ Java环境未正确配置"
    exit 1
fi

# 2. 编译测试工具
echo "2. 编译测试工具..."
cd tools
find . -name "*.java" -exec javac -cp "../target/classes:../target/lib/*" {} \;

# 3. 运行基础功能测试
echo "3. 运行基础功能测试..."
java -cp ".:../target/classes:../target/lib/*" TestCurrentLogic
java -cp ".:../target/classes:../target/lib/*" Test100WPumpPrice
java -cp ".:../target/classes:../target/lib/*" TestBuySellPriceDifference

# 4. 运行API集成测试
echo "4. 运行API集成测试..."
java -cp ".:../target/classes:../target/lib/*" TestJupiterQuoteAPIFix
java -cp ".:../target/classes:../target/lib/*" TestUltraAPI

# 5. 运行音频系统测试
echo "5. 运行音频系统测试..."
java -cp ".:../target/classes:../target/lib/*" TestAlertSound

# 6. 运行缓存集成测试
echo "6. 运行缓存集成测试..."
java -cp ".:../target/classes:../target/lib/*" TestCacheIntegration

# 7. 生成测试报告
echo "7. 生成测试报告..."
echo "测试完成时间: $(date)" > test-report.txt
echo "所有测试已完成，请检查输出结果"

cd ..
```

#### 性能基准测试脚本
```bash
#!/bin/bash
# performance-benchmark.sh

echo "=== 性能基准测试 ==="

# 测试API响应时间
echo "测试API响应时间..."
for i in {1..10}; do
    echo "第 $i 次测试:"
    time java -cp "tools:target/classes:target/lib/*" TestCurrentLogic
    sleep 2
done

# 测试内存使用
echo "测试内存使用..."
java -Xmx512m -XX:+PrintGCDetails -cp "tools:target/classes:target/lib/*" TestCurrentLogic

# 测试并发性能
echo "测试并发性能..."
for i in {1..5}; do
    java -cp "tools:target/classes:target/lib/*" TestCurrentLogic &
done
wait

echo "性能测试完成"
```

---

## 📊 测试数据管理

### 测试数据生成器
```java
public class TestDataGenerator {

    /**
     * 生成模拟价格数据
     */
    public static PriceData generateMockPriceData(String exchange, BigDecimal basePrice) {
        PriceData priceData = new PriceData();
        priceData.setExchange(exchange);
        priceData.setSymbol("PUMP/USDT");

        // 添加随机波动 (±5%)
        Random random = new Random();
        double variation = (random.nextDouble() - 0.5) * 0.1; // ±5%
        BigDecimal price = basePrice.multiply(BigDecimal.valueOf(1 + variation));

        priceData.setLastPrice(price);
        priceData.setBuyPrice(price.multiply(new BigDecimal("1.001"))); // 买价稍高
        priceData.setSellPrice(price.multiply(new BigDecimal("0.999"))); // 卖价稍低
        priceData.setTimestamp(LocalDateTime.now());
        priceData.setValid(true);

        return priceData;
    }

    /**
     * 生成套利场景测试数据
     */
    public static List<ArbitrageScenario> generateArbitrageScenarios() {
        List<ArbitrageScenario> scenarios = new ArrayList<>();

        // 场景1: 明显的买入套利机会
        scenarios.add(new ArbitrageScenario(
            "买入套利机会",
            new BigDecimal("5711.00"), // Gate价格
            new BigDecimal("5651.00"), // Jupiter买入价格
            new BigDecimal("5691.00"), // Jupiter卖出价格
            true, // 应该触发买入告警
            false // 不应该触发卖出告警
        ));

        // 场景2: 明显的卖出套利机会
        scenarios.add(new ArbitrageScenario(
            "卖出套利机会",
            new BigDecimal("5711.00"), // Gate价格
            new BigDecimal("5721.00"), // Jupiter买入价格
            new BigDecimal("5751.00"), // Jupiter卖出价格
            false, // 不应该触发买入告警
            true // 应该触发卖出告警
        ));

        // 场景3: 无套利机会
        scenarios.add(new ArbitrageScenario(
            "无套利机会",
            new BigDecimal("5711.00"), // Gate价格
            new BigDecimal("5701.00"), // Jupiter买入价格
            new BigDecimal("5721.00"), // Jupiter卖出价格
            false, // 不应该触发买入告警
            false // 不应该触发卖出告警
        ));

        return scenarios;
    }

    public static class ArbitrageScenario {
        private final String description;
        private final BigDecimal gatePrice;
        private final BigDecimal jupiterBuyPrice;
        private final BigDecimal jupiterSellPrice;
        private final boolean shouldTriggerBuyAlert;
        private final boolean shouldTriggerSellAlert;

        // 构造函数和getter方法...
    }
}
```

### 测试结果验证器
```java
public class TestResultValidator {

    /**
     * 验证价格数据的有效性
     */
    public static ValidationResult validatePriceData(PriceData priceData) {
        ValidationResult result = new ValidationResult();

        if (priceData == null) {
            result.addError("价格数据为null");
            return result;
        }

        // 检查必填字段
        if (priceData.getLastPrice() == null) {
            result.addError("最新价格不能为null");
        } else if (priceData.getLastPrice().compareTo(BigDecimal.ZERO) <= 0) {
            result.addError("最新价格必须大于0");
        }

        // 检查价格合理性 (PUMP价格应在0.001-1 USDT范围内)
        if (priceData.getLastPrice() != null) {
            BigDecimal minPrice = new BigDecimal("0.001");
            BigDecimal maxPrice = new BigDecimal("1.000");

            if (priceData.getLastPrice().compareTo(minPrice) < 0) {
                result.addWarning("价格低于预期最小值: " + minPrice);
            }

            if (priceData.getLastPrice().compareTo(maxPrice) > 0) {
                result.addWarning("价格高于预期最大值: " + maxPrice);
            }
        }

        // 检查时间戳
        if (priceData.getTimestamp() == null) {
            result.addError("时间戳不能为null");
        } else {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime fiveMinutesAgo = now.minusMinutes(5);

            if (priceData.getTimestamp().isBefore(fiveMinutesAgo)) {
                result.addWarning("价格数据可能过期 (超过5分钟)");
            }
        }

        return result;
    }

    /**
     * 验证套利分析结果
     */
    public static ValidationResult validateArbitrageResult(
            BigDecimal gatePrice,
            BigDecimal jupiterBuyPrice,
            BigDecimal jupiterSellPrice,
            BigDecimal buyDifference,
            BigDecimal sellDifference) {

        ValidationResult result = new ValidationResult();

        // 验证差价计算
        if (gatePrice != null && jupiterBuyPrice != null) {
            BigDecimal expectedBuyDiff = gatePrice.subtract(jupiterBuyPrice);
            if (buyDifference.compareTo(expectedBuyDiff) != 0) {
                result.addError("买入差价计算错误: 期望 " + expectedBuyDiff + ", 实际 " + buyDifference);
            }
        }

        if (gatePrice != null && jupiterSellPrice != null) {
            BigDecimal expectedSellDiff = jupiterSellPrice.subtract(gatePrice);
            if (sellDifference.compareTo(expectedSellDiff) != 0) {
                result.addError("卖出差价计算错误: 期望 " + expectedSellDiff + ", 实际 " + sellDifference);
            }
        }

        return result;
    }

    public static class ValidationResult {
        private final List<String> errors = new ArrayList<>();
        private final List<String> warnings = new ArrayList<>();

        public void addError(String error) { errors.add(error); }
        public void addWarning(String warning) { warnings.add(warning); }

        public boolean isValid() { return errors.isEmpty(); }
        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }
    }
}
```

---

## 🔧 调试与故障排除

### 常见问题诊断工具
```java
public class DiagnosticTool {

    /**
     * 系统健康检查
     */
    public static void performHealthCheck() {
        System.out.println("=== 系统健康检查 ===");

        // 1. Java环境检查
        checkJavaEnvironment();

        // 2. 网络连接检查
        checkNetworkConnectivity();

        // 3. API可用性检查
        checkApiAvailability();

        // 4. 音频系统检查
        checkAudioSystem();

        // 5. 配置文件检查
        checkConfiguration();
    }

    private static void checkJavaEnvironment() {
        System.out.println("\n--- Java环境检查 ---");
        System.out.println("Java版本: " + System.getProperty("java.version"));
        System.out.println("Java厂商: " + System.getProperty("java.vendor"));
        System.out.println("操作系统: " + System.getProperty("os.name"));
        System.out.println("文件编码: " + System.getProperty("file.encoding"));
        System.out.println("时区: " + System.getProperty("user.timezone"));

        // 检查内存使用
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory() / 1024 / 1024;
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        long usedMemory = totalMemory - freeMemory;

        System.out.printf("内存使用: %dMB / %dMB (最大: %dMB)%n",
            usedMemory, totalMemory, maxMemory);
    }

    private static void checkNetworkConnectivity() {
        System.out.println("\n--- 网络连接检查 ---");

        String[] testUrls = {
            "https://api.gateio.ws",
            "https://quote-api.jup.ag",
            "https://price.jup.ag",
            "https://ultra-api.jup.ag"
        };

        for (String url : testUrls) {
            try {
                HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);

                int responseCode = connection.getResponseCode();
                System.out.printf("%-30s: %s%n", url,
                    responseCode == 200 ? "✅ 可访问" : "❌ 不可访问 (" + responseCode + ")");

            } catch (Exception e) {
                System.out.printf("%-30s: ❌ 连接失败 (%s)%n", url, e.getMessage());
            }
        }
    }

    private static void checkAudioSystem() {
        System.out.println("\n--- 音频系统检查 ---");

        try {
            // 检查音频设备
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            System.out.println("可用音频设备数量: " + mixers.length);

            // 检查音频文件
            String[] audioFiles = {"up.wav", "down.wav"};
            for (String audioFile : audioFiles) {
                InputStream audioStream = DiagnosticTool.class.getClassLoader()
                    .getResourceAsStream(audioFile);
                if (audioStream != null) {
                    System.out.println("✅ " + audioFile + " 文件存在");
                    audioStream.close();
                } else {
                    System.out.println("❌ " + audioFile + " 文件缺失");
                }
            }

            // 测试系统蜂鸣器
            System.out.println("测试系统蜂鸣器...");
            Toolkit.getDefaultToolkit().beep();
            System.out.println("✅ 系统蜂鸣器正常");

        } catch (Exception e) {
            System.out.println("❌ 音频系统检查失败: " + e.getMessage());
        }
    }
}
```

### 日志分析工具
```java
public class LogAnalyzer {

    /**
     * 分析系统日志，提取关键指标
     */
    public static void analyzeSystemLogs(String logFilePath) {
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(logFilePath))) {

            Map<String, Integer> errorCounts = new HashMap<>();
            Map<String, List<Long>> responseTimes = new HashMap<>();
            int totalRequests = 0;
            int successfulRequests = 0;

            String line;
            while ((line = reader.readLine()) != null) {
                // 分析错误日志
                if (line.contains("ERROR")) {
                    String errorType = extractErrorType(line);
                    errorCounts.merge(errorType, 1, Integer::sum);
                }

                // 分析API响应时间
                if (line.contains("API响应时间")) {
                    long responseTime = extractResponseTime(line);
                    String apiType = extractApiType(line);
                    responseTimes.computeIfAbsent(apiType, k -> new ArrayList<>()).add(responseTime);
                }

                // 统计请求数量
                if (line.contains("执行价格监控任务")) {
                    totalRequests++;
                }

                if (line.contains("价格监控完成")) {
                    successfulRequests++;
                }
            }

            // 生成分析报告
            generateAnalysisReport(errorCounts, responseTimes, totalRequests, successfulRequests);

        } catch (IOException e) {
            System.err.println("日志分析失败: " + e.getMessage());
        }
    }

    private static void generateAnalysisReport(
            Map<String, Integer> errorCounts,
            Map<String, List<Long>> responseTimes,
            int totalRequests,
            int successfulRequests) {

        System.out.println("=== 日志分析报告 ===");

        // 成功率统计
        double successRate = totalRequests > 0 ? (double) successfulRequests / totalRequests * 100 : 0;
        System.out.printf("请求成功率: %.2f%% (%d/%d)%n", successRate, successfulRequests, totalRequests);

        // 错误统计
        System.out.println("\n错误统计:");
        errorCounts.forEach((errorType, count) ->
            System.out.printf("  %s: %d次%n", errorType, count));

        // 响应时间统计
        System.out.println("\nAPI响应时间统计:");
        responseTimes.forEach((apiType, times) -> {
            double avgTime = times.stream().mapToLong(Long::longValue).average().orElse(0);
            long maxTime = times.stream().mapToLong(Long::longValue).max().orElse(0);
            System.out.printf("  %s: 平均 %.2fms, 最大 %dms%n", apiType, avgTime, maxTime);
        });
    }
}
```

---

**文档版本**: v1.0
**最后更新**: 2025-01-16
**维护者**: AI Agent
