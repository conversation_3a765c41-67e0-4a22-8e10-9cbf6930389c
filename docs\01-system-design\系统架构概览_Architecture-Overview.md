---
title: "PUMP代币价格监控系统架构概览"
author: "系统架构师"
created: "2025-01-16"
updated: "2025-01-16"
version: "1.0"
category: "system-design"
tags: ["architecture", "system-overview", "pump-monitoring"]
status: "active"
---

# PUMP代币价格监控系统架构概览

## 📋 功能说明

### 系统概述
PUMP代币价格监控系统是一个基于Java 11和Spring Boot的实时价格监控应用，专门用于监控PUMP代币在中心化交易所(Gate.io)和去中心化交易所(Jupiter DEX)之间的价格差异，并在发现套利机会时触发音频告警。

### 核心功能
- **实时价格监控**: 每2秒获取100万PUMP代币的CEX和DEX价格
- **套利分析**: 自动计算买入/卖出价格差异，识别套利机会
- **智能告警**: 基于阈值的音频告警系统，支持自定义音频文件
- **配置管理**: JSON配置内嵌到JAR，支持运行时参数调整
- **缓存优化**: 价格数据缓存机制，减少API调用频率

### 业务价值
- 提供实时的套利机会识别
- 降低手动监控成本
- 提升交易决策效率

## 🛠️ 实现方式

### 技术栈
- **后端框架**: Spring Boot 2.7.0
- **Java版本**: Java 11+
- **构建工具**: Maven 3.8+
- **HTTP客户端**: RestTemplate, HttpURLConnection
- **JSON处理**: Jackson ObjectMapper
- **日志系统**: SLF4J + Logback
- **音频处理**: Java Sound API

### 核心组件
| 组件名称 | 功能描述 | 技术实现 |
|---------|----------|----------|
| PumpApplication | 主程序启动类 | Spring Boot启动器 |
| PriceMonitorScheduler | 定时调度器 | @Scheduled注解 |
| PumpPriceService | 价格服务层 | Service层封装 |
| GateIoApiClient | Gate.io API客户端 | RestTemplate |
| JupiterApiClient | Jupiter API客户端 | HttpURLConnection |
| AlertSoundService | 音频告警服务 | Java Sound API |
| PumpConfigService | 配置管理服务 | JSON配置加载 |

## 📊 图示说明

### 系统架构图
```mermaid
graph TB
    subgraph "PUMP价格监控系统"
        A[PumpApplication<br/>主程序启动] --> B[PriceMonitorScheduler<br/>定时调度器]
        B --> C[PumpPriceService<br/>价格服务层]
        
        C --> D[GateIoApiClient<br/>CEX价格客户端]
        C --> E[JupiterApiClient<br/>DEX价格客户端]
        
        B --> F[AlertSoundService<br/>音频告警服务]
        
        G[PumpConfigService<br/>配置管理] --> B
        G --> F
        
        H[PriceCache<br/>价格缓存] --> E
    end
    
    subgraph "外部API服务"
        I[Gate.io API<br/>订单簿数据]
        J[Jupiter Quote API<br/>DEX报价]
        K[Jupiter Price API<br/>价格数据]
    end
    
    subgraph "配置与资源"
        L[application.properties<br/>Spring配置]
        M[pump-config.json<br/>业务配置]
        N[up.wav / down.wav<br/>告警音频]
    end
    
    D --> I
    E --> J
    E --> K
    
    G --> M
    A --> L
    F --> N
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style F fill:#fff3e0
```

### 数据流程图
```mermaid
sequenceDiagram
    participant Timer as 定时器
    participant Scheduler as PriceMonitorScheduler
    participant PriceService as PumpPriceService
    participant GateClient as GateIoApiClient
    participant JupiterClient as JupiterApiClient
    participant AlertService as AlertSoundService
    
    Timer->>Scheduler: 每2秒触发
    Scheduler->>PriceService: getCexPrice()
    PriceService->>GateClient: getPumpPrice()
    GateClient-->>PriceService: PriceData(CEX价格)
    
    Scheduler->>PriceService: getDexBuyPrice(1000000)
    PriceService->>JupiterClient: getQuotePrice(1000000, true)
    JupiterClient-->>PriceService: BigDecimal(买入总价)
    
    Scheduler->>Scheduler: 计算价格差异
    Scheduler->>AlertService: checkBuyAlert(差价)
    
    alt 差价超过阈值
        AlertService->>AlertService: 播放告警音频
    end
    
    Scheduler->>Scheduler: 输出格式化价格信息
```

## ⚙️ 配置示例

### 核心配置文件
```json
{
  "monitor": {
    "interval": 2000,
    "amount": 1000000,
    "comment": "监控间隔(毫秒)和监控数量(PUMP代币数量)"
  },
  "alert": {
    "enabled": true,
    "buyThreshold": 30.00,
    "sellThreshold": 30.00,
    "soundType": "CUSTOM",
    "comment": "报警配置 - 阈值单位为美元"
  },
  "api": {
    "timeout": 30000,
    "retryDelay": 2000,
    "comment": "API配置 - 超时时间和重试延迟(毫秒)"
  }
}
```

### Spring Boot配置
```properties
# 应用配置
spring.application.name=pump-price-monitor
spring.main.web-application-type=none

# 日志配置
logging.level.com.pump.scheduler=INFO
logging.pattern.console=%msg%n
logging.charset.console=UTF-8

# 价格监控配置
pump.monitor.interval=2000
pump.monitor.amount=1000000

# 报警音配置
pump.alert.enabled=true
pump.alert.buy-threshold=30.00
pump.alert.sell-threshold=30.00
```

## 📝 更新记录

| 日期 | 版本 | 更新内容 | 作者 |
|------|------|----------|------|
| 2025-01-16 | 1.0 | 初始版本，完整系统架构设计 | 系统架构师 |

---

**相关文档**: 
- [核心实现逻辑](核心实现逻辑_Implementation-Logic.md)
- [API集成指南](../02-api-integration/API集成指南_Integration-Guide.md)
- [部署配置指南](../03-deployment-ops/部署配置指南_Deployment-Guide.md)
