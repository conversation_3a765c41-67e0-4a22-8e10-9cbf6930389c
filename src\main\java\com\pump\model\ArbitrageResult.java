package com.pump.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 套利分析结果模型
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class ArbitrageResult {
    
    /** CEX价格数据 */
    private PriceData cexPrice;
    
    /** DEX价格数据 */
    private PriceData dexPrice;
    
    /** 交易数量 */
    private BigDecimal amount;
    
    /** CEX买入价格 */
    private BigDecimal cexBuyPrice;
    
    /** CEX卖出价格 */
    private BigDecimal cexSellPrice;
    
    /** DEX买入价格 */
    private BigDecimal dexBuyPrice;
    
    /** DEX卖出价格 */
    private BigDecimal dexSellPrice;
    
    /** 价格差异 */
    private BigDecimal priceDifference;
    
    /** 价格差异百分比 */
    private BigDecimal priceDifferencePercent;
    
    /** 交易建议 */
    private String tradeRecommendation;
    
    /** 推荐平台 */
    private String recommendedPlatform;
    
    /** 分析时间 */
    private LocalDateTime analysisTime;
    
    /** 是否有套利机会 */
    private boolean hasArbitrageOpportunity;
    
    /** 错误信息 */
    private String errorMessage;
    
    // 构造函数
    public ArbitrageResult() {
        this.analysisTime = LocalDateTime.now();
        this.hasArbitrageOpportunity = false;
    }
    
    public ArbitrageResult(PriceData cexPrice, PriceData dexPrice, BigDecimal amount) {
        this();
        this.cexPrice = cexPrice;
        this.dexPrice = dexPrice;
        this.amount = amount;
    }
    
    // Getter和Setter方法
    public PriceData getCexPrice() {
        return cexPrice;
    }
    
    public void setCexPrice(PriceData cexPrice) {
        this.cexPrice = cexPrice;
    }
    
    public PriceData getDexPrice() {
        return dexPrice;
    }
    
    public void setDexPrice(PriceData dexPrice) {
        this.dexPrice = dexPrice;
    }
    
    public BigDecimal getAmount() {
        return amount;
    }
    
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    
    public BigDecimal getCexBuyPrice() {
        return cexBuyPrice;
    }
    
    public void setCexBuyPrice(BigDecimal cexBuyPrice) {
        this.cexBuyPrice = cexBuyPrice;
    }
    
    public BigDecimal getCexSellPrice() {
        return cexSellPrice;
    }
    
    public void setCexSellPrice(BigDecimal cexSellPrice) {
        this.cexSellPrice = cexSellPrice;
    }
    
    public BigDecimal getDexBuyPrice() {
        return dexBuyPrice;
    }
    
    public void setDexBuyPrice(BigDecimal dexBuyPrice) {
        this.dexBuyPrice = dexBuyPrice;
    }
    
    public BigDecimal getDexSellPrice() {
        return dexSellPrice;
    }
    
    public void setDexSellPrice(BigDecimal dexSellPrice) {
        this.dexSellPrice = dexSellPrice;
    }
    
    public BigDecimal getPriceDifference() {
        return priceDifference;
    }
    
    public void setPriceDifference(BigDecimal priceDifference) {
        this.priceDifference = priceDifference;
    }
    
    public BigDecimal getPriceDifferencePercent() {
        return priceDifferencePercent;
    }
    
    public void setPriceDifferencePercent(BigDecimal priceDifferencePercent) {
        this.priceDifferencePercent = priceDifferencePercent;
    }
    
    public String getTradeRecommendation() {
        return tradeRecommendation;
    }
    
    public void setTradeRecommendation(String tradeRecommendation) {
        this.tradeRecommendation = tradeRecommendation;
    }
    
    public String getRecommendedPlatform() {
        return recommendedPlatform;
    }
    
    public void setRecommendedPlatform(String recommendedPlatform) {
        this.recommendedPlatform = recommendedPlatform;
    }
    
    public LocalDateTime getAnalysisTime() {
        return analysisTime;
    }
    
    public void setAnalysisTime(LocalDateTime analysisTime) {
        this.analysisTime = analysisTime;
    }
    
    public boolean isHasArbitrageOpportunity() {
        return hasArbitrageOpportunity;
    }
    
    public void setHasArbitrageOpportunity(boolean hasArbitrageOpportunity) {
        this.hasArbitrageOpportunity = hasArbitrageOpportunity;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    @Override
    public String toString() {
        return String.format("ArbitrageResult{amount=%s, cexPrice=%s, dexPrice=%s, priceDifference=%s, priceDifferencePercent=%s%%, tradeRecommendation='%s', recommendedPlatform='%s', hasArbitrageOpportunity=%s, analysisTime=%s}", 
                           amount, cexBuyPrice, dexBuyPrice, priceDifference, priceDifferencePercent, tradeRecommendation, recommendedPlatform, hasArbitrageOpportunity, analysisTime);
    }
} 