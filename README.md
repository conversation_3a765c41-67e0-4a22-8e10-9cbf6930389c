# PUMP价格监控系统

## 🚀 快速启动

```bash
# 主启动脚本（推荐）
pump.bat

# 或使用scripts目录下的启动脚本
scripts\startup\pump30.bat
scripts\startup\pump-fixed.bat
```

## 📁 目录结构

```
pump/
├── pump.bat                    # 主启动脚本
├── pom.xml                     # Maven项目配置
├── .cursorrules               # 开发规则配置
│
├── src/                       # 源代码
│   ├── main/
│   │   ├── java/             # Java源码
│   │   └── resources/        # 资源文件（包含音频文件）
│   └── test/                 # 测试代码
│
├── docs/                      # 文档目录
│   ├── 01-system-design/     # 系统设计文档
│   ├── 02-api-integration/   # API集成文档
│   ├── 03-deployment-ops/    # 部署运维文档
│   ├── 04-monitoring-alerting/ # 监控告警文档
│   ├── 05-testing-docs/      # 测试文档
│   ├── 06-issue-fixes/       # 问题修复文档
│   ├── 07-project-management/ # 项目管理文档
│   └── 08-reference-materials/ # 参考资料
│
├── scripts/                   # 脚本目录
│   ├── startup/              # 启动脚本
│   ├── tools/                # 工具脚本
│   └── archived/             # 历史脚本归档
│
├── tools/                     # 开发工具
│   ├── update-audio-config.bat # 音频配置工具
│   └── README.md             # 工具说明
│
└── target/                    # Maven构建输出
    └── pump.jar              # 可执行JAR文件
```

## ⚙️ 音频配置

系统支持两种音频告警方式：

- **SYSTEM**: 系统蜂鸣音
- **CUSTOM**: 自定义音频文件（up.wav/down.wav）

使用 `tools\update-audio-config.bat` 可以切换音频配置。

## 📖 文档

详细文档请查看 `docs/` 目录：

- 系统架构：`docs/01-system-design/`
- 部署指南：`docs/03-deployment-ops/`
- 问题修复：`docs/06-issue-fixes/`

## 🎯 功能特性

- ✅ 实时PUMP代币价格监控
- ✅ Gate.io和Jupiter DEX价格对比
- ✅ 套利机会音频告警
- ✅ 多种音频告警方式
- ✅ UTF-8编码支持，无乱码
- ✅ 简洁的控制台输出

---

**快速开始**: 双击 `pump.bat` 即可启动系统！ 