package com.pump.service;

import java.awt.Toolkit;
import java.io.BufferedInputStream;
import java.io.InputStream;
import javax.sound.sampled.*;

/**
 * 音频功能测试程序
 * 用于诊断音频播放问题
 */
public class AudioTestMain {
    
    public static void main(String[] args) {
        System.out.println("=== 音频功能诊断测试 ===");
        
        // 1. 测试系统蜂鸣器
        testSystemBeep();
        
        // 2. 测试音频系统可用性
        testAudioSystemAvailability();
        
        // 3. 测试音频文件加载
        testAudioFileLoading();
        
        // 4. 测试音频播放
        testAudioPlayback();
        
        System.out.println("=== 测试完成 ===");
    }
    
    private static void testSystemBeep() {
        System.out.println("\n1. 测试系统蜂鸣器...");
        try {
            System.out.println("播放系统蜂鸣音（应该听到声音）...");
            Toolkit.getDefaultToolkit().beep();
            Thread.sleep(1000);
            System.out.println("✅ 系统蜂鸣器正常");
        } catch (Exception e) {
            System.out.println("❌ 系统蜂鸣器失败: " + e.getMessage());
        }
    }
    
    private static void testAudioSystemAvailability() {
        System.out.println("\n2. 测试音频系统可用性...");
        try {
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            System.out.println("可用音频设备数量: " + mixers.length);
            
            for (Mixer.Info mixerInfo : mixers) {
                System.out.println("  - " + mixerInfo.getName() + ": " + mixerInfo.getDescription());
            }
            
            if (mixers.length > 0) {
                System.out.println("✅ 音频系统可用");
            } else {
                System.out.println("❌ 没有可用的音频设备");
            }
        } catch (Exception e) {
            System.out.println("❌ 音频系统检查失败: " + e.getMessage());
        }
    }
    
    private static void testAudioFileLoading() {
        System.out.println("\n3. 测试音频文件加载...");
        String[] audioFiles = {"up.wav", "down.wav"};
        
        for (String audioFile : audioFiles) {
            System.out.println("测试加载: " + audioFile);
            
            // 测试多种加载方式
            InputStream stream = loadAudioResourceSafely(audioFile);
            if (stream != null) {
                try {
                    System.out.println("  ✅ 加载成功，支持mark/reset: " + stream.markSupported());
                    System.out.println("  文件大小: " + stream.available() + " bytes");
                    
                    // 测试AudioInputStream创建
                    AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(stream);
                    AudioFormat format = audioInputStream.getFormat();
                    System.out.printf("  音频格式: %.0fHz, %dbit, %dch%n", 
                        format.getSampleRate(), format.getSampleSizeInBits(), format.getChannels());
                    
                    audioInputStream.close();
                } catch (Exception e) {
                    System.out.println("  ❌ 格式验证失败: " + e.getMessage());
                }
            } else {
                System.out.println("  ❌ 加载失败");
            }
        }
    }
    
    private static void testAudioPlayback() {
        System.out.println("\n4. 测试音频播放...");
        
        // 测试播放up.wav
        System.out.println("播放 up.wav（应该听到声音）...");
        if (playTestAudio("up.wav")) {
            System.out.println("✅ up.wav 播放成功");
        } else {
            System.out.println("❌ up.wav 播放失败");
        }
        
        try {
            Thread.sleep(2000); // 等待2秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试播放down.wav
        System.out.println("播放 down.wav（应该听到声音）...");
        if (playTestAudio("down.wav")) {
            System.out.println("✅ down.wav 播放成功");
        } else {
            System.out.println("❌ down.wav 播放失败");
        }
    }
    
    private static boolean playTestAudio(String audioFileName) {
        try {
            InputStream rawStream = loadAudioResourceSafely(audioFileName);
            if (rawStream == null) {
                System.out.println("  无法加载音频文件: " + audioFileName);
                return false;
            }
            
            // 确保支持mark/reset
            InputStream audioStream = rawStream.markSupported() ? 
                rawStream : new BufferedInputStream(rawStream, 8192);
            
            // 创建AudioInputStream
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            
            // 播放音频
            Clip clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            clip.start();
            
            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(100);
            }
            
            // 清理资源
            clip.close();
            audioInputStream.close();
            audioStream.close();
            
            return true;
            
        } catch (Exception e) {
            System.out.println("  播放失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    private static InputStream loadAudioResourceSafely(String audioFileName) {
        // 定义多个可能的资源路径
        String[] resourcePaths = {
            "sounds/" + audioFileName,     // sounds目录
            audioFileName,                 // 根目录
            "/" + audioFileName,           // 绝对根路径
            "/sounds/" + audioFileName     // 绝对sounds路径
        };
        
        // 1. 尝试Class.getResourceAsStream()
        for (String path : resourcePaths) {
            InputStream stream = AudioTestMain.class.getResourceAsStream("/" + path);
            if (stream != null) {
                System.out.println("  通过Class.getResourceAsStream()加载成功: /" + path);
                return stream;
            }
        }
        
        // 2. 尝试ClassLoader.getResourceAsStream()
        for (String path : resourcePaths) {
            InputStream stream = AudioTestMain.class.getClassLoader().getResourceAsStream(path);
            if (stream != null) {
                System.out.println("  通过ClassLoader.getResourceAsStream()加载成功: " + path);
                return stream;
            }
        }
        
        // 3. 尝试系统ClassLoader
        for (String path : resourcePaths) {
            InputStream stream = ClassLoader.getSystemResourceAsStream(path);
            if (stream != null) {
                System.out.println("  通过SystemClassLoader加载成功: " + path);
                return stream;
            }
        }
        
        System.out.println("  所有路径都无法加载音频文件: " + audioFileName);
        return null;
    }
}
