---
title: "PUMP代币价格监控系统部署配置指南"
author: "运维工程师"
created: "2025-01-16"
updated: "2025-01-16"
version: "1.0"
category: "deployment-ops"
tags: ["deployment", "configuration", "jar", "production"]
status: "active"
---

# PUMP代币价格监控系统部署配置指南

## 📋 功能说明

### 部署概述
PUMP代币价格监控系统采用JAR包独立部署方式，支持Windows、Linux和macOS多平台运行。系统配置内嵌到JAR包中，简化部署流程。

### 部署特点
- **独立JAR包**: 所有依赖和配置内嵌
- **跨平台支持**: Windows/Linux/macOS
- **UTF-8编码**: 完善的中文字符支持
- **音频文件内嵌**: 告警音频文件打包到JAR中
- **配置热加载**: 支持运行时配置调整

## 🛠️ 实现方式

### 系统要求
| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| Java | JDK 11+ | JDK 17+ |
| 内存 | 512MB | 1GB |
| 磁盘 | 100MB | 500MB |
| 网络 | 访问Gate.io和Jupiter API | 稳定网络连接 |
| 音频 | 支持WAV格式播放 | 音频设备正常 |

### 构建部署包
```bash
# 1. 清理和编译
mvn clean compile

# 2. 运行测试
mvn test

# 3. 打包JAR
mvn package

# 4. 验证JAR包
ls -la target/pump30.jar
```

### JAR包结构
```
pump30.jar
├── BOOT-INF/
│   ├── classes/
│   │   ├── application.properties      # Spring配置
│   │   ├── pump-config.json           # 业务配置
│   │   ├── up.wav                     # 买入告警音频
│   │   ├── down.wav                   # 卖出告警音频
│   │   └── com/pump/                  # 应用代码
│   └── lib/                           # 依赖JAR包
├── META-INF/
└── org/springframework/boot/loader/   # Spring Boot加载器
```

## 📊 图示说明

### 部署架构图
```mermaid
graph TB
    subgraph "部署环境"
        A[pump30.jar] --> B[JVM进程]
        B --> C[Spring Boot应用]
        
        C --> D[价格监控服务]
        C --> E[音频告警服务]
        C --> F[配置管理服务]
    end
    
    subgraph "外部依赖"
        G[Gate.io API]
        H[Jupiter API]
        I[系统音频设备]
    end
    
    subgraph "配置文件"
        J[application.properties<br/>内嵌配置]
        K[pump-config.json<br/>业务配置]
        L[up.wav/down.wav<br/>音频文件]
    end
    
    D --> G
    D --> H
    E --> I
    
    F --> J
    F --> K
    E --> L
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
```

### 启动流程图
```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 启动脚本
    participant J as JVM
    participant A as Spring应用
    participant C as 配置服务
    participant M as 监控服务
    
    U->>S: 执行启动脚本
    S->>S: 设置UTF-8编码
    S->>J: 启动JVM进程
    J->>A: 加载Spring Boot应用
    A->>C: 初始化配置服务
    C->>C: 加载pump-config.json
    A->>M: 启动价格监控服务
    M->>M: 开始定时监控任务
    M-->>U: 输出价格监控信息
```

### 配置加载流程
```mermaid
flowchart TD
    A[应用启动] --> B[EncodingInitializer]
    B --> C[强制UTF-8编码]
    C --> D[Spring容器初始化]
    D --> E[PumpConfigService.@PostConstruct]
    E --> F{pump-config.json存在?}
    F -->|是| G[加载JSON配置]
    F -->|否| H[使用默认配置]
    G --> I[配置验证]
    H --> I
    I --> J{配置有效?}
    J -->|是| K[配置服务就绪]
    J -->|否| L[启动失败]
    K --> M[开始价格监控]
    
    style K fill:#e8f5e8
    style L fill:#ffebee
    style M fill:#fff3e0
```

## ⚙️ 配置示例

### Windows启动脚本 (pump.bat)
```batch
@echo off
REM PUMP30 启动器 - 自动设置UTF-8编码
chcp 65001 >nul 2>&1
java -Dfile.encoding=UTF-8 ^
     -Dconsole.encoding=UTF-8 ^
     -Dsun.jnu.encoding=UTF-8 ^
     -Duser.timezone=Asia/Shanghai ^
     -jar pump30.jar %*
```

### PowerShell启动脚本 (Start-Pump30.ps1)
```powershell
# PUMP30价格监控系统启动脚本
Write-Host "🚀 启动PUMP30价格监控系统..." -ForegroundColor Yellow
Write-Host "💡 按 Ctrl+C 停止程序" -ForegroundColor Gray

# 设置UTF-8环境变量
$env:JAVA_TOOL_OPTIONS = "-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8"

try {
    # 启动JAR包
    java -Dfile.encoding=UTF-8 `
         -Dconsole.encoding=UTF-8 `
         -Duser.timezone=Asia/Shanghai `
         -jar pump30.jar
} catch {
    Write-Host "❌ 程序运行出错: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Write-Host "🛑 PUMP30价格监控系统已停止" -ForegroundColor Yellow
    Read-Host "按任意键退出"
}
```

### Linux启动脚本 (start-pump.sh)
```bash
#!/bin/bash
# PUMP30价格监控系统启动脚本

echo "🚀 启动PUMP30价格监控系统..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ Java未安装或未配置到PATH"
    exit 1
fi

# 检查JAR文件
if [ ! -f "pump30.jar" ]; then
    echo "❌ pump30.jar文件不存在"
    exit 1
fi

# 设置JVM参数
JAVA_OPTS="-Dfile.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Dconsole.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Dsun.jnu.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Duser.timezone=Asia/Shanghai"

# 启动应用
java $JAVA_OPTS -jar pump30.jar

echo "🛑 PUMP30价格监控系统已停止"
```

### 系统服务配置 (systemd)
```ini
[Unit]
Description=PUMP Price Monitor Service
After=network.target

[Service]
Type=simple
User=pump
WorkingDirectory=/opt/pump-monitor
ExecStart=/usr/bin/java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar pump30.jar
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### Docker部署配置
```dockerfile
FROM openjdk:11-jre-slim

# 设置工作目录
WORKDIR /app

# 复制JAR文件
COPY pump30.jar .

# 设置环境变量
ENV JAVA_OPTS="-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Duser.timezone=Asia/Shanghai"

# 暴露端口（如果需要）
# EXPOSE 8080

# 启动命令
CMD ["sh", "-c", "java $JAVA_OPTS -jar pump30.jar"]
```

### Docker Compose配置
```yaml
version: '3.8'

services:
  pump-monitor:
    build: .
    container_name: pump-price-monitor
    restart: unless-stopped
    environment:
      - JAVA_OPTS=-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Duser.timezone=Asia/Shanghai
    volumes:
      - ./logs:/app/logs
    networks:
      - pump-network

networks:
  pump-network:
    driver: bridge
```

## 📝 更新记录

| 日期 | 版本 | 更新内容 | 作者 |
|------|------|----------|------|
| 2025-01-16 | 1.0 | 初始版本，部署配置指南 | 运维工程师 |

---

**相关文档**: 
- [环境配置说明](环境配置说明_Environment-Setup.md)
- [监控告警配置](../04-monitoring-alerting/监控告警配置_Monitoring-Setup.md)
- [故障排查指南](../06-issue-fixes/故障排查指南_Troubleshooting-Guide.md)
