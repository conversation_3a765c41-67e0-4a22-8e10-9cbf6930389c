# PUMP价格监控系统 - 构建和部署脚本使用指南

## 📋 脚本概述

本项目提供了两个构建和部署脚本，用于自动化完成以下任务：
1. **修改报警阈值配置**
2. **编译打包JAR文件**
3. **创建带日期的打包记录目录**
4. **复制JAR包并重命名（包含阈值信息）**
5. **生成对应的启动脚本**

## 🛠️ 可用脚本

### 1. PowerShell版本（推荐）
- **文件**: `tools/build-and-deploy.ps1`
- **特点**: 功能完整，交互性强，自动读取和修改配置文件
- **适用**: Windows PowerShell 环境

### 2. 批处理版本
- **文件**: `tools/build-and-deploy.bat`
- **特点**: 简单易用，兼容性好
- **限制**: 需要手动修改配置文件中的阈值
- **适用**: 所有Windows环境

## 🚀 使用方法

### PowerShell版本使用步骤

1. **打开PowerShell**
   ```powershell
   # 在项目根目录下打开PowerShell
   cd E:\pump
   ```

2. **运行脚本**
   ```powershell
   .\tools\build-and-deploy.ps1
   ```

3. **交互式配置**
   - 脚本会显示当前的买入和卖出阈值
   - 输入新的阈值（或按回车保持当前值）
   - 确认配置后开始构建

4. **自动处理**
   - 脚本自动修改配置文件
   - 执行Maven编译打包
   - 创建日期目录
   - 复制JAR包并重命名
   - 生成启动脚本和说明文件

### 批处理版本使用步骤

1. **手动修改配置文件**（可选）
   ```json
   # 编辑 src/main/resources/pump-config.json
   {
     "alert": {
       "buyThreshold": 1.5,    # 修改买入阈值
       "sellThreshold": 1.0    # 修改卖出阈值
     }
   }
   ```

2. **运行脚本**
   ```cmd
   tools\build-and-deploy.bat
   ```

3. **输入阈值**
   - 按提示输入买入阈值
   - 按提示输入卖出阈值

4. **自动处理**
   - 其余步骤与PowerShell版本相同

## 📁 输出结构

运行脚本后，会在项目根目录下创建以下结构：

```
builds/
└── 2025-01-17/                         # 简化日期目录 (yyyy-MM-dd)
    ├── pump1.5.jar                     # 智能命名 (相同阈值)
    ├── start_pump1.5.bat               # 对应启动脚本
    └── README.txt                      # 说明文件
```

### 文件说明

- **JAR包**: 智能命名规则
  - 相同阈值: `pump{阈值}.jar` (如: pump1.5.jar)
  - 不同阈值: `pump_buy{买入}_sell{卖出}.jar`
  - 同名文件自动覆盖

- **启动脚本**: 对应的启动脚本
  - 相同阈值: `start_pump{阈值}.bat`
  - 不同阈值: `start_pump_buy{买入}_sell{卖出}.bat`
  - 设置UTF-8编码，显示配置信息

- **说明文件**: `README.txt`
  - 包含构建信息
  - 使用说明
  - 系统要求

## ⚙️ 配置文件说明

脚本会自动修改 `src/main/resources/pump-config.json` 中的以下字段：

```json
{
  "alert": {
    "buyThreshold": 1.0,     # 买入报警阈值（美元）
    "sellThreshold": 1.0     # 卖出报警阈值（美元）
  }
}
```

## 🎯 使用场景

### 场景1：测试不同阈值
```powershell
# 运行脚本，设置买入阈值2.0，卖出阈值1.5
.\tools\build-and-deploy.ps1
# 输入: 2.0, 1.5
# 生成: builds/2025-01-17/pump_buy2.0_sell1.5.jar
```

### 场景2：生产环境部署
```powershell
# 运行脚本，设置相同阈值（智能命名）
.\tools\build-and-deploy.ps1
# 输入: 1.5, 1.5
# 生成: builds/2025-01-17/pump1.5.jar
# 将生成的包部署到生产服务器
```

### 场景3：版本管理
```
builds/
├── 2025-01-17/    # 测试版本
│   ├── pump2.0.jar                    # 买入2.0卖出2.0
│   └── pump_buy2.0_sell1.5.jar        # 买入2.0卖出1.5
├── 2025-01-18/    # 优化版本
│   └── pump1.0.jar                    # 买入1.0卖出1.0
└── 2025-01-19/    # 生产版本
    └── pump0.5.jar                    # 买入0.5卖出0.5
```

## 🔧 系统要求

### 运行环境
- **操作系统**: Windows 10/11
- **Java**: JDK 8 或以上版本
- **Maven**: 3.6 或以上版本

### PowerShell要求
- **版本**: PowerShell 5.0 或以上
- **执行策略**: RemoteSigned 或 Unrestricted
  ```powershell
  # 检查执行策略
  Get-ExecutionPolicy
  
  # 如需修改（管理员权限）
  Set-ExecutionPolicy RemoteSigned
  ```

## 🛡️ 安全注意事项

1. **配置备份**: 脚本会自动备份原配置文件
2. **构建隔离**: 每次构建都在独立的日期目录中
3. **版本控制**: 建议在Git中管理配置文件变更

## 🔍 故障排除

### 常见问题

1. **PowerShell执行策略错误**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **Maven命令不可用**
   ```cmd
   # 检查Maven是否在PATH中
   mvn --version
   ```

3. **编译失败**
   ```powershell
   # 清理后重新编译
   mvn clean compile
   ```

4. **编码问题**
   - 确保控制台支持UTF-8编码
   - 脚本已自动设置UTF-8编码

## 📞 技术支持

如果遇到问题，请检查：
1. 系统要求是否满足
2. 配置文件格式是否正确
3. 网络连接是否正常（Maven依赖下载）

---

**版本**: v1.0  
**更新日期**: 2025-01-17  
**维护者**: PUMP Project Team 