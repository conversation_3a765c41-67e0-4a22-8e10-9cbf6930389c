package com.pump.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * PUMP价格数据模型
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class PriceData {
    
    /** 交易所类型 */
    private String exchange;
    
    /** 交易对 */
    private String symbol;
    
    /** 买入价格 */
    private BigDecimal buyPrice;
    
    /** 卖出价格 */
    private BigDecimal sellPrice;
    
    /** 最新价格 */
    private BigDecimal lastPrice;
    
    /** 价格更新时间 */
    private LocalDateTime timestamp;
    
    /** 交易量 */
    private BigDecimal volume;
    
    /** 是否有效 */
    private boolean valid;
    
    /** 错误信息 */
    private String errorMessage;
    
    // 构造函数
    public PriceData() {
        this.timestamp = LocalDateTime.now();
        this.valid = true;
        this.symbol = "PUMP/USDT";
    }
    
    public PriceData(String exchange, BigDecimal buyPrice, BigDecimal sellPrice, BigDecimal lastPrice) {
        this();
        this.exchange = exchange;
        this.buyPrice = buyPrice;
        this.sellPrice = sellPrice;
        this.lastPrice = lastPrice;
    }
    
    // Getter和Setter方法
    public String getExchange() {
        return exchange;
    }
    
    public void setExchange(String exchange) {
        this.exchange = exchange;
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public BigDecimal getBuyPrice() {
        return buyPrice;
    }
    
    public void setBuyPrice(BigDecimal buyPrice) {
        this.buyPrice = buyPrice;
    }
    
    public BigDecimal getSellPrice() {
        return sellPrice;
    }
    
    public void setSellPrice(BigDecimal sellPrice) {
        this.sellPrice = sellPrice;
    }
    
    public BigDecimal getLastPrice() {
        return lastPrice;
    }
    
    public void setLastPrice(BigDecimal lastPrice) {
        this.lastPrice = lastPrice;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public BigDecimal getVolume() {
        return volume;
    }
    
    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }
    
    public boolean isValid() {
        return valid;
    }
    
    public void setValid(boolean valid) {
        this.valid = valid;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        this.valid = false;
    }
    
    @Override
    public String toString() {
        return String.format("PriceData{exchange='%s', symbol='%s', buyPrice=%s, sellPrice=%s, lastPrice=%s, timestamp=%s, valid=%s}", 
                           exchange, symbol, buyPrice, sellPrice, lastPrice, timestamp, valid);
    }
} 