@echo off
REM 强制设置控制台编码为UTF-8以避免乱码
chcp 65001 > nul
echo 🚀 启动PUMP价格监控系统
echo 📅 %date% %time%
echo.

cd /d "%~dp0"

REM 检查JAR文件是否存在
if not exist "target\pump.jar" (
    echo ❌ 未找到JAR文件，正在构建...
    mvn package -DskipTests
    if errorlevel 1 (
        echo ❌ 构建失败！
        pause
        exit /b 1
    )
)

echo ✅ 准备启动PUMP系统...
echo.

REM 使用优化的Java参数启动，包含UTF-8编码设置
java -Dfile.encoding=UTF-8 ^
     -Dconsole.encoding=UTF-8 ^
     -Xms256m -Xmx512m ^
     -jar target/pump.jar

echo.
echo �� PUMP系统已退出

pause 