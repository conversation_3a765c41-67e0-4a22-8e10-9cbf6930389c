# Jupiter API 限制和报警优化说明

## 📊 1. Jupiter API 访问频率限制详情

### **免费版本 (lite-api.jup.ag)**
- **频率限制**: 60 requests/minute
- **令牌分配**: 60 tokens per 1 minute  
- **无需API Key**
- **适用场景**: 测试和小规模应用

### **付费版本 (api.jup.ag)**
| 等级 | 频率限制 | 令牌分配 | 月费用 |
|------|----------|----------|--------|
| **Pro I** | 600 requests/min | 100 tokens/10s | 付费 |
| **Pro II** | 3,000 requests/min | 500 tokens/10s | 付费 |
| **Pro III** | 6,000 requests/min | 1,000 tokens/10s | 付费 |
| **Pro IV** | 30,000 requests/min | 5,000 tokens/10s | 付费 |

### **令牌桶机制**
- 每个账户有两个独立的令牌桶：
  1. **默认桶**: 用于除Price API外的所有API
  2. **Price API桶**: 专门用于Price API
- 免费版本只使用默认桶处理所有请求
- 超出限制时返回 `429 Too Many Requests`

## ⚠️ 2. 当前系统API使用分析

### **API调用频率**
- **监控间隔**: 每3秒一次（已调整）
- **每次调用**: 3个API请求
  - Gate.io价格查询: 1次
  - Ultra API买入价格: 1次  
  - Ultra API卖出价格: 1次
- **每分钟总调用**: 20次 × 3 = 60次

### **调整前后对比**
| 配置 | 间隔 | 每分钟调用 | 是否超限 |
|------|------|------------|----------|
| **调整前** | 1秒 | 180次 | ❌ 超出限制 |
| **调整后** | 3秒 | 60次 | ✅ 符合限制 |

## 🔧 3. 报警功能优化

### **优化内容**
1. **✅ 移除额外打印**: 报警时只播放音效，不显示额外的控制台信息
2. **✅ 保持日志记录**: 重要的价格和差价信息仍记录在日志中
3. **✅ 音效触发**: 当差价超过阈值时自动播放报警音

### **报警触发条件**
- **买入报警**: 买入差价 > 0 且 差价 >= $30.00
- **卖出报警**: 卖出差价 > 0 且 差价 >= $30.00
- **冷却时间**: 30秒（避免频繁报警）

### **音效类型**
- **SYSTEM**: 系统提示音（买入2声，卖出3声）
- **MULTIPLE**: 多重提示音（买入4声，卖出5声）
- **CUSTOM**: 自定义WAV文件

## 📈 4. 性能优化建议

### **短期优化**
1. **✅ 已实施**: 调整监控间隔为3秒
2. **✅ 已实施**: 移除不必要的控制台输出
3. **建议**: 添加API响应时间监控

### **长期优化**
1. **升级到付费版本**: 如需更高频率监控
2. **实现缓存机制**: 减少重复API调用
3. **错误重试机制**: 处理429限流错误

### **监控间隔建议**
| 使用场景 | 建议间隔 | API调用/分钟 | 适用版本 |
|----------|----------|--------------|----------|
| **测试环境** | 5-10秒 | 18-36次 | 免费版 |
| **生产监控** | 3秒 | 60次 | 免费版极限 |
| **高频交易** | 1秒 | 180次 | Pro I+ |
| **实时套利** | 0.5秒 | 360次 | Pro II+ |

## 🚨 5. API限流处理

### **429错误处理**
```java
// 建议添加的重试逻辑
if (responseCode == 429) {
    // 指数退避重试
    Thread.sleep(Math.min(1000 * Math.pow(2, retryCount), 30000));
    // 重新尝试请求
}
```

### **监控指标**
- API响应时间
- 成功率统计
- 限流错误频率
- 价格获取失败率

## 💡 6. 成本效益分析

### **免费版本**
- **优点**: 无成本，适合测试
- **缺点**: 频率限制，可能影响套利时机
- **适用**: 学习和小规模监控

### **付费版本**
- **优点**: 更高频率，更好性能
- **缺点**: 需要月费
- **适用**: 专业交易和实时套利

## 🎯 7. 当前配置总结

### **已优化配置**
```properties
# 价格监控配置
pump.monitor.interval=3000          # 3秒间隔
pump.monitor.amount=1000000         # 100万PUMP

# 报警音配置  
pump.alert.enabled=true             # 启用报警
pump.alert.buy-threshold=30.00      # 买入阈值$30
pump.alert.sell-threshold=30.00     # 卖出阈值$30
pump.alert.cooldown=30000           # 30秒冷却
pump.alert.sound-type=SYSTEM        # 系统音效
```

### **预期效果**
- ✅ 符合免费API限制
- ✅ 保持有效的价格监控
- ✅ 及时的套利机会提醒
- ✅ 简洁的报警输出

现在系统已经优化完成，可以在免费API限制下稳定运行，并提供有效的套利机会监控！
