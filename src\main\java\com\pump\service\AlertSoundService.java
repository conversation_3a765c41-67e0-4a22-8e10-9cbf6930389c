package com.pump.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import com.pump.config.PumpConfigService;

import java.awt.Toolkit;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;
import javax.sound.sampled.*;

/**
 * 报警音服务
 * 简化版本，只保留核心功能和必要的音频播放方式
 * 
 * <AUTHOR> Agent
 * @version 2.0
 * @since 2025-01-17
 */
@Service
public class AlertSoundService {

    private static final Logger logger = LoggerFactory.getLogger(AlertSoundService.class);
    
    @Autowired
    private PumpConfigService configService;
    
    // 音频播放状态控制（防止重叠播放）
    private volatile boolean isBuyAudioPlaying = false;
    private volatile boolean isSellAudioPlaying = false;
    
    /**
     * 检查并触发买入价格报警
     */
    public void checkBuyAlert(BigDecimal buyDifference, BigDecimal buyPrice, BigDecimal gatePrice) {
        if (!configService.isAlertEnabled() || buyDifference == null) {
            return;
        }

        if (shouldTriggerBuyAlert(buyDifference)) {
            String alertMessage = String.format("🔥 买入套利机会！Ultra API成本更低 $%.2f (Gate: $%.2f vs Ultra: $%.2f)",
                buyDifference, gatePrice, buyPrice);
            triggerAlert(AlertType.BUY_OPPORTUNITY, alertMessage);
        }
    }
    
    /**
     * 检查并触发卖出价格报警
     */
    public void checkSellAlert(BigDecimal sellDifference, BigDecimal sellPrice, BigDecimal gatePrice) {
        if (!configService.isAlertEnabled() || sellDifference == null) {
            return;
        }

        if (shouldTriggerSellAlert(sellDifference)) {
            String alertMessage = String.format("🔥 卖出套利机会！Ultra API收入更高 $%.2f (Ultra: $%.2f vs Gate: $%.2f)",
                sellDifference, sellPrice, gatePrice);
            triggerAlert(AlertType.SELL_OPPORTUNITY, alertMessage);
        }
    }
    
    /**
     * 判断是否应该触发买入报警
     */
    private boolean shouldTriggerBuyAlert(BigDecimal buyDifference) {
        if (isBuyAudioPlaying) {
            return false;
        }
        
        BigDecimal threshold = configService.getBuyThreshold();
        return buyDifference.compareTo(BigDecimal.ZERO) > 0 && 
               buyDifference.compareTo(threshold) > 0;
    }
    
    /**
     * 判断是否应该触发卖出报警
     */
    private boolean shouldTriggerSellAlert(BigDecimal sellDifference) {
        if (isSellAudioPlaying) {
            return false;
        }
        
        BigDecimal threshold = configService.getSellThreshold();
        boolean thresholdMet = sellDifference.compareTo(BigDecimal.ZERO) > 0 && 
                              sellDifference.compareTo(threshold) > 0;
        
        if (configService.isContinuousPlayEnabled()) {
            return thresholdMet;
        }
        
        return thresholdMet;
    }
    
    /**
     * 触发报警音
     */
    private void triggerAlert(AlertType alertType, String message) {
        CompletableFuture.runAsync(() -> {
            try {
                // 设置播放状态
                if (alertType == AlertType.BUY_OPPORTUNITY) {
                    isBuyAudioPlaying = true;
                } else {
                    isSellAudioPlaying = true;
                }

                String soundType = configService.getSoundType();
                
                switch (soundType.toUpperCase()) {
                    case "SYSTEM":
                        playSystemBeep(alertType);
                        break;
                    case "CUSTOM":
                        playCustomSound(alertType);
                        break;
                    default:
                        playSystemBeep(alertType);
                }

            } catch (Exception e) {
                logger.error("播放报警音失败: {}", e.getMessage());
            } finally {
                // 重置播放状态
                if (alertType == AlertType.BUY_OPPORTUNITY) {
                    isBuyAudioPlaying = false;
                } else {
                    isSellAudioPlaying = false;
                }
            }
        });
    }
    
    /**
     * 播放系统提示音
     */
    private void playSystemBeep(AlertType alertType) {
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            int beepCount = alertType == AlertType.BUY_OPPORTUNITY ? 3 : 5;

            for (int i = 0; i < beepCount; i++) {
                toolkit.beep();
                if (i < beepCount - 1) {
                    Thread.sleep(300);
                }
            }
        } catch (Exception e) {
            logger.error("播放系统提示音失败: {}", e.getMessage());
        }
    }
    
    /**
     * 播放自定义音频文件
     */
    private void playCustomSound(AlertType alertType) {
        String audioFileName = alertType == AlertType.BUY_OPPORTUNITY ? "up.wav" : "down.wav";
        
        try {
            // 尝试多个可能的路径
            java.net.URL audioUrl = getClass().getResource("/audio/" + audioFileName);
            if (audioUrl == null) {
                audioUrl = getClass().getResource("/sounds/" + audioFileName);
            }
            
            if (audioUrl == null) {
                // 降级到系统蜂鸣
                playSystemBeep(alertType);
                return;
            }
            
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioUrl);
            Clip clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            clip.start();
            
            // 立即关闭AudioInputStream
            audioInputStream.close();
            
        } catch (Exception e) {
            logger.error("自定义音频播放失败: {}", e.getMessage());
            // 降级到系统蜂鸣
            playSystemBeep(alertType);
        }
    }

    /**
     * 强制播放音频方法 - 用于测试
     */
    public void forcePlayAudio(AlertType alertType) {
        String soundType = configService.getSoundType();
        
        switch (soundType.toUpperCase()) {
            case "SYSTEM":
                playSystemBeep(alertType);
                break;
            case "CUSTOM":
                playCustomSound(alertType);
                break;
            default:
                playSystemBeep(alertType);
        }
    }

    /**
     * 强制播放买入音频 - 用于测试
     */
    public void forcePlayBuyAudio() {
        forcePlayAudio(AlertType.BUY_OPPORTUNITY);
    }

    /**
     * 强制播放卖出音频 - 用于测试
     */
    public void forcePlaySellAudio() {
        forcePlayAudio(AlertType.SELL_OPPORTUNITY);
    }
    
    /**
     * 初始化检查
     */
    @PostConstruct
    public void init() {
        logger.info("AlertSoundService 初始化完成，音频类型: {}", configService.getSoundType());
    }
    
    /**
     * 告警类型枚举
     */
    public enum AlertType {
        BUY_OPPORTUNITY,
        SELL_OPPORTUNITY
    }
}
