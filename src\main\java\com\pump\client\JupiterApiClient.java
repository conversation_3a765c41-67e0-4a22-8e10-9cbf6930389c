package com.pump.client;

import com.pump.model.PriceData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.HttpRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.SocketTimeoutException;
import java.io.IOException;

/**
 * Jupiter API客户端
 * 负责从Jupiter获取PUMP价格数据
 * 使用Price API V3（最新版本）
 * 支持代理配置
 * 
 * <AUTHOR> Agent
 * @version 3.0
 * @since 2025-01-15
 */
@Component
public class JupiterApiClient {
    
    private static final Logger logger = LoggerFactory.getLogger(JupiterApiClient.class);
    
    @Value("${jupiter.api.base-url}")
    private String baseUrl;
    
    @Value("${jupiter.api.quote-url}")
    private String quoteUrl;
    
    @Value("${jupiter.api.timeout}")
    private int timeout;
    
    @Value("${proxy.enabled:false}")
    private boolean proxyEnabled;
    
    @Value("${proxy.host:127.0.0.1}")
    private String proxyHost;
    
    @Value("${proxy.port:7890}")
    private int proxyPort;
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    // PUMP代币地址（用户指定的正确地址）
    private static final String PUMP_TOKEN = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn";
    
    // 价格精度
    private static final int PRICE_SCALE = 6;
    
    public JupiterApiClient() {
        this.restTemplate = createRestTemplate();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 创建配置了代理的RestTemplate
     */
    private RestTemplate createRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(timeout);
        factory.setReadTimeout(timeout);
        
        // 添加连接池配置
        factory.setBufferRequestBody(false);
        
        // 配置代理
        if (proxyEnabled) {
            try {
                // 首先尝试SOCKS5代理
                Proxy proxy = new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(proxyHost, proxyPort));
                factory.setProxy(proxy);
                logger.info("已启用SOCKS5代理: {}:{}", proxyHost, proxyPort);
                
                // 测试代理连接
                testProxyConnection();
                
            } catch (Exception e) {
                logger.error("SOCKS5代理配置失败: {}", e.getMessage());
                logger.info("尝试HTTP代理");
                
                try {
                    // 备用方案：HTTP代理
                    Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyHost, proxyPort));
                    factory.setProxy(proxy);
                    logger.info("已启用HTTP代理: {}:{}", proxyHost, proxyPort);
                } catch (Exception e2) {
                    logger.error("HTTP代理配置也失败: {}", e2.getMessage());
                    logger.info("将使用直接连接");
                }
            }
        } else {
            logger.info("代理已禁用，使用直接连接");
        }
        
        RestTemplate template = new RestTemplate(factory);
        
        // 添加重试拦截器
        template.getInterceptors().add(new RetryInterceptor());
        
        return template;
    }

    /**
     * 测试代理连接
     */
    private void testProxyConnection() {
        try {
            String testUrl = "https://lite-api.jup.ag/health";
            restTemplate.getForObject(testUrl, String.class);
            logger.info("代理连接测试成功");
        } catch (Exception e) {
            logger.warn("代理连接测试失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取PUMP价格数据
     * 使用Price API V3（最新版本）
     * 
     * @return 价格数据
     */
    public PriceData getPumpPrice() {
        logger.debug("获取Jupiter PUMP价格数据 (Price API V3)");
        
        try {
            // 构建Price API V3 URL - 不需要vsToken参数
            String url = String.format("%s?ids=%s", baseUrl, PUMP_TOKEN);
            
            // 添加请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "PUMP-Monitor/1.0");
            headers.set("Accept", "application/json");
            HttpEntity<?> entity = new HttpEntity<>(headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.GET, entity, String.class);
            
            String responseBody = response.getBody();
            
            if (responseBody == null || responseBody.isEmpty()) {
                logger.error("Jupiter Price API V3返回空响应");
                return createErrorPriceData("API返回空响应");
            }
            
            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            // 检查是否包含价格数据 - V3 API直接以token地址为键
            if (jsonNode.has(PUMP_TOKEN)) {
                JsonNode tokenData = jsonNode.get(PUMP_TOKEN);
                
                // V3 API返回格式：直接获取usdPrice字段
                if (tokenData.has("usdPrice")) {
                    BigDecimal price = new BigDecimal(tokenData.get("usdPrice").asText());
                    
                    // 创建价格数据对象
                    PriceData resultData = new PriceData("Jupiter", price, price, price);
                    resultData.setSymbol("PUMP/USDT");
                    
                    // 记录额外信息
                    if (tokenData.has("blockId")) {
                        logger.debug("Jupiter价格数据获取成功 (V3): {}, 区块ID: {}", 
                            price, tokenData.get("blockId").asLong());
                    } else {
                        logger.debug("Jupiter价格数据获取成功 (V3): {}", price);
                    }
                    
                    return resultData;
                } else {
                    logger.error("Jupiter Price API V3返回格式错误，缺少usdPrice字段");
                    logger.debug("Token数据内容: {}", tokenData);
                    return createErrorPriceData("API返回格式错误，缺少usdPrice字段");
                }
                
            } else {
                logger.error("Jupiter Price API V3返回格式错误或无PUMP价格数据");
                logger.debug("API响应内容: {}", responseBody);
                return createErrorPriceData("API返回格式错误或无PUMP价格数据");
            }
            
        } catch (ResourceAccessException e) {
            logger.error("Jupiter API网络连接失败", e);
            return handleNetworkError(e);
        } catch (Exception e) {
            logger.error("获取Jupiter价格数据失败", e);
            return createErrorPriceData("获取价格数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理网络错误
     */
    private PriceData handleNetworkError(ResourceAccessException e) {
        String errorMessage;
        
        if (e.getCause() instanceof SocketTimeoutException) {
            errorMessage = "连接超时，请检查网络或增加超时时间";
            logger.warn("建议增加jupiter.api.timeout配置值");
        } else if (e.getMessage().contains("Connection refused")) {
            errorMessage = "连接被拒绝，请检查API地址或防火墙设置";
        } else if (e.getMessage().contains("UnknownHostException")) {
            errorMessage = "无法解析主机名，请检查DNS设置";
        } else {
            errorMessage = "网络连接失败: " + e.getMessage();
        }
        
        return createErrorPriceData(errorMessage);
    }
    
    /**
     * 获取交易报价 (用于计算大额交易价格)
     * 使用Swap API V1来获取精确的交易报价
     * 
     * @param amount 交易数量
     * @param isBuy 是否为买入操作
     * @return 实际交易价格
     */
    public BigDecimal getQuotePrice(BigDecimal amount, boolean isBuy) {
        logger.debug("获取Jupiter交易报价 (Swap API V1), 数量: {}, 买入: {}", amount, isBuy);
        
        try {
            // 构建报价API URL
            String inputMint, outputMint;
            BigDecimal inputAmount;
            
            if (isBuy) {
                // 买入PUMP：输入USDT，输出PUMP
                inputMint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"; // USDT
                outputMint = PUMP_TOKEN;
                inputAmount = amount; // USDT数量
            } else {
                // 卖出PUMP：输入PUMP，输出USDT
                inputMint = PUMP_TOKEN;
                outputMint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"; // USDT
                inputAmount = amount; // PUMP数量
            }
            
            // 转换为最小单位 (假设USDT和PUMP都是6位精度)
            BigDecimal amountInSmallestUnit = inputAmount.multiply(new BigDecimal("1000000"));
            
            String url = String.format("%s?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50", 
                                     quoteUrl, inputMint, outputMint, amountInSmallestUnit.toBigInteger());
            
            // 发送请求
            String response = restTemplate.getForObject(url, String.class);
            
            if (response == null || response.isEmpty()) {
                logger.warn("Jupiter Quote API返回空响应，使用基础价格");
                return null;
            }
            
            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(response);
            
            // 提取输入和输出数量
            if (jsonNode.has("inAmount") && jsonNode.has("outAmount")) {
                BigDecimal inAmount = new BigDecimal(jsonNode.get("inAmount").asText());
                BigDecimal outAmount = new BigDecimal(jsonNode.get("outAmount").asText());
                
                // 计算价格
                BigDecimal price;
                if (isBuy) {
                    // 买入：USDT输入量 / PUMP输出量 = USDT每个PUMP的价格
                    price = inAmount.divide(outAmount, PRICE_SCALE, RoundingMode.HALF_UP);
                } else {
                    // 卖出：USDT输出量 / PUMP输入量 = USDT每个PUMP的价格
                    price = outAmount.divide(inAmount, PRICE_SCALE, RoundingMode.HALF_UP);
                }
                
                // 记录价格影响
                if (jsonNode.has("priceImpactPct")) {
                    String priceImpact = jsonNode.get("priceImpactPct").asText();
                    logger.debug("Jupiter报价价格: {}, 价格影响: {}%", price, priceImpact);
                } else {
                    logger.debug("Jupiter报价价格: {}", price);
                }
                
                return price;
            }
            
            return null;
            
        } catch (Exception e) {
            logger.error("获取Jupiter报价失败", e);
            return null;
        }
    }
    
    /**
     * 检查API连接状态
     * 
     * @return 是否连接正常
     */
    public boolean isApiHealthy() {
        try {
            // 使用轻量级健康检查端点
            String healthUrl = "https://lite-api.jup.ag/health";
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "PUMP-Monitor/1.0");
            HttpEntity<?> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                healthUrl, HttpMethod.GET, entity, String.class);
            
            boolean isHealthy = response.getStatusCode().is2xxSuccessful();
            logger.debug("Jupiter API健康检查: {}", isHealthy ? "成功" : "失败");
            
            return isHealthy;
            
        } catch (Exception e) {
            logger.error("Jupiter API健康检查失败", e);
            return false;
        }
    }
    
    /**
     * 获取API状态信息
     */
    public String getApiStatus() {
        try {
            if (isApiHealthy()) {
                return "Jupiter API连接正常";
            } else {
                return "Jupiter API连接异常";
            }
        } catch (Exception e) {
            return "Jupiter API状态检查失败: " + e.getMessage();
        }
    }
    
    /**
     * 创建错误价格数据
     * 
     * @param errorMessage 错误信息
     * @return 错误价格数据
     */
    private PriceData createErrorPriceData(String errorMessage) {
        PriceData priceData = new PriceData();
        priceData.setExchange("Jupiter");
        priceData.setErrorMessage(errorMessage);
        priceData.setTimestamp(LocalDateTime.now());
        return priceData;
    }
    
    /**
     * 重试拦截器
     */
    private static class RetryInterceptor implements ClientHttpRequestInterceptor {
        private static final int MAX_RETRIES = 3;
        private static final long RETRY_DELAY = 1000;
        
        @Override
        public ClientHttpResponse intercept(
                HttpRequest request, 
                byte[] body, 
                ClientHttpRequestExecution execution) throws IOException {
            
            IOException lastException = null;
            
            for (int i = 0; i < MAX_RETRIES; i++) {
                try {
                    return execution.execute(request, body);
                } catch (IOException e) {
                    lastException = e;
                    if (i < MAX_RETRIES - 1) {
                        try {
                            Thread.sleep(RETRY_DELAY * (i + 1)); // 指数退避
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            throw new IOException("重试被中断", ie);
                        }
                    }
                }
            }
            
            throw lastException;
        }
    }
}
