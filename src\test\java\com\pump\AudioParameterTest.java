package com.pump;

import javax.sound.sampled.*;
import java.io.InputStream;
import java.io.BufferedInputStream;

/**
 * 音频参数诊断和调整测试
 * 专门测试和调整音频播放参数，解决听不到声音的问题
 */
public class AudioParameterTest {

    public static void main(String[] args) {
        System.out.println("🔧 ========== 音频参数诊断测试 ==========");
        System.out.println("📢 正在诊断音频播放参数问题...");
        System.out.println();

        // 1. 检查音频系统信息
        checkAudioSystemInfo();
        
        // 2. 测试不同的音频参数设置
        testDifferentAudioSettings();
        
        // 3. 测试音频控制参数
        testAudioControls();
        
        System.out.println("🔧 ========== 诊断测试完成 ==========");
    }

    /**
     * 检查音频系统信息
     */
    private static void checkAudioSystemInfo() {
        System.out.println("🔍 === 音频系统信息检查 ===");
        
        try {
            // 获取所有音频设备
            Mixer.Info[] mixerInfos = AudioSystem.getMixerInfo();
            System.out.println("📋 系统音频设备总数: " + mixerInfos.length);
            
            for (int i = 0; i < mixerInfos.length; i++) {
                Mixer.Info info = mixerInfos[i];
                System.out.println("\n🎧 设备 " + (i + 1) + ":");
                System.out.println("   名称: " + info.getName());
                System.out.println("   描述: " + info.getDescription());
                System.out.println("   供应商: " + info.getVendor());
                System.out.println("   版本: " + info.getVersion());
                
                try {
                    Mixer mixer = AudioSystem.getMixer(info);
                    
                    // 检查输出线路
                    Line.Info[] sourceLineInfos = mixer.getSourceLineInfo();
                    System.out.println("   输出线路数: " + sourceLineInfos.length);
                    
                    for (Line.Info lineInfo : sourceLineInfos) {
                        System.out.println("     - " + lineInfo.toString());
                        
                        // 如果是Clip类型，检查支持的格式
                        if (lineInfo instanceof DataLine.Info) {
                            DataLine.Info dataLineInfo = (DataLine.Info) lineInfo;
                            AudioFormat[] formats = dataLineInfo.getFormats();
                            System.out.println("       支持格式数: " + formats.length);
                            
                            // 显示前3个格式
                            for (int j = 0; j < Math.min(formats.length, 3); j++) {
                                System.out.println("         格式" + (j+1) + ": " + formats[j]);
                            }
                        }
                    }
                    
                } catch (Exception e) {
                    System.out.println("   ❌ 无法获取设备详细信息: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ 音频系统信息检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试不同的音频参数设置
     */
    private static void testDifferentAudioSettings() {
        System.out.println("\n🎛️ === 测试不同音频参数设置 ===");
        
        String fileName = "up.wav";
        
        // 测试1: 默认设备播放
        System.out.println("\n🔊 测试1: 使用默认音频设备");
        playWithDefaultDevice(fileName);
        
        sleep(2000);
        
        // 测试2: 遍历所有设备播放
        System.out.println("\n🔊 测试2: 遍历所有音频设备播放");
        playWithAllDevices(fileName);
        
        sleep(2000);
        
        // 测试3: 调整音频格式播放
        System.out.println("\n🔊 测试3: 调整音频格式播放");
        playWithDifferentFormats(fileName);
    }

    /**
     * 使用默认设备播放，但调整各种参数
     */
    private static void playWithDefaultDevice(String fileName) {
        InputStream rawStream = null;
        InputStream audioStream = null;
        AudioInputStream audioInputStream = null;
        Clip clip = null;
        
        try {
            // 加载音频
            String resourcePath = "/audio/" + fileName;
            rawStream = AudioParameterTest.class.getResourceAsStream(resourcePath);
            if (rawStream == null) {
                System.out.println("❌ 找不到音频文件: " + resourcePath);
                return;
            }
            
            audioStream = new BufferedInputStream(rawStream, 8192);
            audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            
            // 创建Clip
            clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            
            System.out.println("📊 原始音频格式: " + audioInputStream.getFormat());
            System.out.println("⏱️ 音频时长: " + (clip.getMicrosecondLength() / 1000) + "ms");
            
            // 调整所有可能的音频控制参数
            adjustAllAudioControls(clip);
            
            // 播放音频
            System.out.println("🎵 开始播放 (默认设备) - 请注意听！");
            clip.start();
            
            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(50);
            }
            
            System.out.println("✅ 默认设备播放完成");
            
        } catch (Exception e) {
            System.out.println("❌ 默认设备播放失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            closeResources(clip, audioInputStream, audioStream, rawStream);
        }
    }

    /**
     * 在所有设备上尝试播放
     */
    private static void playWithAllDevices(String fileName) {
        Mixer.Info[] mixerInfos = AudioSystem.getMixerInfo();
        
        for (int i = 0; i < mixerInfos.length; i++) {
            Mixer.Info info = mixerInfos[i];
            System.out.println("\n🎧 尝试设备 " + (i + 1) + ": " + info.getName());
            
            try {
                Mixer mixer = AudioSystem.getMixer(info);
                Line.Info[] sourceLineInfos = mixer.getSourceLineInfo();
                
                if (sourceLineInfos.length == 0) {
                    System.out.println("   ⚠️ 设备不支持音频输出");
                    continue;
                }
                
                // 尝试在这个设备上播放
                playOnSpecificDevice(fileName, mixer, info.getName());
                
            } catch (Exception e) {
                System.out.println("   ❌ 设备播放失败: " + e.getMessage());
            }
            
            sleep(1000); // 设备间间隔
        }
    }

    /**
     * 在指定设备上播放
     */
    private static void playOnSpecificDevice(String fileName, Mixer mixer, String deviceName) {
        InputStream rawStream = null;
        InputStream audioStream = null;
        AudioInputStream audioInputStream = null;
        Clip clip = null;
        
        try {
            // 加载音频
            String resourcePath = "/audio/" + fileName;
            rawStream = AudioParameterTest.class.getResourceAsStream(resourcePath);
            if (rawStream == null) return;
            
            audioStream = new BufferedInputStream(rawStream, 8192);
            audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            
            // 尝试在指定设备上创建Clip
            try {
                clip = (Clip) mixer.getLine(new DataLine.Info(Clip.class, audioInputStream.getFormat()));
                System.out.println("   ✅ 在指定设备上创建Clip成功");
            } catch (Exception e) {
                System.out.println("   ⚠️ 指定设备创建失败，使用默认设备: " + e.getMessage());
                clip = AudioSystem.getClip();
            }
            
            clip.open(audioInputStream);
            
            // 调整音频控制参数
            adjustAllAudioControls(clip);
            
            // 播放
            System.out.println("   🎵 在设备 [" + deviceName + "] 上播放 - 请注意听！");
            clip.start();
            
            // 等待播放完成
            while (clip.isRunning()) {
                Thread.sleep(50);
            }
            
            System.out.println("   ✅ 设备播放完成");
            
        } catch (Exception e) {
            System.out.println("   ❌ 设备播放异常: " + e.getMessage());
        } finally {
            closeResources(clip, audioInputStream, audioStream, rawStream);
        }
    }

    /**
     * 调整所有音频控制参数
     */
    private static void adjustAllAudioControls(Clip clip) {
        System.out.println("🎛️ 调整音频控制参数:");
        
        Control[] controls = clip.getControls();
        System.out.println("   可用控制数: " + controls.length);
        
        for (Control control : controls) {
            System.out.println("   控制类型: " + control.getType());
            
            try {
                if (control instanceof FloatControl) {
                    FloatControl floatControl = (FloatControl) control;
                    System.out.println("     当前值: " + floatControl.getValue());
                    System.out.println("     范围: " + floatControl.getMinimum() + " ~ " + floatControl.getMaximum());
                    
                    // 如果是音量控制，设置到最大
                    if (control.getType() == FloatControl.Type.MASTER_GAIN ||
                        control.getType() == FloatControl.Type.VOLUME) {
                        
                        float maxValue = floatControl.getMaximum();
                        floatControl.setValue(maxValue);
                        System.out.println("     ✅ 设置到最大值: " + maxValue);
                    }
                    
                } else if (control instanceof BooleanControl) {
                    BooleanControl booleanControl = (BooleanControl) control;
                    System.out.println("     当前状态: " + booleanControl.getValue());
                    
                    // 如果是静音控制，确保不静音
                    if (control.getType() == BooleanControl.Type.MUTE) {
                        booleanControl.setValue(false);
                        System.out.println("     ✅ 取消静音");
                    }
                }
                
            } catch (Exception e) {
                System.out.println("     ⚠️ 控制调整失败: " + e.getMessage());
            }
        }
    }

    /**
     * 测试音频控制参数
     */
    private static void testAudioControls() {
        System.out.println("\n🎚️ === 音频控制参数测试 ===");
        
        try {
            // 获取默认的音频设备信息
            Mixer.Info[] mixerInfos = AudioSystem.getMixerInfo();
            
            for (Mixer.Info info : mixerInfos) {
                if (info.getName().contains("扬声器") || info.getName().contains("Speaker") || 
                    info.getName().contains("主声音驱动程序")) {
                    
                    System.out.println("🔍 检查设备: " + info.getName());
                    
                    try {
                        Mixer mixer = AudioSystem.getMixer(info);
                        Line.Info[] lineInfos = mixer.getSourceLineInfo();
                        
                        for (Line.Info lineInfo : lineInfos) {
                            if (lineInfo instanceof DataLine.Info) {
                                System.out.println("   线路: " + lineInfo);
                                
                                // 尝试打开线路并检查控制
                                try {
                                    Line line = mixer.getLine(lineInfo);
                                    if (line instanceof DataLine) {
                                        line.open();
                                        
                                        Control[] controls = line.getControls();
                                        System.out.println("     控制数量: " + controls.length);
                                        
                                        for (Control control : controls) {
                                            System.out.println("       - " + control.getType() + ": " + control);
                                        }
                                        
                                        line.close();
                                    }
                                } catch (Exception e) {
                                    System.out.println("     ⚠️ 线路检查失败: " + e.getMessage());
                                }
                            }
                        }
                        
                    } catch (Exception e) {
                        System.out.println("   ❌ 设备检查失败: " + e.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ 音频控制参数测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试不同音频格式
     */
    private static void playWithDifferentFormats(String fileName) {
        // 这里可以添加格式转换测试
        System.out.println("📊 音频格式测试 (暂时跳过，使用原始格式)");
    }

    /**
     * 关闭资源
     */
    private static void closeResources(Clip clip, AudioInputStream audioInputStream, 
                                     InputStream audioStream, InputStream rawStream) {
        try {
            if (clip != null) clip.close();
            if (audioInputStream != null) audioInputStream.close();
            if (audioStream != null) audioStream.close();
            if (rawStream != null && rawStream != audioStream) rawStream.close();
        } catch (Exception e) {
            // 忽略清理异常
        }
    }

    /**
     * 线程睡眠
     */
    private static void sleep(int milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
