package com.pump;

import javax.sound.sampled.*;
import java.awt.Toolkit;
import java.io.InputStream;
import java.io.BufferedInputStream;

/**
 * 音频播放测试实例
 * 可以在IDE中直接运行，验证音频播放功能
 * 
 * 使用方法：
 * 1. 在IDE中右键点击这个类
 * 2. 选择 "Run AudioPlaybackTest.main()"
 * 3. 观察控制台输出并注意听声音
 */
public class AudioPlaybackTest {

    public static void main(String[] args) {
        System.out.println("🎵 ========== 音频播放测试开始 ==========");
        System.out.println("📢 请注意听声音！");
        System.out.println();

        // 测试1: 系统蜂鸣测试
        testSystemBeep();
        
        // 等待3秒
        sleep(3000);
        
        // 测试2: 播放up.wav
        testWavFile("up.wav");
        
        // 等待3秒
        sleep(3000);
        
        // 测试3: 播放down.wav
        testWavFile("down.wav");
        
        // 等待3秒
        sleep(3000);
        
        // 测试4: 强力系统蜂鸣
        testPowerfulSystemBeep();
        
        System.out.println();
        System.out.println("🎵 ========== 音频播放测试完成 ==========");
        System.out.println("📋 如果您听到了声音，说明音频功能正常");
        System.out.println("📋 如果没有听到声音，可能是系统音频设置问题");
    }

    /**
     * 测试系统蜂鸣
     */
    private static void testSystemBeep() {
        System.out.println("📢 === 测试1: 系统蜂鸣 ===");
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            
            System.out.println("🔊 播放5次系统蜂鸣，请注意听...");
            for (int i = 1; i <= 5; i++) {
                System.out.println("🔔 第 " + i + " 次蜂鸣");
                toolkit.beep();
                Thread.sleep(500);
            }
            
            System.out.println("✅ 系统蜂鸣测试完成");
            
        } catch (Exception e) {
            System.out.println("❌ 系统蜂鸣测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试WAV文件播放
     */
    private static void testWavFile(String fileName) {
        System.out.println("\n🎵 === 测试WAV播放: " + fileName + " ===");
        
        InputStream rawStream = null;
        InputStream audioStream = null;
        AudioInputStream audioInputStream = null;
        Clip clip = null;
        
        try {
            // 1. 加载音频资源
            System.out.println("🔍 正在加载音频文件: " + fileName);
            String resourcePath = "/audio/" + fileName;
            rawStream = AudioPlaybackTest.class.getResourceAsStream(resourcePath);
            
            if (rawStream == null) {
                System.out.println("❌ 无法找到音频文件: " + resourcePath);
                return;
            }
            System.out.println("✅ 音频文件加载成功");

            // 2. 处理音频流
            System.out.println("🔧 正在处理音频流...");
            audioStream = rawStream.markSupported() ? 
                rawStream : new BufferedInputStream(rawStream, 8192);
            System.out.println("✅ 音频流处理完成，支持mark/reset: " + audioStream.markSupported());

            // 3. 创建AudioInputStream
            System.out.println("🎧 正在创建AudioInputStream...");
            audioInputStream = AudioSystem.getAudioInputStream(audioStream);
            System.out.println("✅ AudioInputStream创建成功");
            System.out.println("📊 音频格式: " + audioInputStream.getFormat());

            // 4. 获取音频设备信息
            System.out.println("🔍 正在检测音频设备...");
            Mixer.Info[] mixerInfos = AudioSystem.getMixerInfo();
            System.out.println("📋 发现 " + mixerInfos.length + " 个音频设备:");
            for (int i = 0; i < Math.min(mixerInfos.length, 3); i++) {
                System.out.println("   " + (i+1) + ". " + mixerInfos[i].getName());
            }

            // 5. 创建音频剪辑
            System.out.println("🎬 正在创建音频剪辑...");
            clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            
            long durationMicros = clip.getMicrosecondLength();
            long durationMillis = durationMicros / 1000;
            System.out.println("✅ 音频剪辑创建成功，时长: " + durationMillis + "ms");

            // 6. 设置音量到最大
            if (clip.isControlSupported(FloatControl.Type.MASTER_GAIN)) {
                FloatControl gainControl = (FloatControl) clip.getControl(FloatControl.Type.MASTER_GAIN);
                gainControl.setValue(gainControl.getMaximum());
                System.out.println("🔊 音量设置到最大: " + gainControl.getValue() + " dB");
            } else {
                System.out.println("⚠️ 不支持音量控制");
            }

            // 7. 播放音频
            System.out.println("🎵 开始播放音频: " + fileName);
            System.out.println("📢 请注意听声音！");
            clip.start();

            // 8. 等待播放完成
            System.out.println("⏳ 等待音频播放完成...");
            while (clip.isRunning()) {
                Thread.sleep(100);
            }

            System.out.println("✅ 音频播放完成: " + fileName);

        } catch (Exception e) {
            System.out.println("❌ 音频播放失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 清理资源
            try {
                if (clip != null) {
                    clip.close();
                    System.out.println("🧹 音频剪辑已关闭");
                }
                if (audioInputStream != null) {
                    audioInputStream.close();
                    System.out.println("🧹 AudioInputStream已关闭");
                }
                if (audioStream != null) {
                    audioStream.close();
                    System.out.println("🧹 音频流已关闭");
                }
                if (rawStream != null && rawStream != audioStream) {
                    rawStream.close();
                    System.out.println("🧹 原始流已关闭");
                }
            } catch (Exception e) {
                System.out.println("⚠️ 资源清理时出现异常: " + e.getMessage());
            }
        }
    }

    /**
     * 强力系统蜂鸣测试
     */
    private static void testPowerfulSystemBeep() {
        System.out.println("\n📢 === 测试4: 强力系统蜂鸣 ===");
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            
            System.out.println("🔊 播放强力连续蜂鸣，请注意听...");
            
            // 连续播放3轮，每轮10次快速蜂鸣
            for (int round = 1; round <= 3; round++) {
                System.out.println("🔔 第 " + round + " 轮强力蜂鸣 (10次连续)");
                
                for (int i = 0; i < 10; i++) {
                    System.out.print("🔊 ");
                    toolkit.beep();
                    Thread.sleep(100); // 快速连续蜂鸣
                }
                
                System.out.println("\n⏳ 等待1秒...");
                Thread.sleep(1000);
            }
            
            System.out.println("✅ 强力系统蜂鸣测试完成");
            
        } catch (Exception e) {
            System.out.println("❌ 强力系统蜂鸣测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 线程睡眠工具方法
     */
    private static void sleep(int milliseconds) {
        try {
            System.out.println("⏳ 等待 " + (milliseconds/1000) + " 秒...");
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
