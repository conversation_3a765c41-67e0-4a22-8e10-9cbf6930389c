# 📦 PUMP监控系统JAR包部署配置说明

## 🎯 配置文件加载机制

### **配置文件优先级**
1. **外部配置文件** (最高优先级) ⭐
   - 位置：`./pump-config.json` (JAR包同目录)
   - 用途：生产环境自定义配置
   - 特点：可热重载，无需重新打包

2. **硬编码默认配置** (备用)
   - 位置：代码内置默认值
   - 用途：确保系统在任何情况下都能启动
   - 特点：保守设置，适合首次部署

## 🚀 部署步骤

### **步骤1：打包JAR文件**
```bash
# 使用Maven打包
mvn clean package

# 生成的JAR文件位置
target/pump-monitor-1.0.jar
```

### **步骤2：准备部署目录**
```
deployment/
├── pump-monitor-1.0.jar     # 主程序
├── pump-config.json         # 外部配置文件 (可选)
└── logs/                    # 日志目录 (自动创建)
```

### **步骤3：配置文件处理**

#### **选择A：使用默认配置**
```bash
# 直接运行，使用硬编码的默认配置
java -jar pump-monitor-1.0.jar

# 默认配置参数：
# - 买入阈值: $30.00 (保守设置)
# - 卖出阈值: $30.00 (保守设置)
# - 监控间隔: 2000ms
# - 音频类型: CUSTOM
# - 代理: 禁用
```

#### **选择B：使用自定义配置** ⭐ **(推荐)**
```bash
# 1. 复制配置文件到JAR包同目录
cp pump-config.json ./deployment/

# 2. 根据需要修改配置
vim ./deployment/pump-config.json

# 3. 运行程序
cd deployment
java -jar pump-monitor-1.0.jar
```

## ⚙️ 配置文件示例

### **生产环境配置** (`./pump-config.json`)
```json
{
  "monitor": {
    "interval": 1000,
    "amount": 1000000,
    "comment": "生产环境：1秒监控间隔"
  },
  "alert": {
    "enabled": true,
    "buyThreshold": 2.00,
    "sellThreshold": 2.00,
    "soundType": "CUSTOM",
    "comment": "生产环境：低阈值，高敏感度"
  },
  "api": {
    "timeout": 30000,
    "retryDelay": 2000,
    "comment": "生产环境：标准超时设置"
  },
  "proxy": {
    "enabled": true,
    "host": "127.0.0.1",
    "port": 7890,
    "type": "SOCKS",
    "comment": "生产环境：启用代理"
  }
}
```

### **测试环境配置**
```json
{
  "monitor": {
    "interval": 5000,
    "amount": 1000000,
    "comment": "测试环境：5秒监控间隔"
  },
  "alert": {
    "enabled": true,
    "buyThreshold": 50.00,
    "sellThreshold": 50.00,
    "soundType": "SYSTEM",
    "comment": "测试环境：高阈值，减少干扰"
  },
  "proxy": {
    "enabled": false,
    "comment": "测试环境：禁用代理"
  }
}
```

## 🔄 配置热重载

### **运行时修改配置**
```bash
# 1. 修改外部配置文件
vim pump-config.json

# 2. 系统自动检测并重新加载
# 无需重启JAR程序！
```

### **配置重载日志**
```
[INFO] 检测到配置文件更新，已重新加载
[INFO] === PUMP系统配置 ===
[INFO] 监控间隔: 1000ms
[INFO] 买入阈值: $2.00
[INFO] 卖出阈值: $2.00
[INFO] ==================
```

## 📁 部署目录结构

### **推荐的生产部署结构**
```
/opt/pump-monitor/
├── pump-monitor-1.0.jar     # 主程序
├── pump-config.json         # 生产配置
├── start.sh                 # 启动脚本
├── stop.sh                  # 停止脚本
├── logs/                    # 日志目录
│   ├── pump-monitor.log
│   └── error.log
└── backup/                  # 配置备份
    └── pump-config.json.bak
```

### **启动脚本示例** (`start.sh`)
```bash
#!/bin/bash
cd /opt/pump-monitor
nohup java -jar pump-monitor-1.0.jar > logs/console.log 2>&1 &
echo $! > pump-monitor.pid
echo "PUMP监控系统已启动"
```

## 🛠️ 故障排除

### **配置文件不生效**
1. **检查文件位置**：确保 `pump-config.json` 在JAR包同目录
2. **检查文件格式**：确保JSON格式正确
3. **查看启动日志**：确认加载了哪个配置文件

### **系统启动失败**
1. **检查默认配置**：确保JAR包内有默认配置文件
2. **检查Java版本**：确保Java 8+
3. **检查依赖**：确保所有依赖都已打包

### **配置热重载不工作**
1. **检查文件权限**：确保程序有读取权限
2. **检查文件修改时间**：系统基于文件修改时间检测更新
3. **查看日志**：检查是否有重载相关的日志信息

## 💡 最佳实践

### **配置管理**
- ✅ **版本控制**：将配置文件纳入版本控制
- ✅ **环境分离**：不同环境使用不同配置
- ✅ **备份配置**：定期备份生产配置
- ✅ **文档更新**：配置变更时更新文档

### **部署安全**
- ✅ **权限控制**：限制配置文件的读写权限
- ✅ **敏感信息**：避免在配置文件中存储敏感信息
- ✅ **监控告警**：监控配置文件的异常变更

## 🎉 总结

JAR包部署时的配置文件处理非常灵活：
- ✅ **外部配置优先**：支持生产环境自定义
- ✅ **内部配置备用**：确保系统正常启动
- ✅ **热重载支持**：运行时动态调整配置
- ✅ **多环境支持**：适应不同部署场景

这种设计既保证了系统的稳定性，又提供了配置的灵活性！
